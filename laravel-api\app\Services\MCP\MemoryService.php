<?php

namespace App\Services\MCP;

use App\Models\ChatHistory;
use App\Models\WebsocketSession;
use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;

class MemoryService extends BaseService
{
    /**
     * Memory types and their configurations
     */
    protected array $memoryTypes = [
        'short_term' => [
            'retention_minutes' => 60,
            'max_entries' => 50,
            'compression_threshold' => 20,
        ],
        'working' => [
            'retention_minutes' => 30,
            'max_entries' => 20,
            'compression_threshold' => 10,
        ],
        'long_term' => [
            'retention_days' => 30,
            'max_entries' => 1000,
            'compression_threshold' => 100,
        ],
        'episodic' => [
            'retention_days' => 7,
            'max_entries' => 200,
            'compression_threshold' => 50,
        ],
        'semantic' => [
            'retention_days' => 90,
            'max_entries' => 500,
            'compression_threshold' => 100,
        ],
    ];

    /**
     * Context importance levels
     */
    protected array $importanceLevels = [
        'critical' => 1.0,
        'high' => 0.8,
        'medium' => 0.6,
        'low' => 0.4,
        'minimal' => 0.2,
    ];

    /**
     * Active memory sessions
     */
    protected array $activeSessions = [];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'memory_service';
    }

    /**
     * Store memory entry
     */
    public function storeMemory(string $sessionId, array $memoryData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('store_memory', function () use ($sessionId, $memoryData) {
            // Validate session
            $session = $this->validateSession($sessionId);

            // Determine memory type and importance
            $memoryType = $memoryData['memory_type'] ?? $this->determineMemoryType($memoryData);
            $importance = $memoryData['importance'] ?? $this->calculateImportance($memoryData);

            // Store in appropriate memory store
            $memoryEntry = $this->storeInMemoryStore($sessionId, $memoryType, $memoryData, $importance);

            // Update session context
            $this->updateSessionContext($sessionId, $memoryEntry);

            // Trigger memory consolidation if needed
            $this->triggerMemoryConsolidation($sessionId, $memoryType);

            return [
                'memory_id' => $memoryEntry['id'],
                'session_id' => $sessionId,
                'memory_type' => $memoryType,
                'importance' => $importance,
                'stored_at' => $memoryEntry['stored_at'],
                'status' => 'stored',
            ];
        }, [
            'session_id' => $sessionId,
            'memory_type' => $memoryData['memory_type'] ?? 'auto',
            'content_size' => strlen(json_encode($memoryData)),
        ]);
    }

    /**
     * Retrieve memories for session
     */
    public function retrieveMemories(string $sessionId, array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('retrieve_memories', function () use ($sessionId, $options) {
            // Validate session
            $session = $this->validateSession($sessionId);

            // Parse retrieval options
            $memoryTypes = $options['memory_types'] ?? array_keys($this->memoryTypes);
            $limit = $options['limit'] ?? 20;
            $minImportance = $options['min_importance'] ?? 0.1;
            $timeRange = $options['time_range'] ?? null;
            $query = $options['query'] ?? null;

            // Retrieve from different memory stores
            $memories = [];
            foreach ($memoryTypes as $memoryType) {
                $typeMemories = $this->retrieveFromMemoryStore(
                    $sessionId,
                    $memoryType,
                    $limit,
                    $minImportance,
                    $timeRange,
                    $query
                );
                $memories = array_merge($memories, $typeMemories);
            }

            // Sort by relevance and importance
            $sortedMemories = $this->sortMemoriesByRelevance($memories, $query);

            // Apply final limit
            $finalMemories = array_slice($sortedMemories, 0, $limit);

            // Update access patterns
            $this->updateMemoryAccessPatterns($finalMemories);

            return [
                'session_id' => $sessionId,
                'memories' => $finalMemories,
                'total_found' => count($memories),
                'returned_count' => count($finalMemories),
                'memory_types_searched' => $memoryTypes,
                'retrieval_metadata' => [
                    'min_importance' => $minImportance,
                    'time_range' => $timeRange,
                    'query_provided' => !empty($query),
                ],
            ];
        }, [
            'session_id' => $sessionId,
            'memory_types_count' => count($options['memory_types'] ?? []),
            'limit' => $options['limit'] ?? 20,
        ]);
    }

    /**
     * Get session context
     */
    public function getSessionContext(string $sessionId): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('get_session_context', function () use ($sessionId) {
            // Validate session
            $session = $this->validateSession($sessionId);

            // Get current context from cache
            $cacheKey = "session_context:{$sessionId}";
            $context = $this->getFromCache($cacheKey, []);

            if (empty($context)) {
                // Build context from memories
                $context = $this->buildSessionContext($sessionId);
                $this->putToCache($cacheKey, $context, 300); // 5 minutes TTL
            }

            // Enrich context with real-time data
            $enrichedContext = $this->enrichSessionContext($sessionId, $context);

            return [
                'session_id' => $sessionId,
                'context' => $enrichedContext,
                'context_summary' => $this->generateContextSummary($enrichedContext),
                'last_updated' => now()->toISOString(),
            ];
        }, [
            'session_id' => $sessionId,
        ]);
    }

    /**
     * Update session context
     */
    public function updateSessionContext(string $sessionId, array $contextUpdate): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('update_session_context', function () use ($sessionId, $contextUpdate) {
            // Validate session
            $session = $this->validateSession($sessionId);

            // Get current context
            $cacheKey = "session_context:{$sessionId}";
            $currentContext = $this->getFromCache($cacheKey, []);

            // Merge updates
            $updatedContext = $this->mergeContextUpdates($currentContext, $contextUpdate);

            // Store updated context
            $this->putToCache($cacheKey, $updatedContext, 300);

            // Log context update
            $this->logActivity('Session context updated', [
                'session_id' => $sessionId,
                'update_keys' => array_keys($contextUpdate),
                'context_size' => strlen(json_encode($updatedContext)),
            ]);

            return [
                'session_id' => $sessionId,
                'status' => 'updated',
                'updated_fields' => array_keys($contextUpdate),
                'context_size' => strlen(json_encode($updatedContext)),
            ];
        }, [
            'session_id' => $sessionId,
            'update_fields' => array_keys($contextUpdate),
        ]);
    }

    /**
     * Clear session memory
     */
    public function clearSessionMemory(string $sessionId, array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_memory');

        return $this->executeWithTracking('clear_session_memory', function () use ($sessionId, $options) {
            // Validate session
            $session = $this->validateSession($sessionId);

            $memoryTypes = $options['memory_types'] ?? array_keys($this->memoryTypes);
            $olderThan = $options['older_than'] ?? null;
            $belowImportance = $options['below_importance'] ?? null;

            $clearedCount = 0;

            foreach ($memoryTypes as $memoryType) {
                $cleared = $this->clearMemoryStore($sessionId, $memoryType, $olderThan, $belowImportance);
                $clearedCount += $cleared;
            }

            // Clear session context cache
            $cacheKey = "session_context:{$sessionId}";
            $this->forgetFromCache($cacheKey);

            $this->logActivity('Session memory cleared', [
                'session_id' => $sessionId,
                'memory_types' => $memoryTypes,
                'cleared_count' => $clearedCount,
            ]);

            return [
                'session_id' => $sessionId,
                'status' => 'cleared',
                'cleared_count' => $clearedCount,
                'memory_types_cleared' => $memoryTypes,
            ];
        }, [
            'session_id' => $sessionId,
            'memory_types_count' => count($options['memory_types'] ?? []),
        ]);
    }

    /**
     * Get memory analytics
     */
    public function getMemoryAnalytics(array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('get_memory_analytics', function () use ($options) {
            $tenantId = $this->getCurrentTenantId();
            $dateFrom = $options['date_from'] ?? now()->subDays(7);
            $dateTo = $options['date_to'] ?? now();

            // Get session statistics
            $totalSessions = WebsocketSession::where('tenant_id', $tenantId)
                                           ->whereBetween('created_at', [$dateFrom, $dateTo])
                                           ->count();

            $activeSessions = WebsocketSession::where('tenant_id', $tenantId)
                                            ->where('is_active', true)
                                            ->count();

            // Get memory usage by type
            $memoryUsageByType = [];
            foreach (array_keys($this->memoryTypes) as $memoryType) {
                $usage = $this->getMemoryTypeUsage($tenantId, $memoryType, $dateFrom, $dateTo);
                $memoryUsageByType[$memoryType] = $usage;
            }

            // Get conversation statistics
            $totalConversations = ChatHistory::where('tenant_id', $tenantId)
                                           ->whereBetween('created_at', [$dateFrom, $dateTo])
                                           ->count();

            $avgConversationLength = ChatHistory::where('tenant_id', $tenantId)
                                              ->whereBetween('created_at', [$dateFrom, $dateTo])
                                              ->avg('message_count') ?? 0;

            return [
                'tenant_id' => $tenantId,
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo,
                ],
                'session_statistics' => [
                    'total_sessions' => $totalSessions,
                    'active_sessions' => $activeSessions,
                    'session_activity_rate' => $totalSessions > 0 ? round($activeSessions / $totalSessions * 100, 2) : 0,
                ],
                'memory_usage_by_type' => $memoryUsageByType,
                'conversation_statistics' => [
                    'total_conversations' => $totalConversations,
                    'average_conversation_length' => round($avgConversationLength, 2),
                ],
                'memory_types_available' => array_keys($this->memoryTypes),
                'analysis_timestamp' => now()->toISOString(),
            ];
        }, [
            'date_range_days' => $options['date_from'] ? now()->diffInDays($options['date_from']) : 7,
        ]);
    }

    /**
     * Consolidate memories
     */
    public function consolidateMemories(string $sessionId, array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('consolidate_memories', function () use ($sessionId, $options) {
            // Validate session
            $session = $this->validateSession($sessionId);

            $memoryType = $options['memory_type'] ?? 'short_term';
            $consolidationStrategy = $options['strategy'] ?? 'importance_based';

            // Get memories to consolidate
            $memories = $this->getMemoriesForConsolidation($sessionId, $memoryType);

            if (empty($memories)) {
                return [
                    'session_id' => $sessionId,
                    'status' => 'no_consolidation_needed',
                    'memory_type' => $memoryType,
                ];
            }

            // Apply consolidation strategy
            $consolidatedMemories = $this->applyConsolidationStrategy($memories, $consolidationStrategy);

            // Store consolidated memories
            $storedCount = $this->storeConsolidatedMemories($sessionId, $consolidatedMemories);

            // Remove original memories
            $removedCount = $this->removeConsolidatedMemories($sessionId, $memories);

            $this->logActivity('Memory consolidation completed', [
                'session_id' => $sessionId,
                'memory_type' => $memoryType,
                'strategy' => $consolidationStrategy,
                'original_count' => count($memories),
                'consolidated_count' => count($consolidatedMemories),
                'stored_count' => $storedCount,
                'removed_count' => $removedCount,
            ]);

            return [
                'session_id' => $sessionId,
                'status' => 'consolidated',
                'memory_type' => $memoryType,
                'strategy' => $consolidationStrategy,
                'original_memories' => count($memories),
                'consolidated_memories' => count($consolidatedMemories),
                'compression_ratio' => count($memories) > 0 ? round(count($consolidatedMemories) / count($memories), 2) : 0,
            ];
        }, [
            'session_id' => $sessionId,
            'memory_type' => $options['memory_type'] ?? 'short_term',
            'strategy' => $options['strategy'] ?? 'importance_based',
        ]);
    }

    /**
     * Validate session
     */
    protected function validateSession(string $sessionId): WebsocketSession
    {
        $session = WebsocketSession::where('tenant_id', $this->getCurrentTenantId())
                                  ->where('session_id', $sessionId)
                                  ->first();

        if (!$session) {
            throw new \InvalidArgumentException("Session not found: {$sessionId}");
        }

        return $session;
    }

    /**
     * Determine memory type based on content
     */
    protected function determineMemoryType(array $memoryData): string
    {
        $content = $memoryData['content'] ?? '';
        $messageType = $memoryData['message_type'] ?? 'user';
        $importance = $memoryData['importance'] ?? 0.5;

        // Determine based on content characteristics
        if ($importance >= 0.8) {
            return 'long_term';
        }

        if ($messageType === 'system' || str_contains($content, 'error')) {
            return 'episodic';
        }

        if (strlen($content) > 500 || $this->containsImportantKeywords($content)) {
            return 'semantic';
        }

        if ($this->isRecentInteraction($memoryData)) {
            return 'working';
        }

        return 'short_term';
    }

    /**
     * Calculate importance score
     */
    protected function calculateImportance(array $memoryData): float
    {
        $score = 0.5; // Base score

        $content = $memoryData['content'] ?? '';
        $messageType = $memoryData['message_type'] ?? 'user';

        // Boost for certain message types
        if ($messageType === 'system') {
            $score += 0.2;
        } elseif ($messageType === 'assistant') {
            $score += 0.1;
        }

        // Boost for length (longer messages often more important)
        $contentLength = strlen($content);
        if ($contentLength > 200) {
            $score += 0.1;
        }
        if ($contentLength > 500) {
            $score += 0.1;
        }

        // Boost for important keywords
        if ($this->containsImportantKeywords($content)) {
            $score += 0.2;
        }

        // Boost for questions
        if (str_contains($content, '?')) {
            $score += 0.1;
        }

        // Boost for errors or issues
        if (str_contains(strtolower($content), 'error') || str_contains(strtolower($content), 'problem')) {
            $score += 0.3;
        }

        return min(1.0, $score);
    }

    /**
     * Store in memory store
     */
    protected function storeInMemoryStore(string $sessionId, string $memoryType, array $memoryData, float $importance): array
    {
        $cacheKey = "memory:{$memoryType}:{$sessionId}";
        $memories = $this->getFromCache($cacheKey, []);

        $memoryEntry = [
            'id' => uniqid('mem_'),
            'session_id' => $sessionId,
            'memory_type' => $memoryType,
            'content' => $memoryData['content'] ?? '',
            'message_type' => $memoryData['message_type'] ?? 'user',
            'importance' => $importance,
            'metadata' => $memoryData['metadata'] ?? [],
            'stored_at' => now()->toISOString(),
            'access_count' => 0,
            'last_accessed' => null,
        ];

        $memories[] = $memoryEntry;

        // Apply memory type limits
        $config = $this->memoryTypes[$memoryType];
        if (count($memories) > $config['max_entries']) {
            // Remove least important memories
            usort($memories, fn($a, $b) => $b['importance'] <=> $a['importance']);
            $memories = array_slice($memories, 0, $config['max_entries']);
        }

        // Store back to cache
        $ttl = $this->getMemoryTypeTTL($memoryType);
        $this->putToCache($cacheKey, $memories, $ttl);

        return $memoryEntry;
    }

    /**
     * Retrieve from memory store
     */
    protected function retrieveFromMemoryStore(string $sessionId, string $memoryType, int $limit, float $minImportance, ?array $timeRange, ?string $query): array
    {
        $cacheKey = "memory:{$memoryType}:{$sessionId}";
        $memories = $this->getFromCache($cacheKey, []);

        // Filter by importance
        $memories = array_filter($memories, fn($memory) => $memory['importance'] >= $minImportance);

        // Filter by time range
        if ($timeRange) {
            $memories = array_filter($memories, function ($memory) use ($timeRange) {
                $storedAt = \Carbon\Carbon::parse($memory['stored_at']);
                return $storedAt->between($timeRange['from'], $timeRange['to']);
            });
        }

        // Filter by query
        if ($query) {
            $memories = array_filter($memories, function ($memory) use ($query) {
                return str_contains(strtolower($memory['content']), strtolower($query));
            });
        }

        // Sort by importance and recency
        usort($memories, function ($a, $b) {
            $importanceCompare = $b['importance'] <=> $a['importance'];
            if ($importanceCompare !== 0) {
                return $importanceCompare;
            }
            return $b['stored_at'] <=> $a['stored_at'];
        });

        return array_slice($memories, 0, $limit);
    }

    /**
     * Build session context from memories
     */
    protected function buildSessionContext(string $sessionId): array
    {
        $context = [
            'session_id' => $sessionId,
            'current_topic' => null,
            'entities' => [],
            'recent_topics' => [],
            'conversation_state' => 'active',
            'user_preferences' => [],
            'conversation_history_summary' => '',
            'important_facts' => [],
            'last_updated' => now()->toISOString(),
        ];

        // Get recent memories from all types
        foreach (array_keys($this->memoryTypes) as $memoryType) {
            $memories = $this->retrieveFromMemoryStore($sessionId, $memoryType, 10, 0.3, null, null);

            foreach ($memories as $memory) {
                // Extract entities
                $entities = $this->extractEntities($memory['content']);
                $context['entities'] = array_unique(array_merge($context['entities'], $entities));

                // Extract topics
                $topics = $this->extractTopics($memory['content']);
                $context['recent_topics'] = array_unique(array_merge($context['recent_topics'], $topics));

                // Store important facts
                if ($memory['importance'] >= 0.7) {
                    $context['important_facts'][] = [
                        'content' => $memory['content'],
                        'importance' => $memory['importance'],
                        'stored_at' => $memory['stored_at'],
                    ];
                }
            }
        }

        // Determine current topic
        if (!empty($context['recent_topics'])) {
            $context['current_topic'] = $context['recent_topics'][0];
        }

        // Limit arrays to prevent context bloat
        $context['entities'] = array_slice($context['entities'], 0, 20);
        $context['recent_topics'] = array_slice($context['recent_topics'], 0, 10);
        $context['important_facts'] = array_slice($context['important_facts'], 0, 15);

        return $context;
    }

    /**
     * Enrich session context with real-time data
     */
    protected function enrichSessionContext(string $sessionId, array $context): array
    {
        // Add real-time session information
        $session = WebsocketSession::where('session_id', $sessionId)->first();
        if ($session) {
            $context['session_info'] = [
                'is_active' => $session->is_active,
                'last_activity' => $session->last_activity_at,
                'connection_count' => $session->connection_count,
                'user_agent' => $session->user_agent,
                'ip_address' => $session->ip_address,
            ];
        }

        // Add recent chat history summary
        $recentChats = ChatHistory::where('session_id', $sessionId)
                                ->where('tenant_id', $this->getCurrentTenantId())
                                ->orderBy('created_at', 'desc')
                                ->limit(5)
                                ->get();

        if ($recentChats->isNotEmpty()) {
            $context['recent_chat_summary'] = $recentChats->map(function ($chat) {
                return [
                    'message_type' => $chat->message_type,
                    'content_preview' => substr($chat->user_message, 0, 100),
                    'timestamp' => $chat->created_at->toISOString(),
                ];
            })->toArray();
        }

        return $context;
    }

    /**
     * Generate context summary
     */
    protected function generateContextSummary(array $context): array
    {
        return [
            'current_topic' => $context['current_topic'],
            'entity_count' => count($context['entities'] ?? []),
            'recent_topics_count' => count($context['recent_topics'] ?? []),
            'important_facts_count' => count($context['important_facts'] ?? []),
            'conversation_state' => $context['conversation_state'] ?? 'unknown',
            'session_active' => $context['session_info']['is_active'] ?? false,
            'last_activity' => $context['session_info']['last_activity'] ?? null,
        ];
    }

    /**
     * Merge context updates
     */
    protected function mergeContextUpdates(array $currentContext, array $updates): array
    {
        foreach ($updates as $key => $value) {
            if (is_array($value) && isset($currentContext[$key]) && is_array($currentContext[$key])) {
                // Merge arrays
                $currentContext[$key] = array_unique(array_merge($currentContext[$key], $value));
            } else {
                // Replace value
                $currentContext[$key] = $value;
            }
        }

        $currentContext['last_updated'] = now()->toISOString();

        return $currentContext;
    }

    /**
     * Clear memory store
     */
    protected function clearMemoryStore(string $sessionId, string $memoryType, ?string $olderThan, ?float $belowImportance): int
    {
        $cacheKey = "memory:{$memoryType}:{$sessionId}";
        $memories = $this->getFromCache($cacheKey, []);

        $originalCount = count($memories);

        // Filter memories to keep
        $filteredMemories = array_filter($memories, function ($memory) use ($olderThan, $belowImportance) {
            // Keep if newer than threshold
            if ($olderThan && \Carbon\Carbon::parse($memory['stored_at'])->isAfter($olderThan)) {
                return true;
            }

            // Keep if importance is above threshold
            if ($belowImportance && $memory['importance'] >= $belowImportance) {
                return true;
            }

            // Remove if both conditions apply
            return false;
        });

        // Store filtered memories
        $ttl = $this->getMemoryTypeTTL($memoryType);
        $this->putToCache($cacheKey, $filteredMemories, $ttl);

        return $originalCount - count($filteredMemories);
    }

    /**
     * Get memory type TTL in seconds
     */
    protected function getMemoryTypeTTL(string $memoryType): int
    {
        $config = $this->memoryTypes[$memoryType];

        if (isset($config['retention_minutes'])) {
            return $config['retention_minutes'] * 60;
        }

        if (isset($config['retention_days'])) {
            return $config['retention_days'] * 24 * 60 * 60;
        }

        return 3600; // Default 1 hour
    }

    /**
     * Check if content contains important keywords
     */
    protected function containsImportantKeywords(string $content): bool
    {
        $importantKeywords = [
            'important', 'critical', 'urgent', 'remember', 'note',
            'error', 'problem', 'issue', 'bug', 'fix',
            'password', 'key', 'secret', 'token', 'credential',
            'deadline', 'meeting', 'appointment', 'schedule',
        ];

        $content = strtolower($content);
        foreach ($importantKeywords as $keyword) {
            if (str_contains($content, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if this is a recent interaction
     */
    protected function isRecentInteraction(array $memoryData): bool
    {
        $timestamp = $memoryData['timestamp'] ?? now();
        if (is_string($timestamp)) {
            $timestamp = \Carbon\Carbon::parse($timestamp);
        }

        return $timestamp->isAfter(now()->subMinutes(5));
    }

    /**
     * Extract entities from content
     */
    protected function extractEntities(string $content): array
    {
        $entities = [];

        // Simple entity extraction (in production, this would use NLP)
        // Extract capitalized words (potential proper nouns)
        preg_match_all('/\b[A-Z][a-z]+\b/', $content, $matches);
        $entities = array_merge($entities, $matches[0]);

        // Extract email addresses
        preg_match_all('/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/', $content, $matches);
        $entities = array_merge($entities, $matches[0]);

        // Extract URLs
        preg_match_all('/https?:\/\/[^\s]+/', $content, $matches);
        $entities = array_merge($entities, $matches[0]);

        return array_unique($entities);
    }

    /**
     * Extract topics from content
     */
    protected function extractTopics(string $content): array
    {
        // Simple topic extraction (in production, this would use NLP)
        $topics = [];

        // Extract hashtags
        preg_match_all('/#\w+/', $content, $matches);
        $topics = array_merge($topics, $matches[0]);

        // Extract common topic indicators
        $topicKeywords = ['about', 'regarding', 'concerning', 'topic:', 'subject:'];
        foreach ($topicKeywords as $keyword) {
            if (str_contains(strtolower($content), $keyword)) {
                // Extract words following the keyword
                $pattern = '/' . preg_quote($keyword, '/') . '\s+(\w+(?:\s+\w+){0,2})/i';
                preg_match($pattern, $content, $matches);
                if (isset($matches[1])) {
                    $topics[] = trim($matches[1]);
                }
            }
        }

        return array_unique($topics);
    }

    /**
     * Sort memories by relevance
     */
    protected function sortMemoriesByRelevance(array $memories, ?string $query): array
    {
        if (!$query) {
            // Sort by importance and recency
            usort($memories, function ($a, $b) {
                $importanceCompare = $b['importance'] <=> $a['importance'];
                if ($importanceCompare !== 0) {
                    return $importanceCompare;
                }
                return $b['stored_at'] <=> $a['stored_at'];
            });
            return $memories;
        }

        // Calculate relevance scores for query
        foreach ($memories as &$memory) {
            $memory['relevance_score'] = $this->calculateRelevanceScore($memory, $query);
        }

        // Sort by relevance score
        usort($memories, fn($a, $b) => $b['relevance_score'] <=> $a['relevance_score']);

        return $memories;
    }

    /**
     * Calculate relevance score for query
     */
    protected function calculateRelevanceScore(array $memory, string $query): float
    {
        $content = strtolower($memory['content']);
        $query = strtolower($query);

        $score = 0;

        // Exact match bonus
        if (str_contains($content, $query)) {
            $score += 0.5;
        }

        // Word overlap
        $queryWords = explode(' ', $query);
        $contentWords = explode(' ', $content);
        $overlap = count(array_intersect($queryWords, $contentWords));
        $score += ($overlap / count($queryWords)) * 0.3;

        // Importance factor
        $score += $memory['importance'] * 0.2;

        return $score;
    }

    /**
     * Update memory access patterns
     */
    protected function updateMemoryAccessPatterns(array $memories): void
    {
        foreach ($memories as $memory) {
            $sessionId = $memory['session_id'];
            $memoryType = $memory['memory_type'];
            $memoryId = $memory['id'];

            // Update access count and timestamp
            $cacheKey = "memory:{$memoryType}:{$sessionId}";
            $allMemories = $this->getFromCache($cacheKey, []);

            foreach ($allMemories as &$storedMemory) {
                if ($storedMemory['id'] === $memoryId) {
                    $storedMemory['access_count']++;
                    $storedMemory['last_accessed'] = now()->toISOString();
                    break;
                }
            }

            $ttl = $this->getMemoryTypeTTL($memoryType);
            $this->putToCache($cacheKey, $allMemories, $ttl);
        }
    }

    /**
     * Get memory type usage statistics
     */
    protected function getMemoryTypeUsage(string $tenantId, string $memoryType, $dateFrom, $dateTo): array
    {
        // This would query actual storage in production
        // For now, return mock data
        return [
            'total_entries' => rand(50, 500),
            'average_importance' => round(rand(40, 80) / 100, 2),
            'storage_size_kb' => rand(100, 1000),
            'access_frequency' => rand(10, 100),
        ];
    }

    /**
     * Get memories for consolidation
     */
    protected function getMemoriesForConsolidation(string $sessionId, string $memoryType): array
    {
        $config = $this->memoryTypes[$memoryType];
        $threshold = $config['compression_threshold'];

        $cacheKey = "memory:{$memoryType}:{$sessionId}";
        $memories = $this->getFromCache($cacheKey, []);

        // Return memories if above threshold
        return count($memories) > $threshold ? $memories : [];
    }

    /**
     * Apply consolidation strategy
     */
    protected function applyConsolidationStrategy(array $memories, string $strategy): array
    {
        switch ($strategy) {
            case 'importance_based':
                return $this->consolidateByImportance($memories);

            case 'topic_based':
                return $this->consolidateByTopic($memories);

            case 'time_based':
                return $this->consolidateByTime($memories);

            default:
                return $this->consolidateByImportance($memories);
        }
    }

    /**
     * Consolidate by importance
     */
    protected function consolidateByImportance(array $memories): array
    {
        // Keep high importance memories, summarize low importance ones
        $highImportance = array_filter($memories, fn($m) => $m['importance'] >= 0.7);
        $lowImportance = array_filter($memories, fn($m) => $m['importance'] < 0.7);

        $consolidated = $highImportance;

        if (!empty($lowImportance)) {
            // Create summary of low importance memories
            $summary = $this->createMemorySummary($lowImportance, 'Low importance interactions');
            $consolidated[] = $summary;
        }

        return $consolidated;
    }

    /**
     * Consolidate by topic
     */
    protected function consolidateByTopic(array $memories): array
    {
        $topicGroups = [];

        foreach ($memories as $memory) {
            $topics = $this->extractTopics($memory['content']);
            $topic = !empty($topics) ? $topics[0] : 'general';

            if (!isset($topicGroups[$topic])) {
                $topicGroups[$topic] = [];
            }
            $topicGroups[$topic][] = $memory;
        }

        $consolidated = [];
        foreach ($topicGroups as $topic => $groupMemories) {
            if (count($groupMemories) > 3) {
                // Consolidate if more than 3 memories on same topic
                $summary = $this->createMemorySummary($groupMemories, "Discussion about {$topic}");
                $consolidated[] = $summary;
            } else {
                $consolidated = array_merge($consolidated, $groupMemories);
            }
        }

        return $consolidated;
    }

    /**
     * Consolidate by time
     */
    protected function consolidateByTime(array $memories): array
    {
        // Group memories by time periods (e.g., hourly)
        $timeGroups = [];

        foreach ($memories as $memory) {
            $hour = \Carbon\Carbon::parse($memory['stored_at'])->format('Y-m-d H');
            if (!isset($timeGroups[$hour])) {
                $timeGroups[$hour] = [];
            }
            $timeGroups[$hour][] = $memory;
        }

        $consolidated = [];
        foreach ($timeGroups as $hour => $groupMemories) {
            if (count($groupMemories) > 5) {
                // Consolidate if more than 5 memories in same hour
                $summary = $this->createMemorySummary($groupMemories, "Interactions during {$hour}");
                $consolidated[] = $summary;
            } else {
                $consolidated = array_merge($consolidated, $groupMemories);
            }
        }

        return $consolidated;
    }

    /**
     * Create memory summary
     */
    protected function createMemorySummary(array $memories, string $title): array
    {
        $contentParts = array_map(fn($m) => substr($m['content'], 0, 100), $memories);
        $avgImportance = array_sum(array_column($memories, 'importance')) / count($memories);

        return [
            'id' => uniqid('summary_'),
            'session_id' => $memories[0]['session_id'],
            'memory_type' => $memories[0]['memory_type'],
            'content' => $title . ': ' . implode('; ', $contentParts),
            'message_type' => 'summary',
            'importance' => $avgImportance,
            'metadata' => [
                'is_summary' => true,
                'original_count' => count($memories),
                'time_range' => [
                    'from' => min(array_column($memories, 'stored_at')),
                    'to' => max(array_column($memories, 'stored_at')),
                ],
            ],
            'stored_at' => now()->toISOString(),
            'access_count' => 0,
            'last_accessed' => null,
        ];
    }

    /**
     * Store consolidated memories
     */
    protected function storeConsolidatedMemories(string $sessionId, array $consolidatedMemories): int
    {
        $count = 0;

        foreach ($consolidatedMemories as $memory) {
            $memoryType = $memory['memory_type'];
            $cacheKey = "memory:{$memoryType}:{$sessionId}";

            $memories = $this->getFromCache($cacheKey, []);
            $memories[] = $memory;

            $ttl = $this->getMemoryTypeTTL($memoryType);
            $this->putToCache($cacheKey, $memories, $ttl);

            $count++;
        }

        return $count;
    }

    /**
     * Remove consolidated memories
     */
    protected function removeConsolidatedMemories(string $sessionId, array $memoriesToRemove): int
    {
        $removeIds = array_column($memoriesToRemove, 'id');
        $count = 0;

        foreach (array_keys($this->memoryTypes) as $memoryType) {
            $cacheKey = "memory:{$memoryType}:{$sessionId}";
            $memories = $this->getFromCache($cacheKey, []);

            $originalCount = count($memories);
            $memories = array_filter($memories, fn($m) => !in_array($m['id'], $removeIds));

            if (count($memories) < $originalCount) {
                $ttl = $this->getMemoryTypeTTL($memoryType);
                $this->putToCache($cacheKey, $memories, $ttl);
                $count += $originalCount - count($memories);
            }
        }

        return $count;
    }

    /**
     * Trigger memory consolidation if needed
     */
    protected function triggerMemoryConsolidation(string $sessionId, string $memoryType): void
    {
        $config = $this->memoryTypes[$memoryType];
        $threshold = $config['compression_threshold'];

        $cacheKey = "memory:{$memoryType}:{$sessionId}";
        $memories = $this->getFromCache($cacheKey, []);

        if (count($memories) > $threshold) {
            // Schedule consolidation (in production, this would be queued)
            $this->logActivity('Memory consolidation triggered', [
                'session_id' => $sessionId,
                'memory_type' => $memoryType,
                'memory_count' => count($memories),
                'threshold' => $threshold,
            ]);
        }
    }
}
