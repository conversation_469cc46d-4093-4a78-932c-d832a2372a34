<?php

namespace App\Events\WebSocket;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ChatMessageEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $tenantId;
    public string $sessionId;
    public string $messageId;
    public string $messageType;
    public array $messageData;
    public array $sender;
    public array $metadata;
    public string $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $tenantId,
        string $sessionId,
        string $messageId,
        string $messageType,
        array $messageData,
        array $sender,
        array $metadata = []
    ) {
        $this->tenantId = $tenantId;
        $this->sessionId = $sessionId;
        $this->messageId = $messageId;
        $this->messageType = $messageType;
        $this->messageData = $messageData;
        $this->sender = $sender;
        $this->metadata = $metadata;
        $this->timestamp = now()->toISOString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("chat.{$this->tenantId}.{$this->sessionId}"),
            new PrivateChannel("tenant.{$this->tenantId}"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event_type' => 'chat_message',
            'message_id' => $this->messageId,
            'session_id' => $this->sessionId,
            'message_type' => $this->messageType,
            'data' => $this->messageData,
            'sender' => $this->sender,
            'metadata' => $this->metadata,
            'timestamp' => $this->timestamp,
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'chat.message';
    }

    /**
     * Determine if this event should be queued.
     */
    public function shouldQueue(): bool
    {
        return config('broadcasting.queue.enabled', true);
    }

    /**
     * Get the queue connection for the event.
     */
    public function broadcastQueue(): string
    {
        return config('broadcasting.queue.queue_name', 'websocket');
    }
}
