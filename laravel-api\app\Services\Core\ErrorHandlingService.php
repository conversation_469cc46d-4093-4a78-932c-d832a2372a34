<?php

namespace App\Services\Core;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;

class ErrorHandlingService
{
    protected LoggingService $loggingService;

    /**
     * Error categories for classification
     */
    protected array $errorCategories = [
        'authentication' => ['auth', 'login', 'token', 'permission'],
        'validation' => ['validation', 'input', 'format', 'required'],
        'business_logic' => ['business', 'rule', 'constraint', 'limit'],
        'external_service' => ['api', 'service', 'connection', 'timeout'],
        'database' => ['database', 'query', 'connection', 'constraint'],
        'system' => ['system', 'memory', 'disk', 'cpu'],
        'configuration' => ['config', 'setting', 'environment'],
        'tenant' => ['tenant', 'context', 'isolation'],
    ];

    /**
     * Error severity levels
     */
    protected array $severityLevels = [
        'critical' => 1,
        'high' => 2,
        'medium' => 3,
        'low' => 4,
        'info' => 5,
    ];

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    /**
     * Handle any exception with comprehensive error processing
     */
    public function handleException(Throwable $exception, array $context = []): Exception
    {
        $errorId = $this->generateErrorId();
        $category = $this->categorizeError($exception);
        $severity = $this->determineSeverity($exception, $category);

        // Log the error
        $this->logError($exception, $errorId, $category, $severity, $context);

        // Send alerts if necessary
        $this->sendAlertsIfNeeded($exception, $category, $severity, $context);

        // Create standardized exception
        return $this->createStandardizedException($exception, $errorId, $category, $severity);
    }

    /**
     * Create a standardized API error response
     */
    public function createErrorResponse(Throwable $exception, array $context = []): JsonResponse
    {
        $errorId = $this->generateErrorId();
        $category = $this->categorizeError($exception);
        $severity = $this->determineSeverity($exception, $category);

        // Log the error
        $this->logError($exception, $errorId, $category, $severity, $context);

        // Determine HTTP status code
        $statusCode = $this->getHttpStatusCode($exception);

        // Create user-friendly message
        $userMessage = $this->getUserFriendlyMessage($exception, $category);

        $response = [
            'error' => true,
            'error_id' => $errorId,
            'message' => $userMessage,
            'category' => $category,
            'timestamp' => now()->toISOString(),
        ];

        // Add debug information in development
        if (config('app.debug')) {
            $response['debug'] = [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Create custom exception types
     */
    public function createPermissionException(string $permission): Exception
    {
        return new class($permission) extends Exception {
            public function __construct(string $permission)
            {
                parent::__construct("Insufficient permissions: {$permission} required");
            }
        };
    }

    public function createConfigurationException(string $message): Exception
    {
        return new class($message) extends Exception {
            public function __construct(string $message)
            {
                parent::__construct("Configuration error: {$message}");
            }
        };
    }

    public function createServiceDisabledException(string $service): Exception
    {
        return new class($service) extends Exception {
            public function __construct(string $service)
            {
                parent::__construct("Service disabled: {$service}");
            }
        };
    }

    public function createTenantContextException(): Exception
    {
        return new class() extends Exception {
            public function __construct()
            {
                parent::__construct("Tenant context is required but not available");
            }
        };
    }

    public function createBusinessRuleException(string $rule, array $context = []): Exception
    {
        return new class($rule, $context) extends Exception {
            protected array $context;

            public function __construct(string $rule, array $context = [])
            {
                $this->context = $context;
                parent::__construct("Business rule violation: {$rule}");
            }

            public function getContext(): array
            {
                return $this->context;
            }
        };
    }

    public function createExternalServiceException(string $service, string $message, ?Throwable $previous = null): Exception
    {
        return new class($service, $message, $previous) extends Exception {
            protected string $service;

            public function __construct(string $service, string $message, ?Throwable $previous = null)
            {
                $this->service = $service;
                parent::__construct("External service error ({$service}): {$message}", 0, $previous);
            }

            public function getService(): string
            {
                return $this->service;
            }
        };
    }

    /**
     * Generate unique error ID
     */
    protected function generateErrorId(): string
    {
        return 'ERR_' . strtoupper(Str::random(8)) . '_' . time();
    }

    /**
     * Categorize error based on exception type and message
     */
    protected function categorizeError(Throwable $exception): string
    {
        $exceptionClass = get_class($exception);
        $message = strtolower($exception->getMessage());

        // Check exception class patterns
        if (str_contains($exceptionClass, 'Auth') || str_contains($exceptionClass, 'Permission')) {
            return 'authentication';
        }

        if (str_contains($exceptionClass, 'Validation') || str_contains($exceptionClass, 'Http')) {
            return 'validation';
        }

        if (str_contains($exceptionClass, 'Database') || str_contains($exceptionClass, 'Query')) {
            return 'database';
        }

        // Check message patterns
        foreach ($this->errorCategories as $category => $keywords) {
            foreach ($keywords as $keyword) {
                if (str_contains($message, $keyword)) {
                    return $category;
                }
            }
        }

        return 'system';
    }

    /**
     * Determine error severity
     */
    protected function determineSeverity(Throwable $exception, string $category): string
    {
        // Critical errors
        if (str_contains($exception->getMessage(), 'fatal') ||
            str_contains($exception->getMessage(), 'critical') ||
            $category === 'system') {
            return 'critical';
        }

        // High severity
        if ($category === 'database' ||
            $category === 'external_service' ||
            $category === 'configuration') {
            return 'high';
        }

        // Medium severity
        if ($category === 'business_logic' ||
            $category === 'tenant') {
            return 'medium';
        }

        // Low severity
        if ($category === 'authentication' ||
            $category === 'validation') {
            return 'low';
        }

        return 'medium';
    }

    /**
     * Log error with comprehensive information
     */
    protected function logError(Throwable $exception, string $errorId, string $category, string $severity, array $context): void
    {
        $this->loggingService->log([
            'log_type' => 'error',
            'log_level' => $this->mapSeverityToLogLevel($severity),
            'message' => $exception->getMessage(),
            'error_id' => $errorId,
            'error_category' => $category,
            'error_severity' => $severity,
            'exception_class' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'stack_trace' => $exception->getTraceAsString(),
            'context' => $context,
            'tenant_id' => $context['tenant_id'] ?? null,
            'user_id' => $context['user_id'] ?? null,
            'is_security_incident' => $this->isSecurityIncident($exception, $category),
        ]);
    }

    /**
     * Send alerts for critical errors
     */
    protected function sendAlertsIfNeeded(Throwable $exception, string $category, string $severity, array $context): void
    {
        if ($severity === 'critical' || $this->isSecurityIncident($exception, $category)) {
            // In a real implementation, this would send alerts via:
            // - Email to administrators
            // - Slack notifications
            // - SMS for critical issues
            // - External monitoring services (PagerDuty, etc.)

            Log::critical('ALERT: Critical error detected', [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'category' => $category,
                'severity' => $severity,
                'context' => $context,
            ]);
        }
    }

    /**
     * Create standardized exception
     */
    protected function createStandardizedException(Throwable $original, string $errorId, string $category, string $severity): Exception
    {
        $message = "Error {$errorId}: {$original->getMessage()}";

        return new class($message, $original, $errorId, $category, $severity) extends Exception {
            protected string $errorId;
            protected string $category;
            protected string $severity;

            public function __construct(string $message, Throwable $previous, string $errorId, string $category, string $severity)
            {
                $this->errorId = $errorId;
                $this->category = $category;
                $this->severity = $severity;
                parent::__construct($message, 0, $previous);
            }

            public function getErrorId(): string
            {
                return $this->errorId;
            }

            public function getCategory(): string
            {
                return $this->category;
            }

            public function getSeverity(): string
            {
                return $this->severity;
            }
        };
    }

    /**
     * Get HTTP status code for exception
     */
    protected function getHttpStatusCode(Throwable $exception): int
    {
        $exceptionClass = get_class($exception);
        $message = strtolower($exception->getMessage());

        // Authentication/Authorization errors
        if (str_contains($message, 'permission') || str_contains($message, 'unauthorized')) {
            return 403;
        }

        if (str_contains($message, 'unauthenticated') || str_contains($message, 'token')) {
            return 401;
        }

        // Validation errors
        if (str_contains($exceptionClass, 'Validation') || str_contains($message, 'validation')) {
            return 422;
        }

        // Not found errors
        if (str_contains($message, 'not found') || str_contains($message, 'does not exist')) {
            return 404;
        }

        // Business rule violations
        if (str_contains($message, 'business rule') || str_contains($message, 'constraint')) {
            return 409;
        }

        // Rate limiting
        if (str_contains($message, 'rate limit') || str_contains($message, 'too many')) {
            return 429;
        }

        // External service errors
        if (str_contains($message, 'external service') || str_contains($message, 'timeout')) {
            return 502;
        }

        // Default to 500 for server errors
        return 500;
    }

    /**
     * Get user-friendly error message
     */
    protected function getUserFriendlyMessage(Throwable $exception, string $category): string
    {
        $messages = [
            'authentication' => 'Authentication failed. Please check your credentials.',
            'validation' => 'The provided data is invalid. Please check your input.',
            'business_logic' => 'This action cannot be completed due to business rules.',
            'external_service' => 'An external service is currently unavailable. Please try again later.',
            'database' => 'A database error occurred. Please try again.',
            'system' => 'A system error occurred. Our team has been notified.',
            'configuration' => 'A configuration error occurred. Please contact support.',
            'tenant' => 'Tenant context error. Please ensure you are accessing the correct tenant.',
        ];

        return $messages[$category] ?? 'An unexpected error occurred. Please try again.';
    }

    /**
     * Map severity to log level
     */
    protected function mapSeverityToLogLevel(string $severity): string
    {
        return match ($severity) {
            'critical' => 'critical',
            'high' => 'error',
            'medium' => 'warning',
            'low' => 'notice',
            'info' => 'info',
            default => 'error',
        };
    }

    /**
     * Check if error is a security incident
     */
    protected function isSecurityIncident(Throwable $exception, string $category): bool
    {
        $message = strtolower($exception->getMessage());

        $securityKeywords = [
            'injection', 'xss', 'csrf', 'unauthorized', 'permission',
            'authentication', 'token', 'session', 'brute force',
            'suspicious', 'malicious', 'attack'
        ];

        foreach ($securityKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }

        return $category === 'authentication';
    }

    /**
     * Get error statistics
     */
    public function getErrorStatistics(int $hours = 24): array
    {
        // This would query the system logs to get error statistics
        // For now, return a placeholder structure
        return [
            'total_errors' => 0,
            'by_category' => [],
            'by_severity' => [],
            'trend' => [],
            'top_errors' => [],
            'period_hours' => $hours,
        ];
    }
}
