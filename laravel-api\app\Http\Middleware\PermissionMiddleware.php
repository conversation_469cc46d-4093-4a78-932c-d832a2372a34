<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        // Ensure user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'error' => 'Unauthenticated',
                'message' => 'Authentication required'
            ], 401);
        }

        $user = Auth::user();
        $tenant = $request->get('tenant');

        // Ensure tenant context is set
        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant context missing',
                'message' => 'Tenant context must be established'
            ], 400);
        }

        // Get user permissions
        $userPermissions = $this->getUserPermissions($user, $tenant);

        // Check if user has all required permissions
        $missingPermissions = array_diff($permissions, $userPermissions);

        if (!empty($missingPermissions)) {
            // Log unauthorized access attempt
            $this->logUnauthorizedAccess($request, $user, $permissions, $missingPermissions);

            return response()->json([
                'error' => 'Insufficient permissions',
                'message' => 'You do not have the required permissions to access this resource',
                'required_permissions' => $permissions,
                'missing_permissions' => $missingPermissions,
                'user_permissions' => $userPermissions
            ], 403);
        }

        // Add permission information to request
        $request->merge([
            'user_permissions' => $userPermissions,
            'granted_permissions' => $permissions
        ]);

        return $next($request);
    }

    /**
     * Get all permissions for a user
     */
    protected function getUserPermissions($user, $tenant): array
    {
        $permissions = [];

        // Get role-based permissions
        $rolePermissions = $this->getRolePermissions($user->role, $tenant);
        $permissions = array_merge($permissions, $rolePermissions);

        // Get user-specific permissions
        $userSpecificPermissions = $user->additional_permissions ?? [];
        $permissions = array_merge($permissions, $userSpecificPermissions);

        // Get tenant-specific user permissions
        $tenantUserPermissions = $tenant->user_permissions[$user->id] ?? [];
        $permissions = array_merge($permissions, $tenantUserPermissions);

        // Remove any revoked permissions
        $revokedPermissions = $user->revoked_permissions ?? [];
        $permissions = array_diff($permissions, $revokedPermissions);

        // Remove tenant-level revoked permissions
        $tenantRevokedPermissions = $tenant->revoked_permissions[$user->id] ?? [];
        $permissions = array_diff($permissions, $tenantRevokedPermissions);

        return array_unique($permissions);
    }

    /**
     * Get permissions based on role
     */
    protected function getRolePermissions(string $role, $tenant): array
    {
        $basePermissions = [
            'owner' => [
                // Tenant management
                'tenant:view', 'tenant:update', 'tenant:delete',
                'tenant:settings:view', 'tenant:settings:update',
                'tenant:billing:view', 'tenant:billing:update',

                // User management
                'users:view', 'users:create', 'users:update', 'users:delete',
                'users:roles:update', 'users:permissions:update',

                // AI Providers
                'ai_providers:view', 'ai_providers:create', 'ai_providers:update', 'ai_providers:delete',
                'ai_providers:test', 'ai_providers:usage:view',

                // Tools
                'tools:view', 'tools:create', 'tools:update', 'tools:delete',
                'tools:execute', 'tools:test', 'tools:usage:view',

                // Workflows
                'workflows:view', 'workflows:create', 'workflows:update', 'workflows:delete',
                'workflows:execute', 'workflows:schedule', 'workflows:usage:view',

                // Documents
                'documents:view', 'documents:create', 'documents:update', 'documents:delete',
                'documents:process', 'documents:embeddings:view',

                // Chat
                'chat:access', 'chat:sessions:view', 'chat:sessions:create',
                'chat:sessions:delete', 'chat:messages:view', 'chat:messages:send',

                // WebSocket
                'websocket:connect', 'websocket:broadcast', 'websocket:sessions:view',
                'websocket:sessions:disconnect',

                // Monitoring
                'monitoring:dashboard:view', 'monitoring:logs:view', 'monitoring:metrics:view',
                'monitoring:health:view', 'monitoring:usage:view', 'monitoring:errors:view'
            ],

            'admin' => [
                // User management (limited)
                'users:view', 'users:create', 'users:update',
                'users:roles:update',

                // AI Providers
                'ai_providers:view', 'ai_providers:create', 'ai_providers:update', 'ai_providers:delete',
                'ai_providers:test', 'ai_providers:usage:view',

                // Tools
                'tools:view', 'tools:create', 'tools:update', 'tools:delete',
                'tools:execute', 'tools:test', 'tools:usage:view',

                // Workflows
                'workflows:view', 'workflows:create', 'workflows:update', 'workflows:delete',
                'workflows:execute', 'workflows:schedule', 'workflows:usage:view',

                // Documents
                'documents:view', 'documents:create', 'documents:update', 'documents:delete',
                'documents:process', 'documents:embeddings:view',

                // Chat
                'chat:access', 'chat:sessions:view', 'chat:sessions:create',
                'chat:sessions:delete', 'chat:messages:view', 'chat:messages:send',

                // WebSocket
                'websocket:connect', 'websocket:broadcast', 'websocket:sessions:view',

                // Monitoring
                'monitoring:dashboard:view', 'monitoring:logs:view', 'monitoring:metrics:view',
                'monitoring:health:view', 'monitoring:usage:view', 'monitoring:errors:view'
            ],

            'manager' => [
                // User management (view only)
                'users:view',

                // AI Providers (view only)
                'ai_providers:view', 'ai_providers:usage:view',

                // Tools
                'tools:view', 'tools:create', 'tools:update',
                'tools:execute', 'tools:test', 'tools:usage:view',

                // Workflows
                'workflows:view', 'workflows:create', 'workflows:update',
                'workflows:execute', 'workflows:schedule', 'workflows:usage:view',

                // Documents
                'documents:view', 'documents:create', 'documents:update', 'documents:delete',
                'documents:process', 'documents:embeddings:view',

                // Chat
                'chat:access', 'chat:sessions:view', 'chat:sessions:create',
                'chat:sessions:delete', 'chat:messages:view', 'chat:messages:send',

                // WebSocket
                'websocket:connect'
            ],

            'user' => [
                // Tools (execute only)
                'tools:view', 'tools:execute', 'tools:test',

                // Workflows (execute only)
                'workflows:view', 'workflows:execute',

                // Documents
                'documents:view', 'documents:create', 'documents:update',
                'documents:process', 'documents:embeddings:view',

                // Chat
                'chat:access', 'chat:sessions:view', 'chat:sessions:create',
                'chat:messages:view', 'chat:messages:send',

                // WebSocket
                'websocket:connect'
            ],

            'viewer' => [
                // Read-only access
                'tools:view',
                'workflows:view',
                'documents:view',
                'chat:sessions:view', 'chat:messages:view',
                'websocket:connect'
            ]
        ];

        $permissions = $basePermissions[$role] ?? [];

        // Add tenant-specific role permissions
        $tenantRolePermissions = $tenant->role_permissions[$role] ?? [];

        return array_unique(array_merge($permissions, $tenantRolePermissions));
    }

    /**
     * Log unauthorized access attempt
     */
    protected function logUnauthorizedAccess(Request $request, $user, array $requiredPermissions, array $missingPermissions): void
    {
        \Log::warning('Unauthorized access attempt - insufficient permissions', [
            'user_id' => $user->id,
            'tenant_id' => $user->tenant_id,
            'user_role' => $user->role,
            'required_permissions' => $requiredPermissions,
            'missing_permissions' => $missingPermissions,
            'endpoint' => $request->fullUrl(),
            'method' => $request->method(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);
    }
}
