<?php

namespace App\Services\MCP;

use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;

class IntentService extends BaseService
{
    /**
     * Predefined intent categories with patterns
     */
    protected array $intentCategories = [
        'question' => [
            'patterns' => ['what', 'how', 'why', 'when', 'where', 'who', '?'],
            'confidence_boost' => 0.2,
            'required_tools' => [],
            'workflow_id' => null,
        ],
        'request' => [
            'patterns' => ['please', 'can you', 'could you', 'would you', 'help me'],
            'confidence_boost' => 0.15,
            'required_tools' => [],
            'workflow_id' => null,
        ],
        'command' => [
            'patterns' => ['create', 'delete', 'update', 'generate', 'build', 'make'],
            'confidence_boost' => 0.25,
            'required_tools' => ['file_manager', 'code_generator'],
            'workflow_id' => 'command_execution',
        ],
        'search' => [
            'patterns' => ['find', 'search', 'look for', 'locate', 'discover'],
            'confidence_boost' => 0.3,
            'required_tools' => ['search_engine', 'knowledge_base'],
            'workflow_id' => 'search_workflow',
        ],
        'analysis' => [
            'patterns' => ['analyze', 'review', 'examine', 'evaluate', 'assess'],
            'confidence_boost' => 0.2,
            'required_tools' => ['analyzer', 'data_processor'],
            'workflow_id' => 'analysis_workflow',
        ],
        'conversation' => [
            'patterns' => ['hello', 'hi', 'thanks', 'thank you', 'goodbye', 'bye'],
            'confidence_boost' => 0.1,
            'required_tools' => [],
            'workflow_id' => null,
        ],
        'troubleshooting' => [
            'patterns' => ['error', 'problem', 'issue', 'bug', 'fix', 'broken'],
            'confidence_boost' => 0.25,
            'required_tools' => ['debugger', 'log_analyzer'],
            'workflow_id' => 'troubleshooting_workflow',
        ],
        'explanation' => [
            'patterns' => ['explain', 'describe', 'tell me about', 'what is'],
            'confidence_boost' => 0.2,
            'required_tools' => ['knowledge_base'],
            'workflow_id' => null,
        ],
        'comparison' => [
            'patterns' => ['compare', 'difference', 'versus', 'vs', 'better'],
            'confidence_boost' => 0.2,
            'required_tools' => ['comparator', 'analyzer'],
            'workflow_id' => 'comparison_workflow',
        ],
        'general' => [
            'patterns' => [],
            'confidence_boost' => 0.0,
            'required_tools' => [],
            'workflow_id' => null,
        ],
    ];

    /**
     * Context keywords that modify intent classification
     */
    protected array $contextModifiers = [
        'urgent' => ['urgent', 'asap', 'immediately', 'quickly', 'fast'],
        'detailed' => ['detailed', 'comprehensive', 'thorough', 'complete'],
        'simple' => ['simple', 'basic', 'quick', 'brief', 'short'],
        'technical' => ['technical', 'advanced', 'complex', 'detailed'],
        'business' => ['business', 'commercial', 'enterprise', 'professional'],
        'personal' => ['personal', 'private', 'individual', 'my'],
    ];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'intent_service';
    }

    /**
     * Classify user intent with confidence scoring
     */
    public function classifyIntent(string $message, array $context = []): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('classify_intent', function () use ($message, $context) {
            // Normalize message for analysis
            $normalizedMessage = $this->normalizeMessage($message);

            // Get base intent classification
            $baseClassification = $this->performBaseClassification($normalizedMessage);

            // Apply context modifiers
            $contextualClassification = $this->applyContextModifiers($baseClassification, $normalizedMessage, $context);

            // Enhance with session context if available
            $enhancedClassification = $this->enhanceWithSessionContext($contextualClassification, $context);

            // Validate and finalize classification
            $finalClassification = $this->finalizeClassification($enhancedClassification, $message);

            return $finalClassification;
        }, [
            'message_length' => strlen($message),
            'context_keys' => array_keys($context),
            'session_id' => $context['session_id'] ?? null,
        ]);
    }

    /**
     * Get intent suggestions based on partial input
     */
    public function getIntentSuggestions(string $partialMessage, array $context = []): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_intent_suggestions', function () use ($partialMessage, $context) {
            $normalizedMessage = $this->normalizeMessage($partialMessage);
            $suggestions = [];

            foreach ($this->intentCategories as $intent => $config) {
                $score = $this->calculatePartialMatchScore($normalizedMessage, $config['patterns']);

                if ($score > 0.1) { // Minimum threshold for suggestions
                    $suggestions[] = [
                        'intent' => $intent,
                        'confidence' => $score,
                        'description' => $this->getIntentDescription($intent),
                        'example_phrases' => $this->getExamplePhrases($intent),
                    ];
                }
            }

            // Sort by confidence
            usort($suggestions, fn($a, $b) => $b['confidence'] <=> $a['confidence']);

            return array_slice($suggestions, 0, 5); // Return top 5 suggestions
        }, [
            'partial_message_length' => strlen($partialMessage),
            'context_provided' => !empty($context),
        ]);
    }

    /**
     * Analyze intent patterns for tenant customization
     */
    public function analyzeIntentPatterns(array $messages, ?string $tenantId = null): array
    {
        $this->requireEnabled();
        $tenantId = $tenantId ?? $this->getCurrentTenantId();

        return $this->executeWithTracking('analyze_intent_patterns', function () use ($messages, $tenantId) {
            $patterns = [];
            $intentCounts = [];
            $confidenceDistribution = [];

            foreach ($messages as $message) {
                $classification = $this->classifyIntent($message['content'] ?? '', $message['context'] ?? []);
                $intent = $classification['intent'];
                $confidence = $classification['confidence'];

                // Count intent occurrences
                $intentCounts[$intent] = ($intentCounts[$intent] ?? 0) + 1;

                // Track confidence distribution
                $confidenceRange = $this->getConfidenceRange($confidence);
                $confidenceDistribution[$confidenceRange] = ($confidenceDistribution[$confidenceRange] ?? 0) + 1;

                // Extract patterns
                $messagePatterns = $this->extractPatterns($message['content'] ?? '');
                foreach ($messagePatterns as $pattern) {
                    $patterns[$intent][] = $pattern;
                }
            }

            // Calculate statistics
            $totalMessages = count($messages);
            $intentDistribution = array_map(fn($count) => round($count / $totalMessages * 100, 2), $intentCounts);

            return [
                'tenant_id' => $tenantId,
                'total_messages_analyzed' => $totalMessages,
                'intent_distribution' => $intentDistribution,
                'confidence_distribution' => $confidenceDistribution,
                'discovered_patterns' => $patterns,
                'recommendations' => $this->generateRecommendations($intentDistribution, $confidenceDistribution),
                'analysis_timestamp' => now()->toISOString(),
            ];
        }, [
            'message_count' => count($messages),
            'tenant_id' => $tenantId,
        ]);
    }

    /**
     * Update intent categories for tenant
     */
    public function updateTenantIntentCategories(array $customCategories, ?string $tenantId = null): bool
    {
        $this->requireEnabled();
        $this->requirePermission('manage_intent_categories');
        $tenantId = $tenantId ?? $this->getCurrentTenantId();

        return $this->executeWithTracking('update_tenant_intent_categories', function () use ($customCategories, $tenantId) {
            // Validate custom categories
            $this->validateIntentCategories($customCategories);

            // Merge with default categories
            $mergedCategories = array_merge($this->intentCategories, $customCategories);

            // Store tenant-specific configuration
            $this->configurationService->setTenantSettings($tenantId, [
                'intent_categories' => $mergedCategories,
                'updated_at' => now()->toISOString(),
            ]);

            $this->logActivity('Updated tenant intent categories', [
                'tenant_id' => $tenantId,
                'categories_count' => count($customCategories),
                'total_categories' => count($mergedCategories),
            ]);

            return true;
        }, [
            'tenant_id' => $tenantId,
            'custom_categories_count' => count($customCategories),
        ]);
    }

    /**
     * Normalize message for analysis
     */
    protected function normalizeMessage(string $message): string
    {
        // Convert to lowercase
        $normalized = strtolower($message);

        // Remove extra whitespace
        $normalized = preg_replace('/\s+/', ' ', $normalized);

        // Remove special characters but keep basic punctuation
        $normalized = preg_replace('/[^\w\s\?\!\.\,]/', '', $normalized);

        return trim($normalized);
    }

    /**
     * Perform base intent classification
     */
    protected function performBaseClassification(string $normalizedMessage): array
    {
        $scores = [];

        foreach ($this->intentCategories as $intent => $config) {
            $score = $this->calculateIntentScore($normalizedMessage, $config);
            $scores[$intent] = $score;
        }

        // Find the highest scoring intent
        $topIntent = array_key_exists('general', $scores) ? 'general' : array_keys($scores)[0];
        $topScore = $scores['general'] ?? 0;

        foreach ($scores as $intent => $score) {
            if ($score > $topScore) {
                $topIntent = $intent;
                $topScore = $score;
            }
        }

        return [
            'intent' => $topIntent,
            'confidence' => $topScore,
            'all_scores' => $scores,
            'required_tools' => $this->intentCategories[$topIntent]['required_tools'] ?? [],
            'workflow_id' => $this->intentCategories[$topIntent]['workflow_id'] ?? null,
        ];
    }

    /**
     * Calculate intent score based on patterns
     */
    protected function calculateIntentScore(string $message, array $config): float
    {
        $patterns = $config['patterns'] ?? [];
        $confidenceBoost = $config['confidence_boost'] ?? 0;

        if (empty($patterns)) {
            return 0.1; // Base score for general intent
        }

        $matches = 0;
        $totalPatterns = count($patterns);

        foreach ($patterns as $pattern) {
            if (str_contains($message, strtolower($pattern))) {
                $matches++;
            }
        }

        $baseScore = $matches / $totalPatterns;
        return min(1.0, $baseScore + $confidenceBoost);
    }

    /**
     * Apply context modifiers to classification
     */
    protected function applyContextModifiers(array $classification, string $message, array $context): array
    {
        $modifiers = [];

        // Check for context modifier keywords in message
        foreach ($this->contextModifiers as $modifier => $keywords) {
            foreach ($keywords as $keyword) {
                if (str_contains($message, $keyword)) {
                    $modifiers[] = $modifier;
                    break;
                }
            }
        }

        // Apply context from previous interactions
        if (isset($context['previous_intent'])) {
            $classification['context']['previous_intent'] = $context['previous_intent'];
        }

        if (isset($context['conversation_topic'])) {
            $classification['context']['conversation_topic'] = $context['conversation_topic'];
        }

        $classification['modifiers'] = $modifiers;
        $classification['context_applied'] = !empty($modifiers) || !empty($context);

        return $classification;
    }

    /**
     * Enhance classification with session context
     */
    protected function enhanceWithSessionContext(array $classification, array $context): array
    {
        $sessionId = $context['session_id'] ?? null;

        if (!$sessionId) {
            return $classification;
        }

        // Get session history from cache if available
        $sessionHistory = $this->getFromCache("session_intent_history:{$sessionId}", []);

        if (!empty($sessionHistory)) {
            // Analyze intent patterns in session
            $recentIntents = array_slice($sessionHistory, -5); // Last 5 intents
            $intentFrequency = array_count_values(array_column($recentIntents, 'intent'));

            // Boost confidence if intent is consistent with recent history
            $currentIntent = $classification['intent'];
            if (isset($intentFrequency[$currentIntent]) && $intentFrequency[$currentIntent] >= 2) {
                $classification['confidence'] = min(1.0, $classification['confidence'] + 0.1);
                $classification['session_consistency'] = true;
            }

            $classification['session_context'] = [
                'recent_intents' => $recentIntents,
                'intent_frequency' => $intentFrequency,
                'session_length' => count($sessionHistory),
            ];
        }

        // Update session history
        $sessionHistory[] = [
            'intent' => $classification['intent'],
            'confidence' => $classification['confidence'],
            'timestamp' => now()->toISOString(),
        ];

        // Keep only last 20 intents
        $sessionHistory = array_slice($sessionHistory, -20);

        // Cache updated history
        $this->putToCache("session_intent_history:{$sessionId}", $sessionHistory, 3600);

        return $classification;
    }

    /**
     * Finalize classification with validation
     */
    protected function finalizeClassification(array $classification, string $originalMessage): array
    {
        // Ensure minimum confidence threshold
        $minConfidence = $this->getConfig('min_confidence_threshold', 0.1);
        if ($classification['confidence'] < $minConfidence) {
            $classification['intent'] = 'general';
            $classification['confidence'] = $minConfidence;
            $classification['fallback_used'] = true;
        }

        // Add metadata
        $classification['original_message'] = $originalMessage;
        $classification['classification_timestamp'] = now()->toISOString();
        $classification['tenant_id'] = $this->getCurrentTenantId();
        $classification['user_id'] = $this->getCurrentUserId();

        // Log classification for analytics
        $this->logActivity('Intent classified', [
            'intent' => $classification['intent'],
            'confidence' => $classification['confidence'],
            'message_length' => strlen($originalMessage),
            'modifiers' => $classification['modifiers'] ?? [],
        ]);

        return $classification;
    }

    /**
     * Calculate partial match score for suggestions
     */
    protected function calculatePartialMatchScore(string $partialMessage, array $patterns): float
    {
        if (empty($patterns) || empty($partialMessage)) {
            return 0;
        }

        $matches = 0;
        foreach ($patterns as $pattern) {
            if (str_starts_with(strtolower($pattern), $partialMessage) ||
                str_contains(strtolower($pattern), $partialMessage)) {
                $matches++;
            }
        }

        return $matches / count($patterns);
    }

    /**
     * Get intent description
     */
    protected function getIntentDescription(string $intent): string
    {
        $descriptions = [
            'question' => 'Asking for information or clarification',
            'request' => 'Requesting assistance or action',
            'command' => 'Giving instructions or commands',
            'search' => 'Looking for specific information',
            'analysis' => 'Requesting analysis or evaluation',
            'conversation' => 'General conversation or social interaction',
            'troubleshooting' => 'Reporting problems or seeking solutions',
            'explanation' => 'Requesting explanations or descriptions',
            'comparison' => 'Comparing options or alternatives',
            'general' => 'General or unclear intent',
        ];

        return $descriptions[$intent] ?? 'Unknown intent type';
    }

    /**
     * Get example phrases for intent
     */
    protected function getExamplePhrases(string $intent): array
    {
        $examples = [
            'question' => ['What is...?', 'How do I...?', 'Why does...?'],
            'request' => ['Please help me...', 'Can you...?', 'I need...'],
            'command' => ['Create a...', 'Delete the...', 'Update this...'],
            'search' => ['Find information about...', 'Search for...', 'Look up...'],
            'analysis' => ['Analyze this...', 'Review the...', 'Evaluate...'],
            'conversation' => ['Hello', 'Thank you', 'How are you?'],
            'troubleshooting' => ['I have an error...', 'This is broken...', 'Fix this...'],
            'explanation' => ['Explain how...', 'Describe the...', 'Tell me about...'],
            'comparison' => ['Compare A and B', 'What\'s better...?', 'Difference between...'],
            'general' => ['General inquiry', 'Mixed intent', 'Unclear request'],
        ];

        return $examples[$intent] ?? [];
    }

    /**
     * Extract patterns from message
     */
    protected function extractPatterns(string $message): array
    {
        $patterns = [];
        $words = explode(' ', $this->normalizeMessage($message));

        // Extract n-grams (1-3 words)
        for ($n = 1; $n <= 3; $n++) {
            for ($i = 0; $i <= count($words) - $n; $i++) {
                $ngram = implode(' ', array_slice($words, $i, $n));
                if (strlen($ngram) > 2) { // Skip very short patterns
                    $patterns[] = $ngram;
                }
            }
        }

        return array_unique($patterns);
    }

    /**
     * Get confidence range for distribution analysis
     */
    protected function getConfidenceRange(float $confidence): string
    {
        if ($confidence >= 0.8) return 'high';
        if ($confidence >= 0.6) return 'medium-high';
        if ($confidence >= 0.4) return 'medium';
        if ($confidence >= 0.2) return 'low-medium';
        return 'low';
    }

    /**
     * Generate recommendations based on analysis
     */
    protected function generateRecommendations(array $intentDistribution, array $confidenceDistribution): array
    {
        $recommendations = [];

        // Check for low confidence patterns
        $lowConfidencePercentage = ($confidenceDistribution['low'] ?? 0) + ($confidenceDistribution['low-medium'] ?? 0);
        if ($lowConfidencePercentage > 30) {
            $recommendations[] = [
                'type' => 'improve_patterns',
                'message' => 'Consider adding more specific patterns for better intent classification',
                'priority' => 'medium',
            ];
        }

        // Check for dominant intents
        $maxIntentPercentage = max($intentDistribution);
        if ($maxIntentPercentage > 60) {
            $dominantIntent = array_search($maxIntentPercentage, $intentDistribution);
            $recommendations[] = [
                'type' => 'intent_specialization',
                'message' => "Consider creating sub-categories for '{$dominantIntent}' intent",
                'priority' => 'low',
            ];
        }

        // Check for unused intents
        $unusedIntents = array_filter($intentDistribution, fn($percentage) => $percentage < 1);
        if (count($unusedIntents) > 3) {
            $recommendations[] = [
                'type' => 'cleanup_intents',
                'message' => 'Consider removing unused intent categories',
                'priority' => 'low',
            ];
        }

        return $recommendations;
    }

    /**
     * Validate intent categories structure
     */
    protected function validateIntentCategories(array $categories): void
    {
        foreach ($categories as $intent => $config) {
            if (!is_array($config)) {
                throw new \InvalidArgumentException("Intent configuration must be an array for: {$intent}");
            }

            $requiredKeys = ['patterns', 'confidence_boost'];
            foreach ($requiredKeys as $key) {
                if (!array_key_exists($key, $config)) {
                    throw new \InvalidArgumentException("Missing required key '{$key}' for intent: {$intent}");
                }
            }

            if (!is_array($config['patterns'])) {
                throw new \InvalidArgumentException("Patterns must be an array for intent: {$intent}");
            }

            if (!is_numeric($config['confidence_boost']) || $config['confidence_boost'] < 0 || $config['confidence_boost'] > 1) {
                throw new \InvalidArgumentException("Confidence boost must be a number between 0 and 1 for intent: {$intent}");
            }
        }
    }
}
