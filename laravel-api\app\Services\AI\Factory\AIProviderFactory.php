<?php

namespace App\Services\AI\Factory;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\AI\Providers\OpenAIProvider;
use App\Services\AI\Providers\GroqProvider;
use App\Services\AI\Providers\HuggingFaceProvider;
use App\Services\AI\Providers\GeminiProvider;
use App\Services\AI\Providers\DeepSeekProvider;
use App\Services\AI\Providers\MistralProvider;
use App\Services\AI\Providers\CodestralProvider;
use App\Services\AI\Providers\AnthropicProvider;
use App\Services\AI\Providers\OpenRouterProvider;
use App\Services\AI\Providers\GrokProvider;
use App\Services\Core\LoggingService;
use App\Models\AIProvider;

class AIProviderFactory
{
    protected LoggingService $loggingService;
    protected array $providers = [];

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
        $this->registerProviders();
    }

    /**
     * Register all available providers
     */
    protected function registerProviders(): void
    {
        $this->providers = [
            'openai' => OpenAIProvider::class,
            'groq' => GroqProvider::class,
            'huggingface' => HuggingFaceProvider::class,
            'gemini' => GeminiProvider::class,
            'deepseek' => DeepSeekProvider::class,
            'mistral' => MistralProvider::class,
            'codestral' => CodestralProvider::class,
            'anthropic' => AnthropicProvider::class,
            'openrouter' => OpenRouterProvider::class,
            'grok' => GrokProvider::class,
        ];
    }

    /**
     * Create a provider instance
     */
    public function create(string $providerName, array $config = []): AIProviderInterface
    {
        if (!isset($this->providers[$providerName])) {
            throw new \InvalidArgumentException("Unknown AI provider: {$providerName}");
        }

        $providerClass = $this->providers[$providerName];
        $provider = new $providerClass($this->loggingService);

        if (!$provider instanceof AIProviderInterface) {
            throw new \RuntimeException("Provider {$providerName} does not implement AIProviderInterface");
        }

        if (!empty($config)) {
            $provider->initialize($config);
        }

        return $provider;
    }

    /**
     * Create provider from database configuration
     */
    public function createFromModel(AIProvider $aiProvider): AIProviderInterface
    {
        $config = [
            'api_key' => decrypt($aiProvider->api_key),
            'api_endpoint' => $aiProvider->api_endpoint,
            'models' => $aiProvider->models,
            'capabilities' => $aiProvider->capabilities,
            'rate_limits' => $aiProvider->rate_limits,
        ];

        $provider = $this->create($aiProvider->provider_name, $config);

        $this->loggingService->logAIProvider('provider_created_from_model', [
            'provider_name' => $aiProvider->provider_name,
            'tenant_id' => $aiProvider->tenant_id,
            'provider_id' => $aiProvider->id,
        ]);

        return $provider;
    }

    /**
     * Get all available provider names
     */
    public function getAvailableProviders(): array
    {
        return array_keys($this->providers);
    }

    /**
     * Check if a provider is available
     */
    public function isProviderAvailable(string $providerName): bool
    {
        return isset($this->providers[$providerName]);
    }

    /**
     * Get provider capabilities without instantiation
     */
    public function getProviderCapabilities(string $providerName): array
    {
        if (!$this->isProviderAvailable($providerName)) {
            return [];
        }

        $providerClass = $this->providers[$providerName];

        // Get static capabilities if available
        if (method_exists($providerClass, 'getStaticCapabilities')) {
            return $providerClass::getStaticCapabilities();
        }

        // Fallback to creating instance
        try {
            $provider = new $providerClass($this->loggingService);
            return $provider->getCapabilities();
        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('provider_capabilities_error', [
                'provider_name' => $providerName,
                'error' => $e->getMessage(),
            ], 'error');
            return [];
        }
    }

    /**
     * Test all providers health
     */
    public function testAllProviders(string $tenantId): array
    {
        $results = [];

        foreach ($this->providers as $providerName => $providerClass) {
            try {
                // Get provider configuration from database
                $aiProvider = AIProvider::where('tenant_id', $tenantId)
                    ->where('provider_name', $providerName)
                    ->where('status', 'active')
                    ->first();

                if (!$aiProvider) {
                    $results[$providerName] = [
                        'status' => 'not_configured',
                        'message' => 'Provider not configured for this tenant',
                    ];
                    continue;
                }

                $provider = $this->createFromModel($aiProvider);
                $healthStatus = $provider->getHealthStatus();

                $results[$providerName] = $healthStatus;

            } catch (\Exception $e) {
                $results[$providerName] = [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];

                $this->loggingService->logAIProvider('provider_health_test_error', [
                    'provider_name' => $providerName,
                    'tenant_id' => $tenantId,
                    'error' => $e->getMessage(),
                ], 'error');
            }
        }

        return $results;
    }

    /**
     * Get best available provider for a capability
     */
    public function getBestProviderForCapability(string $tenantId, string $capability): ?AIProviderInterface
    {
        $availableProviders = AIProvider::where('tenant_id', $tenantId)
            ->where('status', 'active')
            ->get();

        $bestProvider = null;
        $bestScore = 0;

        foreach ($availableProviders as $aiProvider) {
            try {
                $provider = $this->createFromModel($aiProvider);
                $capabilities = $provider->getCapabilities();

                if (in_array($capability, $capabilities)) {
                    // Simple scoring based on provider priority and health
                    $score = $this->calculateProviderScore($provider, $aiProvider);

                    if ($score > $bestScore) {
                        $bestScore = $score;
                        $bestProvider = $provider;
                    }
                }
            } catch (\Exception $e) {
                $this->loggingService->logAIProvider('provider_selection_error', [
                    'provider_name' => $aiProvider->provider_name,
                    'tenant_id' => $tenantId,
                    'capability' => $capability,
                    'error' => $e->getMessage(),
                ], 'error');
            }
        }

        return $bestProvider;
    }

    /**
     * Calculate provider score for selection
     */
    protected function calculateProviderScore(AIProviderInterface $provider, AIProvider $aiProvider): int
    {
        $score = 0;

        // Base score
        $score += 10;

        // Default provider bonus
        if ($aiProvider->is_default) {
            $score += 20;
        }

        // Health status bonus
        $healthStatus = $provider->getHealthStatus();
        if (($healthStatus['status'] ?? '') === 'healthy') {
            $score += 15;
        }

        // Provider-specific bonuses
        switch ($provider->getName()) {
            case 'openai':
                $score += 5; // Reliable and widely supported
                break;
            case 'groq':
                $score += 3; // Fast inference
                break;
            case 'anthropic':
                $score += 4; // High quality responses
                break;
        }

        return $score;
    }
}
