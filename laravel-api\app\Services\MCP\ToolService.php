<?php

namespace App\Services\MCP;

use App\Models\Tool;
use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class ToolService extends BaseService
{
    /**
     * Available tool types and their configurations
     */
    protected array $toolTypes = [
        'api_call' => [
            'required_config' => ['url', 'method'],
            'optional_config' => ['headers', 'timeout', 'retry_attempts'],
            'execution_method' => 'executeApiCall',
        ],
        'database_query' => [
            'required_config' => ['connection', 'query_type'],
            'optional_config' => ['timeout', 'max_rows'],
            'execution_method' => 'executeDatabaseQuery',
        ],
        'file_operation' => [
            'required_config' => ['operation_type', 'file_path'],
            'optional_config' => ['permissions', 'backup'],
            'execution_method' => 'executeFileOperation',
        ],
        'email_sender' => [
            'required_config' => ['smtp_config', 'template'],
            'optional_config' => ['attachments', 'priority'],
            'execution_method' => 'executeEmailSender',
        ],
        'webhook' => [
            'required_config' => ['url', 'payload_template'],
            'optional_config' => ['headers', 'timeout', 'retry_policy'],
            'execution_method' => 'executeWebhook',
        ],
        'data_processor' => [
            'required_config' => ['processor_type', 'input_format'],
            'optional_config' => ['output_format', 'validation_rules'],
            'execution_method' => 'executeDataProcessor',
        ],
        'search_engine' => [
            'required_config' => ['search_provider', 'api_key'],
            'optional_config' => ['max_results', 'filters'],
            'execution_method' => 'executeSearchEngine',
        ],
        'ai_model' => [
            'required_config' => ['model_provider', 'model_name'],
            'optional_config' => ['temperature', 'max_tokens', 'system_prompt'],
            'execution_method' => 'executeAiModel',
        ],
    ];

    /**
     * Tool execution status tracking
     */
    protected array $executionHistory = [];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'tool_service';
    }

    /**
     * Execute a tool with given parameters
     */
    public function executeTool(string $toolName, array $parameters = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('execute_tool', function () use ($toolName, $parameters) {
            // Get tool configuration
            $tool = $this->getToolConfiguration($toolName);

            // Validate tool permissions
            $this->validateToolPermissions($tool, $parameters);

            // Prepare execution context
            $executionId = $this->generateExecutionId();
            $context = $this->prepareExecutionContext($tool, $parameters, $executionId);

            try {
                // Execute the tool
                $result = $this->performToolExecution($tool, $context);

                // Log successful execution
                $this->logToolExecution($executionId, $tool, $parameters, $result, 'success');

                return [
                    'execution_id' => $executionId,
                    'tool_name' => $toolName,
                    'status' => 'success',
                    'result' => $result,
                    'execution_time_ms' => $context['execution_time_ms'] ?? 0,
                    'metadata' => $context['metadata'] ?? [],
                ];
            } catch (\Exception $e) {
                // Log failed execution
                $this->logToolExecution($executionId, $tool, $parameters, null, 'failed', $e->getMessage());

                // Apply error recovery if configured
                $recoveryResult = $this->attemptErrorRecovery($tool, $context, $e);

                if ($recoveryResult) {
                    return $recoveryResult;
                }

                throw $e;
            }
        }, [
            'tool_name' => $toolName,
            'parameter_count' => count($parameters),
        ]);
    }

    /**
     * Register a new tool
     */
    public function registerTool(array $toolData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_tools');

        return $this->executeWithTracking('register_tool', function () use ($toolData) {
            // Validate tool configuration
            $this->validateToolConfiguration($toolData);

            return $this->executeInTransaction(function () use ($toolData) {
                $tool = Tool::create([
                    'tenant_id' => $this->getCurrentTenantId(),
                    'user_id' => $this->getCurrentUserId(),
                    'name' => $toolData['name'],
                    'description' => $toolData['description'],
                    'tool_type' => $toolData['tool_type'],
                    'configuration' => $toolData['configuration'],
                    'parameters_schema' => $toolData['parameters_schema'] ?? [],
                    'permissions_required' => $toolData['permissions_required'] ?? [],
                    'rate_limit_per_minute' => $toolData['rate_limit_per_minute'] ?? 60,
                    'timeout_seconds' => $toolData['timeout_seconds'] ?? 30,
                    'retry_attempts' => $toolData['retry_attempts'] ?? 3,
                    'is_enabled' => $toolData['is_enabled'] ?? true,
                    'metadata' => $toolData['metadata'] ?? [],
                ]);

                $this->logActivity('Tool registered', [
                    'tool_id' => $tool->id,
                    'tool_name' => $tool->name,
                    'tool_type' => $tool->tool_type,
                ]);

                return [
                    'tool_id' => $tool->id,
                    'tool_name' => $tool->name,
                    'status' => 'registered',
                ];
            });
        }, [
            'tool_type' => $toolData['tool_type'],
            'tool_name' => $toolData['name'],
        ]);
    }

    /**
     * Update tool configuration
     */
    public function updateTool(string $toolId, array $updateData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_tools');

        return $this->executeWithTracking('update_tool', function () use ($toolId, $updateData) {
            $tool = Tool::where('tenant_id', $this->getCurrentTenantId())
                       ->findOrFail($toolId);

            // Validate updated configuration if provided
            if (isset($updateData['configuration'])) {
                $this->validateToolTypeConfiguration($tool->tool_type, $updateData['configuration']);
            }

            $tool->update($updateData);

            $this->logActivity('Tool updated', [
                'tool_id' => $tool->id,
                'tool_name' => $tool->name,
                'updated_fields' => array_keys($updateData),
            ]);

            return [
                'tool_id' => $tool->id,
                'tool_name' => $tool->name,
                'status' => 'updated',
            ];
        }, [
            'tool_id' => $toolId,
            'update_fields' => array_keys($updateData),
        ]);
    }

    /**
     * Get available tools for tenant
     */
    public function getAvailableTools(array $filters = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('get_available_tools', function () use ($filters) {
            $query = Tool::where('tenant_id', $this->getCurrentTenantId())
                        ->where('is_enabled', true);

            // Apply filters
            if (isset($filters['tool_type'])) {
                $query->where('tool_type', $filters['tool_type']);
            }

            if (isset($filters['name'])) {
                $query->where('name', 'like', '%' . $filters['name'] . '%');
            }

            if (isset($filters['permissions'])) {
                foreach ($filters['permissions'] as $permission) {
                    $query->whereJsonContains('permissions_required', $permission);
                }
            }

            $tools = $query->orderBy('name')->get();

            return [
                'tools' => $tools->map(function ($tool) {
                    return [
                        'id' => $tool->id,
                        'name' => $tool->name,
                        'description' => $tool->description,
                        'tool_type' => $tool->tool_type,
                        'parameters_schema' => $tool->parameters_schema,
                        'permissions_required' => $tool->permissions_required,
                        'rate_limit_per_minute' => $tool->rate_limit_per_minute,
                        'timeout_seconds' => $tool->timeout_seconds,
                        'metadata' => $tool->metadata,
                    ];
                })->toArray(),
                'total_count' => $tools->count(),
                'tool_types_available' => array_keys($this->toolTypes),
            ];
        }, [
            'filters_applied' => count($filters),
        ]);
    }

    /**
     * Get tool execution history
     */
    public function getExecutionHistory(array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('get_execution_history', function () use ($options) {
            $dateFrom = $options['date_from'] ?? now()->subDays(7);
            $dateTo = $options['date_to'] ?? now();
            $limit = $options['limit'] ?? 100;

            // Get execution logs from system logs
            $executionLogs = $this->loggingService->getLogs([
                'tenant_id' => $this->getCurrentTenantId(),
                'log_type' => 'service',
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
            ], $limit);

            // Filter for tool executions
            $toolExecutions = array_filter($executionLogs, function ($log) {
                return isset($log['context']['tool_execution']) && $log['context']['tool_execution'] === true;
            });

            // Calculate statistics
            $totalExecutions = count($toolExecutions);
            $successfulExecutions = count(array_filter($toolExecutions, fn($log) => $log['context']['status'] === 'success'));
            $failedExecutions = $totalExecutions - $successfulExecutions;

            // Group by tool name
            $executionsByTool = [];
            foreach ($toolExecutions as $execution) {
                $toolName = $execution['context']['tool_name'] ?? 'unknown';
                $executionsByTool[$toolName] = ($executionsByTool[$toolName] ?? 0) + 1;
            }

            return [
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo,
                ],
                'statistics' => [
                    'total_executions' => $totalExecutions,
                    'successful_executions' => $successfulExecutions,
                    'failed_executions' => $failedExecutions,
                    'success_rate' => $totalExecutions > 0 ? round($successfulExecutions / $totalExecutions * 100, 2) : 0,
                ],
                'executions_by_tool' => $executionsByTool,
                'recent_executions' => array_slice($toolExecutions, 0, 20),
            ];
        }, [
            'date_range_days' => $options['date_from'] ? now()->diffInDays($options['date_from']) : 7,
            'limit' => $options['limit'] ?? 100,
        ]);
    }

    /**
     * Test tool configuration
     */
    public function testTool(string $toolId, array $testParameters = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('test_tools');

        return $this->executeWithTracking('test_tool', function () use ($toolId, $testParameters) {
            $tool = Tool::where('tenant_id', $this->getCurrentTenantId())
                       ->findOrFail($toolId);

            // Use test parameters or default test parameters
            $parameters = !empty($testParameters) ? $testParameters : $this->generateTestParameters($tool);

            // Execute tool in test mode
            $testContext = $this->prepareExecutionContext($tool, $parameters, 'TEST_' . time());
            $testContext['test_mode'] = true;

            try {
                $result = $this->performToolExecution($tool, $testContext);

                return [
                    'tool_id' => $tool->id,
                    'tool_name' => $tool->name,
                    'test_status' => 'passed',
                    'test_result' => $result,
                    'test_parameters' => $parameters,
                    'execution_time_ms' => $testContext['execution_time_ms'] ?? 0,
                ];
            } catch (\Exception $e) {
                return [
                    'tool_id' => $tool->id,
                    'tool_name' => $tool->name,
                    'test_status' => 'failed',
                    'error_message' => $e->getMessage(),
                    'test_parameters' => $parameters,
                ];
            }
        }, [
            'tool_id' => $toolId,
            'test_parameters_provided' => !empty($testParameters),
        ]);
    }

    /**
     * Get tool configuration
     */
    protected function getToolConfiguration(string $toolName): Tool
    {
        $tool = Tool::where('tenant_id', $this->getCurrentTenantId())
                   ->where('name', $toolName)
                   ->where('is_enabled', true)
                   ->first();

        if (!$tool) {
            throw new \InvalidArgumentException("Tool not found or disabled: {$toolName}");
        }

        return $tool;
    }

    /**
     * Validate tool permissions
     */
    protected function validateToolPermissions(Tool $tool, array $parameters): void
    {
        $requiredPermissions = $tool->permissions_required ?? [];

        foreach ($requiredPermissions as $permission) {
            if (!$this->hasPermission($permission)) {
                throw $this->errorHandlingService->createPermissionException($permission);
            }
        }

        // Check rate limiting
        $this->checkRateLimit($tool);
    }

    /**
     * Check rate limiting for tool
     */
    protected function checkRateLimit(Tool $tool): void
    {
        $rateLimit = $tool->rate_limit_per_minute;
        if ($rateLimit <= 0) {
            return; // No rate limiting
        }

        $cacheKey = "tool_rate_limit:{$tool->id}:{$this->getCurrentTenantId()}";
        $currentCount = $this->getFromCache($cacheKey, 0);

        if ($currentCount >= $rateLimit) {
            throw new \Exception("Rate limit exceeded for tool {$tool->name}. Limit: {$rateLimit} per minute");
        }

        // Increment counter
        $this->putToCache($cacheKey, $currentCount + 1, 60); // 1 minute TTL
    }

    /**
     * Prepare execution context
     */
    protected function prepareExecutionContext(Tool $tool, array $parameters, string $executionId): array
    {
        return [
            'execution_id' => $executionId,
            'tool' => $tool,
            'parameters' => $parameters,
            'tenant_id' => $this->getCurrentTenantId(),
            'user_id' => $this->getCurrentUserId(),
            'start_time' => microtime(true),
            'timeout' => $tool->timeout_seconds,
            'retry_attempts' => $tool->retry_attempts,
            'metadata' => [],
            'test_mode' => false,
        ];
    }

    /**
     * Perform tool execution
     */
    protected function performToolExecution(Tool $tool, array $context): array
    {
        $toolType = $tool->tool_type;
        $executionMethod = $this->toolTypes[$toolType]['execution_method'] ?? null;

        if (!$executionMethod || !method_exists($this, $executionMethod)) {
            throw new \InvalidArgumentException("Unsupported tool type: {$toolType}");
        }

        $startTime = microtime(true);

        try {
            $result = $this->$executionMethod($tool, $context);
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $context['execution_time_ms'] = $executionTime;

            return $result;
        } catch (\Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $context['execution_time_ms'] = $executionTime;
            throw $e;
        }
    }

    /**
     * Execute API call tool
     */
    protected function executeApiCall(Tool $tool, array $context): array
    {
        $config = $tool->configuration;
        $parameters = $context['parameters'];

        $url = $this->interpolateTemplate($config['url'], $parameters);
        $method = strtoupper($config['method']);
        $headers = $config['headers'] ?? [];
        $timeout = $config['timeout'] ?? $tool->timeout_seconds;

        // Prepare HTTP request
        $httpClient = Http::timeout($timeout);

        if (!empty($headers)) {
            $httpClient = $httpClient->withHeaders($headers);
        }

        // Add request body if needed
        $body = $parameters['body'] ?? null;
        if ($body && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $httpClient = $httpClient->withBody($body, $headers['Content-Type'] ?? 'application/json');
        }

        // Execute request
        $response = $httpClient->send($method, $url);

        return [
            'status_code' => $response->status(),
            'headers' => $response->headers(),
            'body' => $response->json() ?? $response->body(),
            'success' => $response->successful(),
            'url' => $url,
            'method' => $method,
        ];
    }

    /**
     * Execute database query tool
     */
    protected function executeDatabaseQuery(Tool $tool, array $context): array
    {
        $config = $tool->configuration;
        $parameters = $context['parameters'];

        $connection = $config['connection'] ?? 'default';
        $queryType = $config['query_type']; // 'select', 'insert', 'update', 'delete'
        $query = $this->interpolateTemplate($parameters['query'], $parameters);
        $maxRows = $config['max_rows'] ?? 1000;

        // Validate query type for security
        $this->validateDatabaseQuery($query, $queryType);

        // Execute query based on type
        switch ($queryType) {
            case 'select':
                $results = \DB::connection($connection)->select($query);
                return [
                    'query_type' => 'select',
                    'results' => array_slice($results, 0, $maxRows),
                    'row_count' => count($results),
                    'limited' => count($results) > $maxRows,
                ];

            case 'insert':
            case 'update':
            case 'delete':
                $affectedRows = \DB::connection($connection)->affectingStatement($query);
                return [
                    'query_type' => $queryType,
                    'affected_rows' => $affectedRows,
                    'success' => true,
                ];

            default:
                throw new \InvalidArgumentException("Unsupported query type: {$queryType}");
        }
    }

    /**
     * Execute file operation tool
     */
    protected function executeFileOperation(Tool $tool, array $context): array
    {
        $config = $tool->configuration;
        $parameters = $context['parameters'];

        $operationType = $config['operation_type']; // 'read', 'write', 'delete', 'copy', 'move'
        $filePath = $this->interpolateTemplate($config['file_path'], $parameters);

        // Validate file path for security
        $this->validateFilePath($filePath);

        switch ($operationType) {
            case 'read':
                if (!file_exists($filePath)) {
                    throw new \Exception("File not found: {$filePath}");
                }
                return [
                    'operation' => 'read',
                    'file_path' => $filePath,
                    'content' => file_get_contents($filePath),
                    'file_size' => filesize($filePath),
                ];

            case 'write':
                $content = $parameters['content'] ?? '';
                $bytesWritten = file_put_contents($filePath, $content);
                return [
                    'operation' => 'write',
                    'file_path' => $filePath,
                    'bytes_written' => $bytesWritten,
                    'success' => $bytesWritten !== false,
                ];

            case 'delete':
                $success = unlink($filePath);
                return [
                    'operation' => 'delete',
                    'file_path' => $filePath,
                    'success' => $success,
                ];

            default:
                throw new \InvalidArgumentException("Unsupported file operation: {$operationType}");
        }
    }

    /**
     * Execute email sender tool
     */
    protected function executeEmailSender(Tool $tool, array $context): array
    {
        $config = $tool->configuration;
        $parameters = $context['parameters'];

        // This would integrate with actual email service
        // For now, return a mock response
        return [
            'operation' => 'email_send',
            'to' => $parameters['to'] ?? 'unknown',
            'subject' => $parameters['subject'] ?? 'No subject',
            'status' => 'sent',
            'message_id' => 'mock_' . time(),
        ];
    }

    /**
     * Execute webhook tool
     */
    protected function executeWebhook(Tool $tool, array $context): array
    {
        $config = $tool->configuration;
        $parameters = $context['parameters'];

        $url = $config['url'];
        $payloadTemplate = $config['payload_template'];
        $headers = $config['headers'] ?? ['Content-Type' => 'application/json'];
        $timeout = $config['timeout'] ?? $tool->timeout_seconds;

        // Prepare payload
        $payload = $this->interpolateTemplate($payloadTemplate, $parameters);

        // Send webhook
        $response = Http::timeout($timeout)
                       ->withHeaders($headers)
                       ->post($url, json_decode($payload, true));

        return [
            'operation' => 'webhook',
            'url' => $url,
            'status_code' => $response->status(),
            'success' => $response->successful(),
            'response_body' => $response->body(),
        ];
    }

    /**
     * Execute data processor tool
     */
    protected function executeDataProcessor(Tool $tool, array $context): array
    {
        $config = $tool->configuration;
        $parameters = $context['parameters'];

        $processorType = $config['processor_type']; // 'json_transform', 'csv_parse', 'xml_parse', etc.
        $inputData = $parameters['input_data'];

        switch ($processorType) {
            case 'json_transform':
                $transformRules = $config['transform_rules'] ?? [];
                $result = $this->transformJsonData($inputData, $transformRules);
                break;

            case 'csv_parse':
                $delimiter = $config['delimiter'] ?? ',';
                $result = $this->parseCsvData($inputData, $delimiter);
                break;

            default:
                throw new \InvalidArgumentException("Unsupported processor type: {$processorType}");
        }

        return [
            'operation' => 'data_processing',
            'processor_type' => $processorType,
            'input_size' => strlen($inputData),
            'output_size' => strlen(json_encode($result)),
            'result' => $result,
        ];
    }

    /**
     * Execute search engine tool
     */
    protected function executeSearchEngine(Tool $tool, array $context): array
    {
        $config = $tool->configuration;
        $parameters = $context['parameters'];

        $searchProvider = $config['search_provider']; // 'google', 'bing', 'duckduckgo'
        $query = $parameters['query'];
        $maxResults = $config['max_results'] ?? 10;

        // This would integrate with actual search APIs
        // For now, return a mock response
        return [
            'operation' => 'search',
            'provider' => $searchProvider,
            'query' => $query,
            'results' => [
                [
                    'title' => 'Mock Search Result 1',
                    'url' => 'https://example.com/1',
                    'snippet' => 'This is a mock search result for: ' . $query,
                ],
                [
                    'title' => 'Mock Search Result 2',
                    'url' => 'https://example.com/2',
                    'snippet' => 'Another mock result for: ' . $query,
                ],
            ],
            'total_results' => 2,
        ];
    }

    /**
     * Execute AI model tool
     */
    protected function executeAiModel(Tool $tool, array $context): array
    {
        $config = $tool->configuration;
        $parameters = $context['parameters'];

        $modelProvider = $config['model_provider']; // 'openai', 'anthropic', 'groq', etc.
        $modelName = $config['model_name'];
        $prompt = $parameters['prompt'];

        // This would integrate with actual AI providers
        // For now, return a mock response
        return [
            'operation' => 'ai_inference',
            'provider' => $modelProvider,
            'model' => $modelName,
            'prompt' => $prompt,
            'response' => 'This is a mock AI response to: ' . substr($prompt, 0, 50) . '...',
            'tokens_used' => strlen($prompt) + 50, // Mock token count
        ];
    }

    /**
     * Interpolate template with parameters
     */
    protected function interpolateTemplate(string $template, array $parameters): string
    {
        foreach ($parameters as $key => $value) {
            $template = str_replace("{{$key}}", $value, $template);
        }

        return $template;
    }

    /**
     * Validate database query for security
     */
    protected function validateDatabaseQuery(string $query, string $expectedType): void
    {
        $query = strtolower(trim($query));
        $queryType = explode(' ', $query)[0];

        if ($queryType !== strtolower($expectedType)) {
            throw new \Exception("Query type mismatch. Expected: {$expectedType}, Got: {$queryType}");
        }

        // Check for dangerous operations
        $dangerousPatterns = ['drop', 'truncate', 'alter', 'create', 'grant', 'revoke'];
        foreach ($dangerousPatterns as $pattern) {
            if (str_contains($query, $pattern)) {
                throw new \Exception("Dangerous SQL operation detected: {$pattern}");
            }
        }
    }

    /**
     * Validate file path for security
     */
    protected function validateFilePath(string $filePath): void
    {
        // Prevent directory traversal
        if (str_contains($filePath, '..') || str_contains($filePath, '~')) {
            throw new \Exception("Invalid file path: directory traversal detected");
        }

        // Ensure path is within allowed directories
        $allowedPaths = $this->getConfig('allowed_file_paths', [storage_path()]);
        $realPath = realpath(dirname($filePath));

        $isAllowed = false;
        foreach ($allowedPaths as $allowedPath) {
            if (str_starts_with($realPath, realpath($allowedPath))) {
                $isAllowed = true;
                break;
            }
        }

        if (!$isAllowed) {
            throw new \Exception("File path not allowed: {$filePath}");
        }
    }

    /**
     * Transform JSON data based on rules
     */
    protected function transformJsonData(string $jsonData, array $transformRules): array
    {
        $data = json_decode($jsonData, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("Invalid JSON data");
        }

        // Apply transformation rules
        foreach ($transformRules as $rule) {
            // Simple transformation logic - can be extended
            if ($rule['type'] === 'rename_field') {
                $data[$rule['new_name']] = $data[$rule['old_name']] ?? null;
                unset($data[$rule['old_name']]);
            }
        }

        return $data;
    }

    /**
     * Parse CSV data
     */
    protected function parseCsvData(string $csvData, string $delimiter): array
    {
        $lines = explode("\n", trim($csvData));
        $result = [];

        foreach ($lines as $line) {
            $result[] = str_getcsv($line, $delimiter);
        }

        return $result;
    }

    /**
     * Generate test parameters for tool
     */
    protected function generateTestParameters(Tool $tool): array
    {
        $schema = $tool->parameters_schema ?? [];
        $testParams = [];

        foreach ($schema as $paramName => $paramConfig) {
            $testParams[$paramName] = $paramConfig['test_value'] ?? $this->generateTestValue($paramConfig['type'] ?? 'string');
        }

        return $testParams;
    }

    /**
     * Generate test value based on type
     */
    protected function generateTestValue(string $type): mixed
    {
        return match ($type) {
            'string' => 'test_value',
            'integer' => 42,
            'boolean' => true,
            'array' => ['test', 'array'],
            'object' => ['test' => 'object'],
            default => 'test_value',
        };
    }

    /**
     * Validate tool configuration
     */
    protected function validateToolConfiguration(array $toolData): void
    {
        $requiredFields = ['name', 'tool_type', 'configuration'];
        foreach ($requiredFields as $field) {
            if (!isset($toolData[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        $toolType = $toolData['tool_type'];
        if (!isset($this->toolTypes[$toolType])) {
            throw new \InvalidArgumentException("Unsupported tool type: {$toolType}");
        }

        $this->validateToolTypeConfiguration($toolType, $toolData['configuration']);
    }

    /**
     * Validate tool type specific configuration
     */
    protected function validateToolTypeConfiguration(string $toolType, array $configuration): void
    {
        $typeConfig = $this->toolTypes[$toolType];
        $requiredConfig = $typeConfig['required_config'];

        foreach ($requiredConfig as $configKey) {
            if (!isset($configuration[$configKey])) {
                throw new \InvalidArgumentException("Missing required configuration for {$toolType}: {$configKey}");
            }
        }
    }

    /**
     * Attempt error recovery
     */
    protected function attemptErrorRecovery(Tool $tool, array $context, \Exception $exception): ?array
    {
        $retryAttempts = $context['retry_attempts'] ?? 0;
        $currentAttempt = $context['current_attempt'] ?? 0;

        if ($currentAttempt < $retryAttempts) {
            $context['current_attempt'] = $currentAttempt + 1;

            // Wait before retry
            sleep(1);

            try {
                return $this->performToolExecution($tool, $context);
            } catch (\Exception $retryException) {
                // If retry also fails, return null to indicate no recovery
                return null;
            }
        }

        return null;
    }

    /**
     * Log tool execution
     */
    protected function logToolExecution(string $executionId, Tool $tool, array $parameters, ?array $result, string $status, ?string $errorMessage = null): void
    {
        $this->logActivity('Tool execution', [
            'tool_execution' => true,
            'execution_id' => $executionId,
            'tool_id' => $tool->id,
            'tool_name' => $tool->name,
            'tool_type' => $tool->tool_type,
            'status' => $status,
            'parameter_count' => count($parameters),
            'result_size' => $result ? strlen(json_encode($result)) : 0,
            'error_message' => $errorMessage,
        ], $status === 'failed' ? 'error' : 'info');
    }

    /**
     * Generate execution ID
     */
    protected function generateExecutionId(): string
    {
        return 'TOOL_' . strtoupper(Str::random(8)) . '_' . time();
    }
}
