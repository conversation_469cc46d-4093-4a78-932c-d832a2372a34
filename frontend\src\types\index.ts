// Axient MCP++ Frontend Types

// ============================================================================
// Authentication & User Types
// ============================================================================

export interface User {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'user' | 'viewer';
    tenant_id: string;
    avatar?: string;
    created_at: string;
    updated_at: string;
    last_login_at?: string;
    two_factor_enabled: boolean;
}

export interface Tenant {
    id: string;
    name: string;
    domain: string;
    settings: TenantSettings;
    subscription_status: 'active' | 'inactive' | 'trial' | 'suspended';
    created_at: string;
    updated_at: string;
}

export interface TenantSettings {
    max_users: number;
    max_ai_providers: number;
    max_monthly_tokens: number;
    features: string[];
    branding?: {
        logo?: string;
        primary_color?: string;
        secondary_color?: string;
    };
}

export interface AuthResponse {
    user: User;
    token: string;
    expires_at: string;
    tenant: Tenant;
}

// ============================================================================
// AI Provider Types
// ============================================================================

export interface AIProvider {
    id: string;
    provider_name: string;
    display_name: string;
    api_key: string; // Encrypted on backend
    api_endpoint?: string;
    models: string[];
    capabilities: AICapability[];
    rate_limits: RateLimit;
    is_default: boolean;
    status: 'active' | 'inactive' | 'error';
    health_status?: HealthStatus;
    created_at: string;
    updated_at: string;
}

export type AICapability =
    | 'text_generation'
    | 'chat'
    | 'embeddings'
    | 'function_calling'
    | 'streaming'
    | 'vision'
    | 'code_generation'
    | 'multimodal'
    | 'real_time_information'
    | 'high_speed_inference'
    | 'custom_models'
    | 'open_source'
    | 'classification'
    | 'translation'
    | 'summarization'
    | 'european_compliance'
    | 'multilingual'
    | 'fill_in_middle'
    | 'programming_languages'
    | 'code_explanation'
    | 'debugging'
    | 'long_context'
    | 'safety'
    | 'constitutional_ai'
    | 'model_aggregation'
    | 'cost_optimization'
    | 'provider_comparison'
    | 'humor'
    | 'rebellious_responses'
    | 'x_twitter_integration'
    | 'current_events';

export interface RateLimit {
    requests_per_minute: number;
    tokens_per_minute: number;
    requests_per_day: number;
}

export interface HealthStatus {
    status: 'healthy' | 'unhealthy' | 'degraded';
    response_time_ms?: number;
    message: string;
    timestamp: string;
    error_code?: number;
}

export interface AIModel {
    id: string;
    name: string;
    description: string;
    capabilities: AICapability[];
    context_length: number;
    cost_per_token: number;
    provider?: string;
}

// ============================================================================
// Chat & Messaging Types
// ============================================================================

export interface ChatSession {
    id: string;
    title: string;
    user_id: string;
    tenant_id: string;
    ai_provider_id?: string;
    model?: string;
    system_prompt?: string;
    settings: ChatSettings;
    status: 'active' | 'archived' | 'deleted';
    message_count: number;
    token_usage: TokenUsage;
    created_at: string;
    updated_at: string;
    last_message_at?: string;
}

export interface ChatMessage {
    id: string;
    session_id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    metadata?: MessageMetadata;
    token_usage?: TokenUsage;
    processing_time_ms?: number;
    created_at: string;
}

export interface MessageMetadata {
    provider_name?: string;
    model?: string;
    finish_reason?: string;
    function_calls?: FunctionCall[];
    attachments?: Attachment[];
    error?: string;
}

export interface FunctionCall {
    name: string;
    arguments: Record<string, any>;
    result?: any;
}

export interface Attachment {
    id: string;
    name: string;
    type: string;
    size: number;
    url: string;
}

export interface ChatSettings {
    temperature: number;
    max_tokens: number;
    top_p?: number;
    frequency_penalty?: number;
    presence_penalty?: number;
    stop_sequences?: string[];
    stream: boolean;
}

export interface TokenUsage {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    cost?: number;
}

// ============================================================================
// WebSocket Types
// ============================================================================

export interface WebSocketSession {
    id: string;
    user_id: string;
    tenant_id: string;
    channel: string;
    status: 'connected' | 'disconnected' | 'reconnecting';
    connected_at: string;
    last_activity_at: string;
    metadata?: Record<string, any>;
}

export interface WebSocketEvent {
    type: WebSocketEventType;
    payload: any;
    timestamp: string;
    session_id?: string;
    user_id?: string;
}

export type WebSocketEventType =
    | 'chat.message.received'
    | 'chat.message.processed'
    | 'chat.typing.start'
    | 'chat.typing.stop'
    | 'user.presence.online'
    | 'user.presence.offline'
    | 'ai.provider.request'
    | 'ai.provider.response'
    | 'system.notification'
    | 'error';

export interface TypingIndicator {
    user_id: string;
    session_id: string;
    is_typing: boolean;
    timestamp: string;
}

export interface UserPresence {
    user_id: string;
    status: 'online' | 'offline' | 'away' | 'busy';
    last_seen: string;
    current_session?: string;
}

// ============================================================================
// Tool & Workflow Types
// ============================================================================

export interface Tool {
    id: string;
    name: string;
    description: string;
    type: ToolType;
    configuration: Record<string, any>;
    input_schema: JSONSchema;
    output_schema: JSONSchema;
    is_enabled: boolean;
    created_at: string;
    updated_at: string;
}

export type ToolType =
    | 'api_call'
    | 'database_query'
    | 'file_processor'
    | 'web_scraper'
    | 'email_sender'
    | 'custom_function';

export interface Workflow {
    id: string;
    name: string;
    description: string;
    steps: WorkflowStep[];
    triggers: WorkflowTrigger[];
    is_enabled: boolean;
    execution_count: number;
    last_executed_at?: string;
    created_at: string;
    updated_at: string;
}

export interface WorkflowStep {
    id: string;
    type: 'tool' | 'condition' | 'ai_generation' | 'delay';
    configuration: Record<string, any>;
    next_steps: string[];
    error_handling?: ErrorHandling;
}

export interface WorkflowTrigger {
    type: 'manual' | 'schedule' | 'webhook' | 'event';
    configuration: Record<string, any>;
}

export interface ErrorHandling {
    strategy: 'retry' | 'skip' | 'fail' | 'fallback';
    max_retries?: number;
    fallback_step?: string;
}

// ============================================================================
// Document & Knowledge Base Types
// ============================================================================

export interface Document {
    id: string;
    name: string;
    type: string;
    size: number;
    content?: string;
    metadata: DocumentMetadata;
    processing_status: 'pending' | 'processing' | 'completed' | 'failed';
    embedding_status: 'pending' | 'processing' | 'completed' | 'failed';
    created_at: string;
    updated_at: string;
}

export interface DocumentMetadata {
    mime_type: string;
    encoding?: string;
    language?: string;
    page_count?: number;
    word_count?: number;
    tags?: string[];
    source?: string;
}

export interface Embedding {
    id: string;
    document_id: string;
    chunk_index: number;
    content: string;
    vector: number[];
    metadata: Record<string, any>;
    created_at: string;
}

// ============================================================================
// Monitoring & Analytics Types
// ============================================================================

export interface SystemHealth {
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: ServiceHealth[];
    metrics: SystemMetrics;
    timestamp: string;
}

export interface ServiceHealth {
    name: string;
    status: 'healthy' | 'degraded' | 'unhealthy';
    response_time_ms?: number;
    error_rate?: number;
    last_check: string;
}

export interface SystemMetrics {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    active_connections: number;
    requests_per_minute: number;
    error_rate: number;
}

export interface LogEntry {
    id: string;
    level: 'debug' | 'info' | 'warning' | 'error' | 'critical';
    channel: string;
    message: string;
    context: Record<string, any>;
    timestamp: string;
    user_id?: string;
    tenant_id?: string;
}

export interface UsageStatistics {
    period: string;
    total_requests: number;
    total_tokens: number;
    total_cost: number;
    by_provider: Record<string, ProviderUsage>;
    by_model: Record<string, ModelUsage>;
    by_user: Record<string, UserUsage>;
}

export interface ProviderUsage {
    requests: number;
    tokens: number;
    cost: number;
    average_response_time: number;
    error_rate: number;
}

export interface ModelUsage {
    requests: number;
    tokens: number;
    cost: number;
    provider: string;
}

export interface UserUsage {
    requests: number;
    tokens: number;
    cost: number;
    sessions: number;
}

// ============================================================================
// API Response Types
// ============================================================================

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    errors?: Record<string, string[]>;
    meta?: {
        pagination?: Pagination;
        total?: number;
        page?: number;
        per_page?: number;
    };
}

export interface Pagination {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

export interface ErrorResponse {
    success: false;
    message: string;
    errors?: Record<string, string[]>;
    code?: string;
    details?: any;
}

// ============================================================================
// Utility Types
// ============================================================================

export interface JSONSchema {
    type: string;
    properties?: Record<string, JSONSchema>;
    required?: string[];
    items?: JSONSchema;
    enum?: any[];
    description?: string;
}

export interface SelectOption {
    value: string;
    label: string;
    disabled?: boolean;
    description?: string;
}

export interface TableColumn<T = any> {
    key: keyof T;
    label: string;
    sortable?: boolean;
    width?: string;
    render?: (value: any, row: T) => React.ReactNode;
}

export interface FilterOption {
    key: string;
    label: string;
    type: 'text' | 'select' | 'date' | 'number' | 'boolean';
    options?: SelectOption[];
    placeholder?: string;
}

// ============================================================================
// Form Types
// ============================================================================

export interface FormField {
    name: string;
    label: string;
    type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'number' | 'date';
    placeholder?: string;
    required?: boolean;
    validation?: any;
    options?: SelectOption[];
    description?: string;
    disabled?: boolean;
}

export interface FormConfig {
    fields: FormField[];
    submitLabel?: string;
    cancelLabel?: string;
    layout?: 'vertical' | 'horizontal';
    columns?: number;
} 