<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant;
use App\Services\Core\LoggingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password as PasswordRule;

class AuthController extends Controller
{
    protected LoggingService $loggingService;

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    /**
     * Register a new user and tenant
     */
    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'tenant_name' => 'required|string|max:255|unique:tenants,name',
            'tenant_subdomain' => 'nullable|string|max:63|unique:tenants,subdomain|regex:/^[a-z0-9-]+$/',
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', PasswordRule::defaults()],
            'phone' => 'nullable|string|max:20',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your input data',
                'error' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create tenant first
            $tenant = Tenant::create([
                'name' => $request->tenant_name,
                'subdomain' => $request->tenant_subdomain,
                'email' => $request->email,
                'is_active' => true,
                'subscription_status' => 'trial',
                'trial_ends_at' => now()->addDays(14),
                'timezone' => $request->timezone ?? 'UTC',
                'language' => $request->language ?? 'en',
                'feature_flags' => [
                    'ai_providers' => true,
                    'tools' => true,
                    'workflows' => true,
                    'documents' => true,
                    'chat' => true,
                    'websocket' => true,
                ],
                'limits' => [
                    'users' => 5,
                    'ai_requests_per_month' => 1000,
                    'storage_mb' => 1024,
                    'websocket_connections' => 5,
                ],
                'settings' => [
                    'require_email_verification' => true,
                    'require_2fa' => false,
                    'session_timeout_minutes' => 480,
                    'password_expiry_days' => 90,
                ]
            ]);

            // Create owner user
            $user = User::create([
                'tenant_id' => $tenant->id,
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'owner',
                'phone' => $request->phone,
                'timezone' => $request->timezone ?? 'UTC',
                'language' => $request->language ?? 'en',
                'is_active' => true,
                'api_access_enabled' => true,
                'password_changed_at' => now(),
            ]);

            // Email verification disabled for now - can be enabled later
            // $user->sendEmailVerificationNotification();

            // Create API token
            $token = $user->createToken('auth-token', [
                'chat:access',
                'documents:create',
                'documents:view',
                'tools:view',
                'workflows:view',
                'websocket:connect'
            ])->plainTextToken;

            // Log registration
            $this->loggingService->log([
                'log_type' => 'security',
                'log_level' => 'info',
                'message' => 'New user registration',
                'user_id' => $user->id,
                'tenant_id' => $tenant->id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'metadata' => [
                    'tenant_name' => $tenant->name,
                    'user_email' => $user->email,
                ],
                'security_event_type' => 'login_success',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Registration successful',
                'data' => [
                    'user' => $user->makeHidden(['two_factor_secret', 'two_factor_recovery_codes']),
                    'tenant' => $tenant->makeHidden(['api_key', 'webhook_secret']),
                    'token' => $token,
                    'token_type' => 'Bearer',
                    'expires_at' => null, // No expiration for registration token
                ]
            ], 201);

        } catch (\Exception $e) {
            $this->loggingService->log([
                'log_type' => 'error',
                'log_level' => 'error',
                'message' => 'Registration failed',
                'error_message' => $e->getMessage(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'metadata' => [
                    'email' => $request->email,
                    'tenant_name' => $request->tenant_name,
                ],
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during registration. Please try again.',
                'error' => 'Registration failed'
            ], 500);
        }
    }

    /**
     * Login user
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
            'tenant_identifier' => 'nullable|string', // subdomain, slug, or tenant ID
            'remember' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'error' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Find user
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                $this->logFailedLogin($request, null, 'User not found');
                return response()->json([
                    'success' => false,
                    'message' => 'The provided credentials are incorrect.',
                    'error' => 'Invalid credentials'
                ], 401);
            }

            // Check if user is locked
            if ($user->isLocked()) {
                $this->logFailedLogin($request, $user, 'Account locked');
                return response()->json([
                    'error' => 'Account locked',
                    'message' => 'Your account has been temporarily locked due to multiple failed login attempts.',
                    'locked_until' => $user->locked_until
                ], 423);
            }

            // Verify password
            if (!Hash::check($request->password, $user->password)) {
                $user->incrementFailedLoginAttempts();
                $this->logFailedLogin($request, $user, 'Invalid password');
                return response()->json([
                    'error' => 'Invalid credentials',
                    'message' => 'The provided credentials are incorrect.'
                ], 401);
            }

            // Check if user is active
            if (!$user->is_active) {
                $this->logFailedLogin($request, $user, 'User inactive');
                return response()->json([
                    'error' => 'Account inactive',
                    'message' => 'Your account has been deactivated.'
                ], 403);
            }

            // Get tenant
            $tenant = $user->tenant;
            if (!$tenant || !$tenant->is_active) {
                $this->logFailedLogin($request, $user, 'Tenant inactive');
                return response()->json([
                    'error' => 'Tenant inactive',
                    'message' => 'The tenant account is currently inactive.'
                ], 403);
            }

            // Check subscription status
            if (!$tenant->is_subscription_active) {
                $this->logFailedLogin($request, $user, 'Subscription inactive');
                return response()->json([
                    'error' => 'Subscription inactive',
                    'message' => 'The tenant subscription is not active.'
                ], 402);
            }

            // Reset failed login attempts
            $user->resetFailedLoginAttempts();

            // Update last activity
            $user->updateLastActivity($request->ip(), $request->userAgent());
            $user->increment('login_count');

            // Create token with appropriate abilities
            $abilities = $this->getUserTokenAbilities($user);
            $tokenName = 'auth-token-' . now()->timestamp;
            $token = $user->createToken($tokenName, $abilities)->plainTextToken;

            // Log successful login
            $this->loggingService->log([
                'log_type' => 'security',
                'log_level' => 'info',
                'message' => 'User login successful',
                'user_id' => $user->id,
                'tenant_id' => $tenant->id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'security_event_type' => 'login_success',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $user->makeHidden(['two_factor_secret', 'two_factor_recovery_codes']),
                    'tenant' => $tenant->makeHidden(['api_key', 'webhook_secret']),
                    'token' => $token,
                    'token_type' => 'Bearer',
                    'expires_at' => null,
                    'abilities' => $abilities,
                ]
            ]);

        } catch (\Exception $e) {
            $this->loggingService->log([
                'log_type' => 'error',
                'log_level' => 'error',
                'message' => 'Login error',
                'error_message' => $e->getMessage(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'metadata' => ['email' => $request->email],
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during login. Please try again.',
                'error' => 'Login failed'
            ], 500);
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            // Revoke current token
            $request->user()->currentAccessToken()->delete();

            // Log logout
            $this->loggingService->log([
                'log_type' => 'security',
                'log_level' => 'info',
                'message' => 'User logout',
                'user_id' => $user->id,
                'tenant_id' => $user->tenant_id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'security_event_type' => 'logout',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Logout successful'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Logout failed',
                'message' => 'An error occurred during logout.'
            ], 500);
        }
    }

    /**
     * Get authenticated user
     */
    public function user(Request $request): JsonResponse
    {
        $user = Auth::user();
        $tenant = $request->get('tenant');

        return response()->json([
            'user' => $user->makeHidden(['two_factor_secret', 'two_factor_recovery_codes']),
            'tenant' => $tenant->makeHidden(['api_key', 'webhook_secret']),
            'permissions' => $user->effective_permissions,
        ]);
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
            'preferences' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user->update($request->only([
                'name', 'phone', 'timezone', 'language', 'preferences'
            ]));

            return response()->json([
                'message' => 'Profile updated successfully',
                'user' => $user->makeHidden(['two_factor_secret', 'two_factor_recovery_codes'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Profile update failed',
                'message' => 'An error occurred while updating your profile.'
            ], 500);
        }
    }

    /**
     * Change password
     */
    public function changePassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'password' => ['required', 'confirmed', PasswordRule::defaults()],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'error' => 'Invalid current password',
                'message' => 'The current password is incorrect.'
            ], 400);
        }

        try {
            $user->update([
                'password' => Hash::make($request->password),
                'password_changed_at' => now(),
            ]);

            // Revoke all existing tokens except current
            $currentToken = $request->user()->currentAccessToken();
            $user->tokens()->where('id', '!=', $currentToken->id)->delete();

            $this->loggingService->log([
                'log_type' => 'security',
                'log_level' => 'info',
                'message' => 'Password changed',
                'user_id' => $user->id,
                'tenant_id' => $user->tenant_id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'message' => 'Password changed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Password change failed',
                'message' => 'An error occurred while changing your password.'
            ], 500);
        }
    }

    /**
     * Get user token abilities based on role and permissions
     */
    protected function getUserTokenAbilities(User $user): array
    {
        $baseAbilities = [
            'chat:access',
            'websocket:connect',
        ];

        $roleAbilities = [
            'owner' => ['*'], // Full access
            'admin' => [
                'users:view', 'users:create', 'users:update',
                'ai_providers:*', 'tools:*', 'workflows:*', 'documents:*',
                'monitoring:view', 'websocket:broadcast'
            ],
            'manager' => [
                'users:view', 'ai_providers:view',
                'tools:create', 'tools:update', 'tools:execute',
                'workflows:create', 'workflows:update', 'workflows:execute',
                'documents:*'
            ],
            'user' => [
                'tools:view', 'tools:execute',
                'workflows:view', 'workflows:execute',
                'documents:create', 'documents:view', 'documents:update'
            ],
            'viewer' => [
                'tools:view', 'workflows:view', 'documents:view'
            ]
        ];

        $abilities = array_merge($baseAbilities, $roleAbilities[$user->role] ?? []);

        return array_unique($abilities);
    }

    /**
     * Log failed login attempt
     */
    protected function logFailedLogin(Request $request, ?User $user, string $reason): void
    {
        $this->loggingService->log([
            'log_type' => 'security',
            'log_level' => 'warning',
            'message' => 'Failed login attempt',
            'user_id' => $user?->id,
            'tenant_id' => $user?->tenant_id,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'metadata' => [
                'email' => $request->email,
                'reason' => $reason,
            ],
            'security_event_type' => 'login_failure',
            'is_security_incident' => true,
        ]);
    }

    // Additional methods for forgot password, reset password, 2FA, etc. would go here
    // For brevity, I'm including the core authentication methods
}
