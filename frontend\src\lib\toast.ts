import toast, { ToastOptions } from 'react-hot-toast';

// Default toast options
const defaultOptions: ToastOptions = {
    duration: 4000,
    position: 'top-right',
    style: {
        background: '#fff',
        color: '#374151',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '12px 16px',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    },
};

// Success toast
export const showSuccess = (message: string, options?: ToastOptions) => {
    return toast.success(message, {
        ...defaultOptions,
        style: {
            ...defaultOptions.style,
            border: '1px solid #10b981',
            background: '#f0fdf4',
        },
        iconTheme: {
            primary: '#10b981',
            secondary: '#f0fdf4',
        },
        ...options,
    });
};

// Error toast
export const showError = (message: string, options?: ToastOptions) => {
    return toast.error(message, {
        ...defaultOptions,
        duration: 6000,
        style: {
            ...defaultOptions.style,
            border: '1px solid #ef4444',
            background: '#fef2f2',
        },
        iconTheme: {
            primary: '#ef4444',
            secondary: '#fef2f2',
        },
        ...options,
    });
};

// Loading toast
export const showLoading = (message: string, options?: ToastOptions) => {
    return toast.loading(message, {
        ...defaultOptions,
        style: {
            ...defaultOptions.style,
            border: '1px solid #6b7280',
            background: '#f9fafb',
        },
        ...options,
    });
};

// Dismiss specific toast
export const dismiss = (toastId: string) => {
    toast.dismiss(toastId);
};

// Export the main toast object for custom usage
export { toast };

// Common toast messages
export const TOAST_MESSAGES = {
    // Authentication
    LOGIN_SUCCESS: 'Welcome back! You have been successfully logged in.',
    LOGIN_ERROR: 'Login failed. Please check your credentials and try again.',
    LOGOUT_SUCCESS: 'You have been successfully logged out.',
    REGISTER_SUCCESS: 'Account created successfully! Welcome to Axient MCP++.',
    REGISTER_ERROR: 'Registration failed. Please try again.',

    // Form validation
    FORM_INVALID: 'Please correct the errors in the form before submitting.',
    REQUIRED_FIELDS: 'Please fill in all required fields.',

    // Network
    NETWORK_ERROR: 'Network error. Please check your connection and try again.',
    SERVER_ERROR: 'Server error. Please try again later.',

    // Generic
    SUCCESS: 'Operation completed successfully.',
    ERROR: 'An error occurred. Please try again.',
    LOADING: 'Processing...',
    SAVED: 'Changes saved successfully.',
    DELETED: 'Item deleted successfully.',
    COPIED: 'Copied to clipboard.',
}; 