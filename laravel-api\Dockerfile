FROM php:8.3-fpm

# Set working directory
WORKDIR /var/www

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libpq-dev \
    libzip-dev \
    zip \
    unzip \
    supervisor \
    nginx \
    && docker-php-ext-configure pgsql -with-pgsql=/usr/local/pgsql \
    && docker-php-ext-install pdo pdo_pgsql pgsql mbstring exif pcntl bcmath gd zip

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy existing application directory contents
COPY . /var/www

# Copy existing application directory permissions
COPY --chown=www-data:www-data . /var/www

# Set proper permissions
RUN chown -R www-data:www-data /var/www \
    && chmod -R 755 /var/www/storage \
    && chmod -R 755 /var/www/bootstrap/cache

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader

# Create supervisor configuration for Laravel queue worker
RUN echo '[program:laravel-worker]' > /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'process_name=%(program_name)s_%(process_num)02d' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'command=php /var/www/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'autostart=true' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'autorestart=true' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'stopasgroup=true' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'killasgroup=true' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'user=www-data' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'numprocs=8' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'stdout_logfile=/var/www/storage/logs/worker.log' >> /etc/supervisor/conf.d/laravel-worker.conf \
    && echo 'stopwaitsecs=3600' >> /etc/supervisor/conf.d/laravel-worker.conf

# Expose port 8000
EXPOSE 8000

# Create startup script
RUN echo '#!/bin/bash' > /usr/local/bin/start.sh \
    && echo 'php artisan config:cache' >> /usr/local/bin/start.sh \
    && echo 'php artisan route:cache' >> /usr/local/bin/start.sh \
    && echo 'php artisan view:cache' >> /usr/local/bin/start.sh \
    && echo 'supervisord -c /etc/supervisor/supervisord.conf &' >> /usr/local/bin/start.sh \
    && echo 'php artisan serve --host=0.0.0.0 --port=8000' >> /usr/local/bin/start.sh \
    && chmod +x /usr/local/bin/start.sh

CMD ["/usr/local/bin/start.sh"]
