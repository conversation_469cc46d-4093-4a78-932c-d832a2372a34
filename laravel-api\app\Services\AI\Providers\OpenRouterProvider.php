<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class OpenRouterProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://openrouter.ai/api/v1';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => $this->config['app_url'] ?? 'https://localhost',
                'X-Title' => $this->config['app_name'] ?? 'Axient MCP',
            ])->timeout(10)->get($this->baseUrl . '/models');

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'OpenRouter API connection successful',
                    'models_count' => count($response->json('data', [])),
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'OpenRouter API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'OpenRouter API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'HTTP-Referer' => $this->config['app_url'] ?? 'https://localhost',
                'X-Title' => $this->config['app_name'] ?? 'Axient MCP',
            ])->get($this->baseUrl . '/models');

            if ($response->successful()) {
                $models = $response->json('data', []);
                return array_map(function ($model) {
                    return [
                        'id' => $model['id'],
                        'name' => $model['name'] ?? $model['id'],
                        'description' => $model['description'] ?? $this->getModelDescription($model['id']),
                        'capabilities' => $this->getModelCapabilities($model['id']),
                        'context_length' => $model['context_length'] ?? 4096,
                        'cost_per_token' => $this->parseModelCost($model),
                        'provider' => $this->extractProvider($model['id']),
                    ];
                }, $models);
            }

            return $this->getDefaultModels();

        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('openrouter_models_fetch_error', [
                'error' => $e->getMessage(),
            ], 'error');
            return $this->getDefaultModels();
        }
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        // OpenRouter uses chat completions, convert to chat format
        $messages = [['role' => 'user', 'content' => $prompt]];
        return $this->generateChatCompletion($messages, $options, $stream);
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'openai/gpt-3.5-turbo';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'model' => $model,
                'messages' => $messages,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            if (isset($options['top_p'])) {
                $payload['top_p'] = $options['top_p'];
            }

            if (isset($options['frequency_penalty'])) {
                $payload['frequency_penalty'] = $options['frequency_penalty'];
            }

            if (isset($options['presence_penalty'])) {
                $payload['presence_penalty'] = $options['presence_penalty'];
            }

            if (isset($options['stop'])) {
                $payload['stop'] = $options['stop'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => $this->config['app_url'] ?? 'https://localhost',
                'X-Title' => $this->config['app_name'] ?? 'Axient MCP',
            ])->timeout(60)->post($this->baseUrl . '/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['message']['content'] ?? '',
                    'message' => $data['choices'][0]['message'] ?? [],
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? [], $model),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'provider' => $this->extractProvider($model),
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('OpenRouter API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        $model = $options['model'] ?? 'openai/text-embedding-ada-002';

        try {
            $payload = [
                'model' => $model,
                'input' => $texts,
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => $this->config['app_url'] ?? 'https://localhost',
                'X-Title' => $this->config['app_name'] ?? 'Axient MCP',
            ])->timeout(60)->post($this->baseUrl . '/embeddings', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'embeddings' => array_map(fn($item) => $item['embedding'], $data['data']),
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? [], $model),
                    'provider' => $this->extractProvider($model),
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('OpenRouter embedding error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'model_aggregation',
            'cost_optimization',
            'provider_comparison',
            'streaming',
            'function_calling',
        ];
    }

    public function getName(): string
    {
        return 'openrouter';
    }

    public function getDisplayName(): string
    {
        return 'OpenRouter';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 200,
            'tokens_per_minute' => 40000,
            'requests_per_day' => 1000,
        ];
    }

    public function calculateCost(array $usage, string $model = ''): float
    {
        $promptTokens = $usage['prompt_tokens'] ?? 0;
        $completionTokens = $usage['completion_tokens'] ?? 0;

        // OpenRouter has dynamic pricing based on the underlying model
        // This is a simplified calculation - actual costs vary by model
        $baseCost = 0.000002; // Base cost per token

        if (str_contains($model, 'gpt-4')) {
            $baseCost = 0.00003;
        } elseif (str_contains($model, 'claude')) {
            $baseCost = 0.000008;
        } elseif (str_contains($model, 'llama')) {
            $baseCost = 0.0000002;
        }

        return ($promptTokens + $completionTokens) * $baseCost;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('openrouter_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'openrouter',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'openai/gpt-4',
                'name' => 'GPT-4 (OpenAI)',
                'description' => 'OpenAI GPT-4 via OpenRouter',
                'capabilities' => ['text_generation', 'chat', 'function_calling'],
                'context_length' => 8192,
                'cost_per_token' => 0.00003,
                'provider' => 'openai',
            ],
            [
                'id' => 'openai/gpt-3.5-turbo',
                'name' => 'GPT-3.5 Turbo (OpenAI)',
                'description' => 'OpenAI GPT-3.5 Turbo via OpenRouter',
                'capabilities' => ['text_generation', 'chat', 'function_calling'],
                'context_length' => 4096,
                'cost_per_token' => 0.000002,
                'provider' => 'openai',
            ],
            [
                'id' => 'anthropic/claude-3-sonnet',
                'name' => 'Claude 3 Sonnet (Anthropic)',
                'description' => 'Anthropic Claude 3 Sonnet via OpenRouter',
                'capabilities' => ['text_generation', 'chat', 'long_context'],
                'context_length' => 200000,
                'cost_per_token' => 0.000003,
                'provider' => 'anthropic',
            ],
            [
                'id' => 'meta-llama/llama-2-70b-chat',
                'name' => 'Llama 2 70B Chat',
                'description' => 'Meta Llama 2 70B Chat via OpenRouter',
                'capabilities' => ['text_generation', 'chat'],
                'context_length' => 4096,
                'cost_per_token' => 0.0000008,
                'provider' => 'meta',
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $provider = $this->extractProvider($modelId);
        $modelName = str_replace($provider . '/', '', $modelId);

        return ucfirst($provider) . ' ' . ucwords(str_replace('-', ' ', $modelName)) . ' via OpenRouter';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        if (str_contains($modelId, 'embedding')) {
            return ['embeddings'];
        }

        $capabilities = ['text_generation', 'chat'];

        if (str_contains($modelId, 'gpt-4') || str_contains($modelId, 'gpt-3.5')) {
            $capabilities[] = 'function_calling';
        }

        if (str_contains($modelId, 'claude')) {
            $capabilities[] = 'long_context';
        }

        return $capabilities;
    }

    protected function extractProvider(string $modelId): string
    {
        $parts = explode('/', $modelId);
        return $parts[0] ?? 'unknown';
    }

    protected function parseModelCost(array $model): float
    {
        // OpenRouter provides pricing in the model data
        $pricing = $model['pricing'] ?? [];
        $promptCost = $pricing['prompt'] ?? 0;
        $completionCost = $pricing['completion'] ?? 0;

        // Return average cost per token
        return ($promptCost + $completionCost) / 2;
    }

    /**
     * Get cost comparison across models
     */
    public function getCostComparison(array $models, int $estimatedTokens = 1000): array
    {
        $comparison = [];

        foreach ($models as $model) {
            $costPerToken = $this->parseModelCost($model);
            $estimatedCost = $costPerToken * $estimatedTokens;

            $comparison[] = [
                'model' => $model['id'],
                'provider' => $this->extractProvider($model['id']),
                'cost_per_token' => $costPerToken,
                'estimated_cost' => $estimatedCost,
                'context_length' => $model['context_length'] ?? 4096,
            ];
        }

        // Sort by estimated cost
        usort($comparison, fn($a, $b) => $a['estimated_cost'] <=> $b['estimated_cost']);

        return $comparison;
    }

    /**
     * Get best model for budget
     */
    public function getBestModelForBudget(float $maxCost, int $estimatedTokens = 1000): ?array
    {
        $models = $this->getAvailableModels();

        foreach ($models as $model) {
            $estimatedCost = $model['cost_per_token'] * $estimatedTokens;
            if ($estimatedCost <= $maxCost) {
                return $model;
            }
        }

        return null;
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'model_aggregation',
            'cost_optimization',
            'provider_comparison',
            'streaming',
            'function_calling',
        ];
    }
}
