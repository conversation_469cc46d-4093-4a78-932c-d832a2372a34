<?php

namespace App\Services\WebSocket;

use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;
use Illuminate\Support\Facades\Redis;

class ChannelManager extends BaseService
{
    /**
     * Channel types and their configurations
     */
    protected array $channelTypes = [
        'tenant_broadcast' => [
            'prefix' => 'tenant',
            'private' => false,
            'presence' => false,
            'persistent' => true,
        ],
        'chat' => [
            'prefix' => 'chat',
            'private' => true,
            'presence' => true,
            'persistent' => true,
        ],
        'notification' => [
            'prefix' => 'notification',
            'private' => true,
            'presence' => false,
            'persistent' => false,
        ],
        'system' => [
            'prefix' => 'system',
            'private' => true,
            'presence' => false,
            'persistent' => true,
        ],
        'monitoring' => [
            'prefix' => 'monitoring',
            'private' => true,
            'presence' => false,
            'persistent' => false,
        ],
        'user_private' => [
            'prefix' => 'private',
            'private' => true,
            'presence' => false,
            'persistent' => true,
        ],
        'presence' => [
            'prefix' => 'presence',
            'private' => true,
            'presence' => true,
            'persistent' => false,
        ],
    ];

    /**
     * Active channels tracking
     */
    protected array $activeChannels = [];

    /**
     * Channel subscriptions tracking
     */
    protected array $channelSubscriptions = [];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'channel_manager';
    }

    /**
     * Set up tenant-specific channels
     */
    public function setupTenantChannels(string $tenantId, string $userId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('setup_tenant_channels', function () use ($tenantId, $userId) {
            $channels = [];

            // Create tenant broadcast channel
            $channels['tenant_broadcast'] = $this->createChannel('tenant_broadcast', $tenantId);

            // Create user private channel
            $channels['user_private'] = $this->createChannel('user_private', $tenantId, $userId);

            // Create notification channel
            $channels['notification'] = $this->createChannel('notification', $tenantId, $userId);

            // Create presence channel
            $channels['presence'] = $this->createChannel('presence', $tenantId, $userId);

            // Subscribe user to channels
            foreach ($channels as $channelType => $channelName) {
                $this->subscribeUserToChannel($tenantId, $userId, $channelName, $channelType);
            }

            $this->logActivity('Tenant channels set up', [
                'tenant_id' => $tenantId,
                'user_id' => $userId,
                'channels_created' => count($channels),
                'channel_names' => array_values($channels),
            ]);

            return $channels;
        }, [
            'tenant_id' => $tenantId,
            'user_id' => $userId,
        ]);
    }

    /**
     * Create a channel
     */
    public function createChannel(string $channelType, string $tenantId, ?string $userId = null, ?string $sessionId = null): string
    {
        $this->requireEnabled();

        return $this->executeWithTracking('create_channel', function () use ($channelType, $tenantId, $userId, $sessionId) {
            if (!isset($this->channelTypes[$channelType])) {
                throw new \InvalidArgumentException("Unknown channel type: {$channelType}");
            }

            $config = $this->channelTypes[$channelType];
            $channelName = $this->generateChannelName($channelType, $tenantId, $userId, $sessionId);

            // Register channel
            $this->registerChannel($channelName, $channelType, $tenantId, $config);

            return $channelName;
        }, [
            'channel_type' => $channelType,
            'tenant_id' => $tenantId,
            'user_id' => $userId,
        ]);
    }

    /**
     * Get chat channel for session
     */
    public function getChatChannel(string $tenantId, string $sessionId): string
    {
        return $this->createChannel('chat', $tenantId, null, $sessionId);
    }

    /**
     * Get notification channel for tenant/user
     */
    public function getNotificationChannel(string $tenantId, ?string $userId = null): string
    {
        return $this->createChannel('notification', $tenantId, $userId);
    }

    /**
     * Get system channel for tenant
     */
    public function getSystemChannel(string $tenantId): string
    {
        return $this->createChannel('system', $tenantId);
    }

    /**
     * Get tenant broadcast channel
     */
    public function getTenantBroadcastChannel(string $tenantId): string
    {
        return $this->createChannel('tenant_broadcast', $tenantId);
    }

    /**
     * Get monitoring channel for tenant
     */
    public function getMonitoringChannel(string $tenantId): string
    {
        return $this->createChannel('monitoring', $tenantId);
    }

    /**
     * Get user's active channels
     */
    public function getUserChannels(string $tenantId, string $userId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_user_channels', function () use ($tenantId, $userId) {
            $userKey = "{$tenantId}:{$userId}";
            $channels = [];

            foreach ($this->channelSubscriptions as $channelName => $subscriptions) {
                if (isset($subscriptions[$userKey])) {
                    $channels[] = $channelName;
                }
            }

            return $channels;
        }, [
            'tenant_id' => $tenantId,
            'user_id' => $userId,
        ]);
    }

    /**
     * Subscribe user to channel
     */
    public function subscribeUserToChannel(string $tenantId, string $userId, string $channelName, string $channelType): bool
    {
        $this->requireEnabled();

        return $this->executeWithTracking('subscribe_user_to_channel', function () use ($tenantId, $userId, $channelName, $channelType) {
            $userKey = "{$tenantId}:{$userId}";

            // Initialize channel subscriptions if not exists
            if (!isset($this->channelSubscriptions[$channelName])) {
                $this->channelSubscriptions[$channelName] = [];
            }

            // Add user subscription
            $this->channelSubscriptions[$channelName][$userKey] = [
                'tenant_id' => $tenantId,
                'user_id' => $userId,
                'channel_type' => $channelType,
                'subscribed_at' => now(),
                'last_activity' => now(),
            ];

            // Update Redis for persistence
            $this->updateChannelSubscriptionsInRedis($channelName);

            $this->logActivity('User subscribed to channel', [
                'tenant_id' => $tenantId,
                'user_id' => $userId,
                'channel_name' => $channelName,
                'channel_type' => $channelType,
            ]);

            return true;
        }, [
            'tenant_id' => $tenantId,
            'user_id' => $userId,
            'channel_name' => $channelName,
        ]);
    }

    /**
     * Unsubscribe user from channel
     */
    public function unsubscribeUserFromChannel(string $tenantId, string $userId, string $channelName): bool
    {
        $this->requireEnabled();

        return $this->executeWithTracking('unsubscribe_user_from_channel', function () use ($tenantId, $userId, $channelName) {
            $userKey = "{$tenantId}:{$userId}";

            if (isset($this->channelSubscriptions[$channelName][$userKey])) {
                unset($this->channelSubscriptions[$channelName][$userKey]);

                // Update Redis
                $this->updateChannelSubscriptionsInRedis($channelName);

                // Clean up empty channels
                if (empty($this->channelSubscriptions[$channelName])) {
                    $this->cleanupChannel($channelName);
                }

                $this->logActivity('User unsubscribed from channel', [
                    'tenant_id' => $tenantId,
                    'user_id' => $userId,
                    'channel_name' => $channelName,
                ]);

                return true;
            }

            return false;
        }, [
            'tenant_id' => $tenantId,
            'user_id' => $userId,
            'channel_name' => $channelName,
        ]);
    }

    /**
     * Clean up user channels
     */
    public function cleanupUserChannels(string $tenantId, string $userId): int
    {
        $this->requireEnabled();

        return $this->executeWithTracking('cleanup_user_channels', function () use ($tenantId, $userId) {
            $userKey = "{$tenantId}:{$userId}";
            $cleanedChannels = 0;

            foreach ($this->channelSubscriptions as $channelName => $subscriptions) {
                if (isset($subscriptions[$userKey])) {
                    unset($this->channelSubscriptions[$channelName][$userKey]);
                    $cleanedChannels++;

                    // Update Redis
                    $this->updateChannelSubscriptionsInRedis($channelName);

                    // Clean up empty channels
                    if (empty($this->channelSubscriptions[$channelName])) {
                        $this->cleanupChannel($channelName);
                    }
                }
            }

            $this->logActivity('User channels cleaned up', [
                'tenant_id' => $tenantId,
                'user_id' => $userId,
                'channels_cleaned' => $cleanedChannels,
            ]);

            return $cleanedChannels;
        }, [
            'tenant_id' => $tenantId,
            'user_id' => $userId,
        ]);
    }

    /**
     * Get channel statistics
     */
    public function getChannelStatistics(string $tenantId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_channel_statistics', function () use ($tenantId) {
            $stats = [
                'tenant_id' => $tenantId,
                'total_channels' => 0,
                'active_channels' => 0,
                'channels_by_type' => [],
                'total_subscriptions' => 0,
                'subscriptions_by_channel' => [],
                'users_online' => 0,
            ];

            $tenantUsers = [];

            foreach ($this->activeChannels as $channelName => $channelInfo) {
                if ($channelInfo['tenant_id'] === $tenantId) {
                    $stats['total_channels']++;

                    $channelType = $channelInfo['channel_type'];
                    $stats['channels_by_type'][$channelType] = ($stats['channels_by_type'][$channelType] ?? 0) + 1;

                    // Count subscriptions for this channel
                    $subscriptions = $this->channelSubscriptions[$channelName] ?? [];
                    $channelSubscriptionCount = count($subscriptions);

                    if ($channelSubscriptionCount > 0) {
                        $stats['active_channels']++;
                        $stats['total_subscriptions'] += $channelSubscriptionCount;
                        $stats['subscriptions_by_channel'][$channelName] = $channelSubscriptionCount;

                        // Track unique users
                        foreach ($subscriptions as $subscription) {
                            if ($subscription['tenant_id'] === $tenantId) {
                                $tenantUsers[$subscription['user_id']] = true;
                            }
                        }
                    }
                }
            }

            $stats['users_online'] = count($tenantUsers);

            return $stats;
        }, [
            'tenant_id' => $tenantId,
        ]);
    }

    /**
     * Get channel presence information
     */
    public function getChannelPresence(string $channelName): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_channel_presence', function () use ($channelName) {
            $subscriptions = $this->channelSubscriptions[$channelName] ?? [];
            $presence = [];

            foreach ($subscriptions as $userKey => $subscription) {
                $presence[] = [
                    'user_id' => $subscription['user_id'],
                    'tenant_id' => $subscription['tenant_id'],
                    'subscribed_at' => $subscription['subscribed_at'],
                    'last_activity' => $subscription['last_activity'],
                    'online' => $this->isUserOnline($subscription['user_id']),
                ];
            }

            return [
                'channel_name' => $channelName,
                'total_subscribers' => count($presence),
                'online_users' => count(array_filter($presence, fn($p) => $p['online'])),
                'presence' => $presence,
            ];
        }, [
            'channel_name' => $channelName,
        ]);
    }

    /**
     * Update user activity in channel
     */
    public function updateUserActivity(string $tenantId, string $userId, string $channelName): bool
    {
        $this->requireEnabled();

        return $this->executeWithTracking('update_user_activity', function () use ($tenantId, $userId, $channelName) {
            $userKey = "{$tenantId}:{$userId}";

            if (isset($this->channelSubscriptions[$channelName][$userKey])) {
                $this->channelSubscriptions[$channelName][$userKey]['last_activity'] = now();
                return true;
            }

            return false;
        }, [
            'tenant_id' => $tenantId,
            'user_id' => $userId,
            'channel_name' => $channelName,
        ]);
    }

    /**
     * Generate channel name
     */
    protected function generateChannelName(string $channelType, string $tenantId, ?string $userId = null, ?string $sessionId = null): string
    {
        $config = $this->channelTypes[$channelType];
        $prefix = $config['prefix'];

        $parts = [$prefix, $tenantId];

        if ($userId) {
            $parts[] = $userId;
        }

        if ($sessionId) {
            $parts[] = $sessionId;
        }

        $channelName = implode('.', $parts);

        // Add private prefix if needed
        if ($config['private']) {
            $channelName = 'private-' . $channelName;
        }

        // Add presence prefix if needed
        if ($config['presence']) {
            $channelName = 'presence-' . $channelName;
        }

        return $channelName;
    }

    /**
     * Register channel
     */
    protected function registerChannel(string $channelName, string $channelType, string $tenantId, array $config): void
    {
        $this->activeChannels[$channelName] = [
            'channel_name' => $channelName,
            'channel_type' => $channelType,
            'tenant_id' => $tenantId,
            'config' => $config,
            'created_at' => now(),
            'last_activity' => now(),
        ];

        // Store in Redis for persistence if configured
        if ($config['persistent']) {
            $this->storeChannelInRedis($channelName, $this->activeChannels[$channelName]);
        }
    }

    /**
     * Clean up channel
     */
    protected function cleanupChannel(string $channelName): void
    {
        if (isset($this->activeChannels[$channelName])) {
            $channelInfo = $this->activeChannels[$channelName];

            // Remove from active channels
            unset($this->activeChannels[$channelName]);

            // Remove subscriptions
            unset($this->channelSubscriptions[$channelName]);

            // Remove from Redis
            $this->removeChannelFromRedis($channelName);

            $this->logActivity('Channel cleaned up', [
                'channel_name' => $channelName,
                'channel_type' => $channelInfo['channel_type'],
                'tenant_id' => $channelInfo['tenant_id'],
            ]);
        }
    }

    /**
     * Store channel in Redis
     */
    protected function storeChannelInRedis(string $channelName, array $channelInfo): void
    {
        try {
            $key = "websocket:channels:{$channelName}";
            Redis::setex($key, 3600, json_encode($channelInfo)); // 1 hour TTL
        } catch (\Exception $e) {
            $this->logActivity('Failed to store channel in Redis', [
                'channel_name' => $channelName,
                'error' => $e->getMessage(),
            ], 'error');
        }
    }

    /**
     * Update channel subscriptions in Redis
     */
    protected function updateChannelSubscriptionsInRedis(string $channelName): void
    {
        try {
            $key = "websocket:subscriptions:{$channelName}";
            $subscriptions = $this->channelSubscriptions[$channelName] ?? [];

            if (!empty($subscriptions)) {
                Redis::setex($key, 3600, json_encode($subscriptions)); // 1 hour TTL
            } else {
                Redis::del($key);
            }
        } catch (\Exception $e) {
            $this->logActivity('Failed to update channel subscriptions in Redis', [
                'channel_name' => $channelName,
                'error' => $e->getMessage(),
            ], 'error');
        }
    }

    /**
     * Remove channel from Redis
     */
    protected function removeChannelFromRedis(string $channelName): void
    {
        try {
            Redis::del("websocket:channels:{$channelName}");
            Redis::del("websocket:subscriptions:{$channelName}");
        } catch (\Exception $e) {
            $this->logActivity('Failed to remove channel from Redis', [
                'channel_name' => $channelName,
                'error' => $e->getMessage(),
            ], 'error');
        }
    }

    /**
     * Check if user is online
     */
    protected function isUserOnline(string $userId): bool
    {
        // This would check against active WebSocket connections
        // For now, return true as placeholder
        return true;
    }

    /**
     * Load channels from Redis on startup
     */
    public function loadChannelsFromRedis(): int
    {
        $this->requireEnabled();

        return $this->executeWithTracking('load_channels_from_redis', function () {
            $loadedChannels = 0;

            try {
                $pattern = "websocket:channels:*";
                $keys = Redis::keys($pattern);

                foreach ($keys as $key) {
                    $channelData = Redis::get($key);
                    if ($channelData) {
                        $channelInfo = json_decode($channelData, true);
                        $channelName = $channelInfo['channel_name'];

                        $this->activeChannels[$channelName] = $channelInfo;
                        $loadedChannels++;

                        // Load subscriptions
                        $subscriptionKey = "websocket:subscriptions:{$channelName}";
                        $subscriptionData = Redis::get($subscriptionKey);
                        if ($subscriptionData) {
                            $this->channelSubscriptions[$channelName] = json_decode($subscriptionData, true);
                        }
                    }
                }

                $this->logActivity('Channels loaded from Redis', [
                    'loaded_channels' => $loadedChannels,
                ]);
            } catch (\Exception $e) {
                $this->logActivity('Failed to load channels from Redis', [
                    'error' => $e->getMessage(),
                ], 'error');
            }

            return $loadedChannels;
        });
    }

    /**
     * Clean up expired channels
     */
    public function cleanupExpiredChannels(): int
    {
        $this->requireEnabled();

        return $this->executeWithTracking('cleanup_expired_channels', function () {
            $cleanedChannels = 0;
            $expirationTime = now()->subHours(1); // Channels expire after 1 hour of inactivity

            foreach ($this->activeChannels as $channelName => $channelInfo) {
                if ($channelInfo['last_activity'] < $expirationTime) {
                    // Check if channel has any active subscriptions
                    $subscriptions = $this->channelSubscriptions[$channelName] ?? [];
                    $activeSubscriptions = array_filter($subscriptions, function ($subscription) use ($expirationTime) {
                        return $subscription['last_activity'] >= $expirationTime;
                    });

                    if (empty($activeSubscriptions)) {
                        $this->cleanupChannel($channelName);
                        $cleanedChannels++;
                    }
                }
            }

            $this->logActivity('Expired channels cleaned up', [
                'cleaned_channels' => $cleanedChannels,
            ]);

            return $cleanedChannels;
        });
    }
}
