<?php

namespace App\Events\WebSocket;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NotificationEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $tenantId;
    public ?string $userId;
    public string $notificationId;
    public string $notificationType;
    public string $title;
    public string $message;
    public array $data;
    public string $priority;
    public array $metadata;
    public string $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $tenantId,
        ?string $userId,
        string $notificationId,
        string $notificationType,
        string $title,
        string $message,
        array $data = [],
        string $priority = 'normal',
        array $metadata = []
    ) {
        $this->tenantId = $tenantId;
        $this->userId = $userId;
        $this->notificationId = $notificationId;
        $this->notificationType = $notificationType;
        $this->title = $title;
        $this->message = $message;
        $this->data = $data;
        $this->priority = $priority;
        $this->metadata = $metadata;
        $this->timestamp = now()->toISOString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [];

        // If user-specific notification
        if ($this->userId) {
            $channels[] = new PrivateChannel("notification.{$this->tenantId}.{$this->userId}");
        }

        // Always broadcast to tenant channel for admin notifications
        $channels[] = new PrivateChannel("notification.{$this->tenantId}");

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event_type' => 'notification',
            'notification_id' => $this->notificationId,
            'notification_type' => $this->notificationType,
            'title' => $this->title,
            'message' => $this->message,
            'data' => $this->data,
            'priority' => $this->priority,
            'user_id' => $this->userId,
            'metadata' => $this->metadata,
            'timestamp' => $this->timestamp,
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'notification.received';
    }

    /**
     * Determine if this event should be queued.
     */
    public function shouldQueue(): bool
    {
        return config('broadcasting.queue.enabled', true);
    }

    /**
     * Get the queue connection for the event.
     */
    public function broadcastQueue(): string
    {
        return config('broadcasting.queue.queue_name', 'websocket');
    }
}
