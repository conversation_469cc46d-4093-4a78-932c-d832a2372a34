<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class <PERSON><PERSON>rovider extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'ai_providers';

    protected $fillable = [
        'tenant_id',
        'created_by_user_id',
        'provider_name',
        'display_name',
        'description',
        'api_key',
        'organization_id',
        'base_url',
        'configuration',
        'available_models',
        'default_model',
        'rate_limit_per_minute',
        'rate_limit_per_hour',
        'rate_limit_per_day',
        'cost_per_1k_tokens',
        'cost_per_1k_output_tokens',
        'supports_streaming',
        'supports_function_calling',
        'supports_vision',
        'supports_code_generation',
        'max_context_length',
        'supported_features',
        'is_active',
        'is_default',
        'allowed_user_roles',
        'restricted_features',
        'webhook_url',
        'webhook_secret',
        'notifications_enabled',
        'notification_thresholds',
        'status',
        'metadata',
        'notes'
    ];

    protected $hidden = [
        'api_key'
    ];

    protected $casts = [
        'configuration' => 'array',
        'available_models' => 'array',
        'rate_limit_per_minute' => 'integer',
        'rate_limit_per_hour' => 'integer',
        'rate_limit_per_day' => 'integer',
        'cost_per_1k_tokens' => 'decimal:6',
        'cost_per_1k_output_tokens' => 'decimal:6',
        'tokens_used_today' => 'decimal:0',
        'tokens_used_this_month' => 'decimal:0',
        'total_tokens_used' => 'decimal:0',
        'requests_today' => 'integer',
        'requests_this_month' => 'integer',
        'total_requests' => 'integer',
        'total_cost' => 'decimal:2',
        'average_response_time_ms' => 'decimal:2',
        'success_rate_percentage' => 'decimal:2',
        'error_count_today' => 'integer',
        'last_successful_request' => 'datetime',
        'last_error' => 'datetime',
        'last_health_check' => 'datetime',
        'health_check_details' => 'array',
        'auto_failover_enabled' => 'boolean',
        'failover_priority' => 'integer',
        'supports_streaming' => 'boolean',
        'supports_function_calling' => 'boolean',
        'supports_vision' => 'boolean',
        'supports_code_generation' => 'boolean',
        'max_context_length' => 'integer',
        'supported_features' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'allowed_user_roles' => 'array',
        'restricted_features' => 'array',
        'notifications_enabled' => 'boolean',
        'notification_thresholds' => 'array',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the AI provider.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user who created the AI provider.
     */
    public function createdByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }
}
