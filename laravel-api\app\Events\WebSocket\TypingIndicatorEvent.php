<?php

namespace App\Events\WebSocket;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TypingIndicatorEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $tenantId;
    public string $sessionId;
    public string $userId;
    public string $userName;
    public bool $isTyping;
    public array $metadata;
    public string $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $tenantId,
        string $sessionId,
        string $userId,
        string $userName,
        bool $isTyping,
        array $metadata = []
    ) {
        $this->tenantId = $tenantId;
        $this->sessionId = $sessionId;
        $this->userId = $userId;
        $this->userName = $userName;
        $this->isTyping = $isTyping;
        $this->metadata = $metadata;
        $this->timestamp = now()->toISOString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("chat.{$this->tenantId}.{$this->sessionId}"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event_type' => 'typing_indicator',
            'session_id' => $this->sessionId,
            'user_id' => $this->userId,
            'user_name' => $this->userName,
            'is_typing' => $this->isTyping,
            'metadata' => $this->metadata,
            'timestamp' => $this->timestamp,
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return $this->isTyping ? 'chat.typing.start' : 'chat.typing.stop';
    }

    /**
     * Determine if this event should be queued.
     */
    public function shouldQueue(): bool
    {
        // Typing indicators should be sent immediately for real-time feel
        return false;
    }
}
