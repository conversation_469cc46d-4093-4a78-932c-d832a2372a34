<?php

namespace App\Services\MCP;

use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;
use App\Services\AI\AIProviderService;
use Illuminate\Support\Str;

class MCPOrchestrator extends BaseService
{
    protected IntentService $intentService;
    protected RetrieverService $retrieverService;
    protected ToolService $toolService;
    protected WorkflowService $workflowService;
    protected MemoryService $memoryService;
    protected FormatterService $formatterService;
    protected GuardrailService $guardrailService;
    protected AIProviderService $aiProviderService;

    /**
     * Operation pipeline stages
     */
    protected array $pipelineStages = [
        'intent_classification',
        'memory_retrieval',
        'knowledge_retrieval',
        'tool_execution',
        'workflow_processing',
        'ai_generation',
        'response_formatting',
        'guardrail_validation',
        'result_aggregation',
    ];

    /**
     * Active operations tracking
     */
    protected array $activeOperations = [];

    /**
     * Operation results cache
     */
    protected array $operationResults = [];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService,
        IntentService $intentService,
        RetrieverService $retrieverService,
        ToolService $toolService,
        WorkflowService $workflowService,
        MemoryService $memoryService,
        FormatterService $formatterService,
        GuardrailService $guardrailService,
        AIProviderService $aiProviderService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);

        $this->intentService = $intentService;
        $this->retrieverService = $retrieverService;
        $this->toolService = $toolService;
        $this->workflowService = $workflowService;
        $this->memoryService = $memoryService;
        $this->formatterService = $formatterService;
        $this->guardrailService = $guardrailService;
        $this->aiProviderService = $aiProviderService;
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'mcp_orchestrator';
    }

    /**
     * Process MCP request through complete pipeline
     */
    public function processRequest(array $requestData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        $operationId = $this->generateOperationId();
        $sessionId = $requestData['session_id'] ?? null;

        return $this->executeWithTracking('process_request', function () use ($requestData, $operationId, $sessionId) {
            // Initialize operation tracking
            $this->initializeOperation($operationId, $requestData);

            try {
                // Execute pipeline stages
                $result = $this->executePipeline($operationId, $requestData, $sessionId);

                // Mark operation as completed
                $this->completeOperation($operationId, $result);

                return $result;
            } catch (\Exception $e) {
                // Handle operation failure
                $this->failOperation($operationId, $e);
                throw $e;
            }
        }, [
            'operation_id' => $operationId,
            'session_id' => $sessionId,
            'request_type' => $requestData['type'] ?? 'unknown',
        ]);
    }

    /**
     * Execute the complete MCP pipeline
     */
    protected function executePipeline(string $operationId, array $requestData, ?string $sessionId): array
    {
        $pipelineContext = [
            'operation_id' => $operationId,
            'session_id' => $sessionId,
            'tenant_id' => $this->getCurrentTenantId(),
            'user_id' => $this->getCurrentUserId(),
            'request_data' => $requestData,
            'stage_results' => [],
            'metadata' => [],
        ];

        // Execute each pipeline stage
        foreach ($this->pipelineStages as $stage) {
            $pipelineContext = $this->executeStage($stage, $pipelineContext);
        }

        return [
            'operation_id' => $operationId,
            'session_id' => $sessionId,
            'response' => $pipelineContext['final_response'] ?? null,
            'metadata' => $pipelineContext['metadata'],
            'stage_results' => $pipelineContext['stage_results'],
            'execution_time_ms' => $pipelineContext['total_execution_time'] ?? 0,
            'success' => true,
        ];
    }

    /**
     * Execute individual pipeline stage
     */
    protected function executeStage(string $stage, array $context): array
    {
        $stageStartTime = microtime(true);

        try {
            $this->logActivity("Executing pipeline stage: {$stage}", [
                'operation_id' => $context['operation_id'],
                'stage' => $stage,
            ]);

            $stageResult = match ($stage) {
                'intent_classification' => $this->executeIntentClassification($context),
                'memory_retrieval' => $this->executeMemoryRetrieval($context),
                'knowledge_retrieval' => $this->executeKnowledgeRetrieval($context),
                'tool_execution' => $this->executeToolExecution($context),
                'workflow_processing' => $this->executeWorkflowProcessing($context),
                'ai_generation' => $this->executeAIGeneration($context),
                'response_formatting' => $this->executeResponseFormatting($context),
                'guardrail_validation' => $this->executeGuardrailValidation($context),
                'result_aggregation' => $this->executeResultAggregation($context),
                default => throw new \InvalidArgumentException("Unknown pipeline stage: {$stage}"),
            };

            $executionTime = round((microtime(true) - $stageStartTime) * 1000, 2);

            // Update context with stage result
            $context['stage_results'][$stage] = [
                'result' => $stageResult,
                'execution_time_ms' => $executionTime,
                'success' => true,
                'timestamp' => now()->toISOString(),
            ];

            // Update metadata
            $context['metadata']['stages_completed'][] = $stage;
            $context['metadata']['total_execution_time'] = ($context['metadata']['total_execution_time'] ?? 0) + $executionTime;

            return $context;
        } catch (\Exception $e) {
            $executionTime = round((microtime(true) - $stageStartTime) * 1000, 2);

            // Log stage failure
            $this->logActivity("Pipeline stage failed: {$stage}", [
                'operation_id' => $context['operation_id'],
                'stage' => $stage,
                'error' => $e->getMessage(),
                'execution_time_ms' => $executionTime,
            ], 'error');

            // Record failure in context
            $context['stage_results'][$stage] = [
                'result' => null,
                'execution_time_ms' => $executionTime,
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ];

            // Update metadata
            $context['metadata']['stages_failed'][] = $stage;
            $context['metadata']['total_execution_time'] = ($context['metadata']['total_execution_time'] ?? 0) + $executionTime;

            // Handle stage failure
            return $this->handleStageFailure($stage, $context, $e);
        }
    }

    /**
     * Execute intent classification stage
     */
    protected function executeIntentClassification(array $context): array
    {
        $requestData = $context['request_data'];
        $userMessage = $requestData['message'] ?? '';

        return $this->intentService->classifyIntent($userMessage, [
            'session_id' => $context['session_id'],
            'context' => $requestData['context'] ?? [],
        ]);
    }

    /**
     * Execute memory retrieval stage
     */
    protected function executeMemoryRetrieval(array $context): array
    {
        $sessionId = $context['session_id'];

        if (!$sessionId) {
            return ['memory' => [], 'context' => []];
        }

        return $this->memoryService->retrieveSessionMemory($sessionId, [
            'include_context' => true,
            'max_messages' => $this->getConfig('memory.max_context_messages', 10),
        ]);
    }

    /**
     * Execute knowledge retrieval stage
     */
    protected function executeKnowledgeRetrieval(array $context): array
    {
        $intentResult = $context['stage_results']['intent_classification']['result'] ?? [];
        $query = $context['request_data']['message'] ?? '';

        if (empty($query)) {
            return ['documents' => [], 'embeddings' => []];
        }

        return $this->retrieverService->retrieveRelevantKnowledge($query, [
            'intent' => $intentResult['intent'] ?? 'general',
            'confidence' => $intentResult['confidence'] ?? 0.5,
            'max_results' => $this->getConfig('retrieval.max_results', 5),
            'similarity_threshold' => $this->getConfig('retrieval.similarity_threshold', 0.7),
        ]);
    }

    /**
     * Execute tool execution stage
     */
    protected function executeToolExecution(array $context): array
    {
        $intentResult = $context['stage_results']['intent_classification']['result'] ?? [];
        $requiredTools = $intentResult['required_tools'] ?? [];

        if (empty($requiredTools)) {
            return ['tool_results' => [], 'executed_tools' => []];
        }

        $toolResults = [];
        foreach ($requiredTools as $tool) {
            $toolResult = $this->toolService->executeTool($tool['name'], $tool['parameters'] ?? []);
            $toolResults[$tool['name']] = $toolResult;
        }

        return [
            'tool_results' => $toolResults,
            'executed_tools' => array_keys($toolResults),
        ];
    }

    /**
     * Execute workflow processing stage
     */
    protected function executeWorkflowProcessing(array $context): array
    {
        $intentResult = $context['stage_results']['intent_classification']['result'] ?? [];
        $workflowId = $intentResult['workflow_id'] ?? null;

        if (!$workflowId) {
            return ['workflow_result' => null, 'steps_executed' => []];
        }

        return $this->workflowService->executeWorkflow($workflowId, [
            'context' => $context,
            'input_data' => $context['request_data'],
        ]);
    }

    /**
     * Execute AI generation stage
     */
    protected function executeAIGeneration(array $context): array
    {
        $intentResult = $context['stage_results']['intent_classification']['result'] ?? [];
        $prompt = $context['request_data']['message'] ?? '';

        if (empty($prompt)) {
            return ['generated_text' => null];
        }

        return $this->aiProviderService->generateText($prompt, [
            'session_id' => $context['session_id'],
            'context' => $context['request_data']['context'] ?? [],
        ]);
    }

    /**
     * Execute response formatting stage
     */
    protected function executeResponseFormatting(array $context): array
    {
        $allResults = $context['stage_results'];
        $requestData = $context['request_data'];

        return $this->formatterService->formatResponse([
            'intent' => $allResults['intent_classification']['result'] ?? [],
            'memory' => $allResults['memory_retrieval']['result'] ?? [],
            'knowledge' => $allResults['knowledge_retrieval']['result'] ?? [],
            'tools' => $allResults['tool_execution']['result'] ?? [],
            'workflow' => $allResults['workflow_processing']['result'] ?? [],
            'ai_generation' => $allResults['ai_generation']['result'] ?? [],
            'original_request' => $requestData,
        ]);
    }

    /**
     * Execute guardrail validation stage
     */
    protected function executeGuardrailValidation(array $context): array
    {
        $formattedResponse = $context['stage_results']['response_formatting']['result'] ?? [];

        return $this->guardrailService->validateResponse($formattedResponse, [
            'tenant_id' => $this->getCurrentTenantId(),
            'user_id' => $this->getCurrentUserId(),
            'context' => $context,
        ]);
    }

    /**
     * Execute result aggregation stage
     */
    protected function executeResultAggregation(array $context): array
    {
        $guardrailResult = $context['stage_results']['guardrail_validation']['result'] ?? [];
        $formattedResponse = $context['stage_results']['response_formatting']['result'] ?? [];

        // If guardrails failed, use fallback response
        if (!($guardrailResult['passed'] ?? true)) {
            $finalResponse = $guardrailResult['fallback_response'] ?? [
                'message' => 'I apologize, but I cannot provide a response to that request.',
                'type' => 'error',
                'reason' => 'guardrail_violation',
            ];
        } else {
            $finalResponse = $formattedResponse;
        }

        // Update context with final response
        $context['final_response'] = $finalResponse;

        return [
            'final_response' => $finalResponse,
            'guardrail_passed' => $guardrailResult['passed'] ?? true,
            'aggregation_metadata' => [
                'stages_completed' => count($context['stage_results']),
                'total_execution_time' => $context['metadata']['total_execution_time'] ?? 0,
            ],
        ];
    }

    /**
     * Handle stage failure with recovery mechanisms
     */
    protected function handleStageFailure(string $stage, array $context, \Exception $exception): array
    {
        $recoveryStrategy = $this->getConfig("recovery.{$stage}", 'fail');

        switch ($recoveryStrategy) {
            case 'skip':
                $this->logActivity("Skipping failed stage: {$stage}", [
                    'operation_id' => $context['operation_id'],
                    'error' => $exception->getMessage(),
                ], 'warning');
                return $context;

            case 'fallback':
                return $this->executeFallbackStrategy($stage, $context, $exception);

            case 'retry':
                return $this->retryStage($stage, $context);

            default:
                throw $exception;
        }
    }

    /**
     * Execute fallback strategy for failed stage
     */
    protected function executeFallbackStrategy(string $stage, array $context, \Exception $exception): array
    {
        $fallbackResult = match ($stage) {
            'intent_classification' => ['intent' => 'general', 'confidence' => 0.1],
            'memory_retrieval' => ['memory' => [], 'context' => []],
            'knowledge_retrieval' => ['documents' => [], 'embeddings' => []],
            'tool_execution' => ['tool_results' => [], 'executed_tools' => []],
            'workflow_processing' => ['workflow_result' => null, 'steps_executed' => []],
            'response_formatting' => ['message' => 'I apologize, but I encountered an error processing your request.'],
            'guardrail_validation' => ['passed' => false, 'violations' => ['system_error']],
            'result_aggregation' => ['final_response' => ['message' => 'System error occurred.']],
            default => [],
        };

        $context['stage_results'][$stage] = [
            'result' => $fallbackResult,
            'success' => false,
            'fallback_used' => true,
            'original_error' => $exception->getMessage(),
            'timestamp' => now()->toISOString(),
        ];

        return $context;
    }

    /**
     * Retry failed stage
     */
    protected function retryStage(string $stage, array $context): array
    {
        $maxRetries = $this->getConfig('retry.max_attempts', 3);
        $currentRetries = $context['metadata']['retries'][$stage] ?? 0;

        if ($currentRetries >= $maxRetries) {
            throw new \Exception("Stage {$stage} failed after {$maxRetries} retries");
        }

        $context['metadata']['retries'][$stage] = $currentRetries + 1;

        // Wait before retry
        $retryDelay = $this->getConfig('retry.delay_ms', 1000);
        usleep($retryDelay * 1000);

        return $this->executeStage($stage, $context);
    }

    /**
     * Initialize operation tracking
     */
    protected function initializeOperation(string $operationId, array $requestData): void
    {
        $this->activeOperations[$operationId] = [
            'id' => $operationId,
            'status' => 'running',
            'started_at' => now(),
            'request_data' => $requestData,
            'tenant_id' => $this->getCurrentTenantId(),
            'user_id' => $this->getCurrentUserId(),
        ];

        $this->loggingService->logMCPOperation([
            'operation_id' => $operationId,
            'operation_type' => 'initialize',
            'status' => 'started',
            'request_data' => $requestData,
        ]);
    }

    /**
     * Complete operation tracking
     */
    protected function completeOperation(string $operationId, array $result): void
    {
        if (isset($this->activeOperations[$operationId])) {
            $this->activeOperations[$operationId]['status'] = 'completed';
            $this->activeOperations[$operationId]['completed_at'] = now();
            $this->activeOperations[$operationId]['result'] = $result;
        }

        $this->loggingService->logMCPOperation([
            'operation_id' => $operationId,
            'operation_type' => 'complete',
            'status' => 'completed',
            'result_summary' => [
                'success' => $result['success'] ?? false,
                'stages_completed' => count($result['stage_results'] ?? []),
                'execution_time_ms' => $result['execution_time_ms'] ?? 0,
            ],
        ]);
    }

    /**
     * Fail operation tracking
     */
    protected function failOperation(string $operationId, \Exception $exception): void
    {
        if (isset($this->activeOperations[$operationId])) {
            $this->activeOperations[$operationId]['status'] = 'failed';
            $this->activeOperations[$operationId]['failed_at'] = now();
            $this->activeOperations[$operationId]['error'] = $exception->getMessage();
        }

        $this->loggingService->logMCPOperation([
            'operation_id' => $operationId,
            'operation_type' => 'fail',
            'status' => 'failed',
            'error' => $exception->getMessage(),
        ]);
    }

    /**
     * Generate unique operation ID
     */
    protected function generateOperationId(): string
    {
        return 'MCP_' . strtoupper(Str::random(8)) . '_' . time();
    }

    /**
     * Get active operations count
     */
    public function getActiveOperationsCount(): int
    {
        return count(array_filter($this->activeOperations, fn($op) => $op['status'] === 'running'));
    }

    /**
     * Get operation status
     */
    public function getOperationStatus(string $operationId): ?array
    {
        return $this->activeOperations[$operationId] ?? null;
    }

    /**
     * Cancel operation
     */
    public function cancelOperation(string $operationId): bool
    {
        if (isset($this->activeOperations[$operationId]) && $this->activeOperations[$operationId]['status'] === 'running') {
            $this->activeOperations[$operationId]['status'] = 'cancelled';
            $this->activeOperations[$operationId]['cancelled_at'] = now();
            return true;
        }

        return false;
    }

    /**
     * Clean up completed operations
     */
    public function cleanupOperations(): int
    {
        $cleanupThreshold = now()->subHours($this->getConfig('cleanup.operation_retention_hours', 24));
        $cleaned = 0;

        foreach ($this->activeOperations as $operationId => $operation) {
            $completedAt = $operation['completed_at'] ?? $operation['failed_at'] ?? $operation['cancelled_at'] ?? null;

            if ($completedAt && $completedAt < $cleanupThreshold) {
                unset($this->activeOperations[$operationId]);
                $cleaned++;
            }
        }

        return $cleaned;
    }
}
