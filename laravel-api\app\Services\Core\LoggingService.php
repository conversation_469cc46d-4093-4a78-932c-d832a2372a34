<?php

namespace App\Services\Core;

use App\Models\SystemLog;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class LoggingService
{
    /**
     * Available log channels
     */
    protected array $channels = [
        'http_request',
        'websocket_event',
        'ai_provider_request',
        'mcp_operation',
        'service',
        'error',
        'security',
        'performance',
        'cache',
        'database',
        'system'
    ];

    /**
     * Log levels with priority
     */
    protected array $logLevels = [
        'emergency' => 0,
        'alert' => 1,
        'critical' => 2,
        'error' => 3,
        'warning' => 4,
        'notice' => 5,
        'info' => 6,
        'debug' => 7,
    ];

    /**
     * Current tenant context
     */
    protected ?Tenant $currentTenant = null;

    /**
     * Current user context
     */
    protected ?User $currentUser = null;

    /**
     * Log configuration
     */
    protected array $config = [
        'enabled' => true,
        'database_logging' => true,
        'file_logging' => true,
        'real_time_streaming' => false,
        'retention_days' => 30,
        'max_log_size_mb' => 100,
        'batch_size' => 100,
    ];

    /**
     * Batch logging queue
     */
    protected array $logQueue = [];

    public function __construct()
    {
        $this->loadConfiguration();
        $this->setCurrentContext();
    }

    /**
     * Main logging method
     */
    public function log(array $logData): void
    {
        if (!$this->config['enabled']) {
            return;
        }

        // Validate and normalize log data
        $normalizedData = $this->normalizeLogData($logData);

        // Add context information
        $normalizedData = $this->addContextInformation($normalizedData);

        // Log to database if enabled
        if ($this->config['database_logging']) {
            $this->logToDatabase($normalizedData);
        }

        // Log to file system
        if ($this->config['file_logging']) {
            $this->logToFile($normalizedData);
        }

        // Stream in real-time if enabled
        if ($this->config['real_time_streaming']) {
            $this->streamRealTime($normalizedData);
        }
    }

    /**
     * Log performance metrics
     */
    public function logPerformance(array $performanceData): void
    {
        $this->log(array_merge([
            'log_type' => 'performance',
            'log_level' => 'info',
            'message' => 'Performance metrics',
        ], $performanceData));
    }

    /**
     * Log HTTP request
     */
    public function logHttpRequest(array $requestData): void
    {
        $this->log(array_merge([
            'log_type' => 'http_request',
            'log_level' => 'info',
            'message' => 'HTTP request processed',
        ], $requestData));
    }

    /**
     * Log WebSocket event
     */
    public function logWebSocketEvent(array $eventData): void
    {
        $this->log(array_merge([
            'log_type' => 'websocket_event',
            'log_level' => 'info',
            'message' => 'WebSocket event processed',
        ], $eventData));
    }

    /**
     * Log AI provider interaction
     */
    public function logAIProviderRequest(array $providerData): void
    {
        $this->log(array_merge([
            'log_type' => 'ai_provider_request',
            'log_level' => 'info',
            'message' => 'AI provider request processed',
        ], $providerData));
    }

    /**
     * Log MCP operation
     */
    public function logMCPOperation(array $operationData): void
    {
        $this->log(array_merge([
            'log_type' => 'mcp_operation',
            'log_level' => 'info',
            'message' => 'MCP operation executed',
        ], $operationData));
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(array $securityData): void
    {
        $this->log(array_merge([
            'log_type' => 'security',
            'log_level' => 'warning',
            'message' => 'Security event detected',
            'is_security_incident' => true,
        ], $securityData));
    }

    /**
     * Batch logging for high-volume operations
     */
    public function addToBatch(array $logData): void
    {
        $this->logQueue[] = $this->normalizeLogData($logData);

        if (count($this->logQueue) >= $this->config['batch_size']) {
            $this->flushBatch();
        }
    }

    /**
     * Flush batch queue
     */
    public function flushBatch(): void
    {
        if (empty($this->logQueue)) {
            return;
        }

        foreach ($this->logQueue as $logData) {
            $this->log($logData);
        }

        $this->logQueue = [];
    }

    /**
     * Get logs with filtering
     */
    public function getLogs(array $filters = [], int $limit = 100, int $offset = 0): array
    {
        $query = SystemLog::query();

        // Apply filters
        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['log_type'])) {
            $query->where('log_type', $filters['log_type']);
        }

        if (isset($filters['log_level'])) {
            $query->where('log_level', $filters['log_level']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('message', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('context', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (isset($filters['is_security_incident'])) {
            $query->where('is_security_incident', $filters['is_security_incident']);
        }

        return $query->orderBy('created_at', 'desc')
                    ->limit($limit)
                    ->offset($offset)
                    ->get()
                    ->toArray();
    }

    /**
     * Get log statistics
     */
    public function getLogStatistics(array $filters = []): array
    {
        $query = SystemLog::query();

        // Apply tenant filter if provided
        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        // Apply date range filter
        $dateFrom = $filters['date_from'] ?? now()->subDays(7);
        $dateTo = $filters['date_to'] ?? now();
        $query->whereBetween('created_at', [$dateFrom, $dateTo]);

        $totalLogs = $query->count();

        $byLogType = $query->selectRaw('log_type, COUNT(*) as count')
                          ->groupBy('log_type')
                          ->pluck('count', 'log_type')
                          ->toArray();

        $byLogLevel = $query->selectRaw('log_level, COUNT(*) as count')
                           ->groupBy('log_level')
                           ->pluck('count', 'log_level')
                           ->toArray();

        $securityIncidents = $query->where('is_security_incident', true)->count();

        $errorCount = $query->whereIn('log_level', ['error', 'critical', 'emergency'])->count();

        return [
            'total_logs' => $totalLogs,
            'by_log_type' => $byLogType,
            'by_log_level' => $byLogLevel,
            'security_incidents' => $securityIncidents,
            'error_count' => $errorCount,
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo,
            ],
        ];
    }

    /**
     * Clean old logs based on retention policy
     */
    public function cleanOldLogs(): int
    {
        $retentionDate = now()->subDays($this->config['retention_days']);

        return SystemLog::where('created_at', '<', $retentionDate)->delete();
    }

    /**
     * Export logs to file
     */
    public function exportLogs(array $filters = [], string $format = 'json'): string
    {
        $logs = $this->getLogs($filters, 10000); // Large limit for export

        $filename = 'logs_export_' . now()->format('Y-m-d_H-i-s') . '.' . $format;
        $filepath = storage_path('app/exports/' . $filename);

        // Ensure directory exists
        if (!is_dir(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        switch ($format) {
            case 'json':
                file_put_contents($filepath, json_encode($logs, JSON_PRETTY_PRINT));
                break;
            case 'csv':
                $this->exportToCsv($logs, $filepath);
                break;
            default:
                throw new \InvalidArgumentException("Unsupported export format: {$format}");
        }

        return $filepath;
    }

    /**
     * Normalize log data structure
     */
    protected function normalizeLogData(array $logData): array
    {
        $normalized = [
            'id' => Str::uuid(),
            'tenant_id' => $logData['tenant_id'] ?? $this->currentTenant?->id,
            'user_id' => $logData['user_id'] ?? $this->currentUser?->id,
            'log_type' => $logData['log_type'] ?? 'system',
            'log_level' => $logData['log_level'] ?? 'info',
            'message' => $logData['message'] ?? 'No message provided',
            'context' => $logData['context'] ?? [],
            'metadata' => $logData['metadata'] ?? [],
            'ip_address' => $logData['ip_address'] ?? request()?->ip(),
            'user_agent' => $logData['user_agent'] ?? request()?->userAgent(),
            'request_id' => $logData['request_id'] ?? $this->generateRequestId(),
            'session_id' => $logData['session_id'] ?? session()?->getId(),
            'url' => $logData['url'] ?? request()?->fullUrl(),
            'method' => $logData['method'] ?? request()?->method(),
            'response_time_ms' => $logData['response_time_ms'] ?? null,
            'memory_usage_mb' => $logData['memory_usage_mb'] ?? round(memory_get_usage(true) / 1024 / 1024, 2),
            'error_message' => $logData['error_message'] ?? null,
            'error_code' => $logData['error_code'] ?? null,
            'stack_trace' => $logData['stack_trace'] ?? null,
            'security_event_type' => $logData['security_event_type'] ?? null,
            'is_security_incident' => $logData['is_security_incident'] ?? false,
            'created_at' => now(),
        ];

        // Validate log type
        if (!in_array($normalized['log_type'], $this->channels)) {
            $normalized['log_type'] = 'system';
        }

        // Validate log level
        if (!array_key_exists($normalized['log_level'], $this->logLevels)) {
            $normalized['log_level'] = 'info';
        }

        return $normalized;
    }

    /**
     * Add contextual information to log data
     */
    protected function addContextInformation(array $logData): array
    {
        $logData['server_name'] = gethostname();
        $logData['php_version'] = PHP_VERSION;
        $logData['laravel_version'] = app()->version();
        $logData['environment'] = app()->environment();

        return $logData;
    }

    /**
     * Log to database
     */
    protected function logToDatabase(array $logData): void
    {
        try {
            SystemLog::create($logData);
        } catch (\Exception $e) {
            // Fallback to file logging if database fails
            Log::error('Failed to log to database', [
                'error' => $e->getMessage(),
                'original_log' => $logData,
            ]);
        }
    }

    /**
     * Log to file system
     */
    protected function logToFile(array $logData): void
    {
        $channel = $logData['log_type'];
        $level = $logData['log_level'];

        Log::channel($channel)->log($level, $logData['message'], $logData);
    }

    /**
     * Stream logs in real-time
     */
    protected function streamRealTime(array $logData): void
    {
        // This would integrate with WebSocket broadcasting
        // For now, we'll just log that streaming was requested
        Log::info('Real-time log streaming requested', [
            'log_id' => $logData['id'],
            'log_type' => $logData['log_type'],
        ]);
    }

    /**
     * Load logging configuration
     */
    protected function loadConfiguration(): void
    {
        $this->config = array_merge($this->config, config('logging.custom', []));
    }

    /**
     * Set current context (tenant and user)
     */
    protected function setCurrentContext(): void
    {
        $this->currentUser = Auth::user();

        // Get tenant from user or request
        if ($this->currentUser) {
            $this->currentTenant = $this->currentUser->tenant;
        } elseif (request() && request()->has('tenant')) {
            $this->currentTenant = request()->get('tenant');
        }
    }

    /**
     * Generate unique request ID
     */
    protected function generateRequestId(): string
    {
        return 'REQ_' . strtoupper(Str::random(8)) . '_' . time();
    }

    /**
     * Export logs to CSV format
     */
    protected function exportToCsv(array $logs, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        if (!empty($logs)) {
            fputcsv($file, array_keys($logs[0]));
        }

        // Write data
        foreach ($logs as $log) {
            fputcsv($file, $log);
        }

        fclose($file);
    }

    /**
     * Get service health status
     */
    public function getHealthStatus(): array
    {
        $recentErrors = SystemLog::where('log_level', 'error')
                                ->where('created_at', '>', now()->subHour())
                                ->count();

        $totalLogs = SystemLog::count();
        $oldestLog = SystemLog::oldest()->first();

        return [
            'service' => 'logging',
            'status' => $this->config['enabled'] ? 'enabled' : 'disabled',
            'database_logging' => $this->config['database_logging'],
            'file_logging' => $this->config['file_logging'],
            'total_logs' => $totalLogs,
            'recent_errors' => $recentErrors,
            'oldest_log_date' => $oldestLog?->created_at,
            'retention_days' => $this->config['retention_days'],
            'timestamp' => now()->toISOString(),
        ];
    }
}
