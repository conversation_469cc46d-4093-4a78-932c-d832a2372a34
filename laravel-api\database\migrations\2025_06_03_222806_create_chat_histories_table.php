<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_histories', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id');
            $table->uuid('user_id');
            $table->uuid('websocket_session_id')->nullable();

            // Session Information
            $table->string('session_id');
            $table->string('conversation_id')->nullable(); // For grouping related messages
            $table->integer('message_sequence')->default(1);

            // Message Content
            $table->enum('message_type', ['user', 'assistant', 'system', 'tool', 'error'])->default('user');
            $table->text('message_content');
            $table->json('message_metadata')->nullable(); // Attachments, formatting, etc.
            $table->text('original_prompt')->nullable(); // Before processing
            $table->text('processed_prompt')->nullable(); // After MCP processing

            // AI Provider Information
            $table->uuid('ai_provider_id')->nullable();
            $table->string('ai_model_used')->nullable();
            $table->json('ai_model_parameters')->nullable(); // temperature, max_tokens, etc.
            $table->decimal('input_tokens', 15, 0)->nullable();
            $table->decimal('output_tokens', 15, 0)->nullable();
            $table->decimal('total_tokens', 15, 0)->nullable();
            $table->decimal('estimated_cost', 10, 6)->nullable();

            // MCP Processing Information
            $table->json('mcp_agents_used')->nullable(); // Which agents were involved
            $table->json('intent_classification')->nullable(); // Intent service results
            $table->json('retrieval_results')->nullable(); // RAG results
            $table->json('tools_executed')->nullable(); // Tool service results
            $table->json('workflow_steps')->nullable(); // Workflow execution
            $table->json('memory_context')->nullable(); // Memory service data
            $table->json('guardrail_checks')->nullable(); // Guardrail results

            // Performance Metrics
            $table->decimal('total_processing_time_ms', 10, 2)->nullable();
            $table->decimal('ai_response_time_ms', 10, 2)->nullable();
            $table->decimal('mcp_processing_time_ms', 10, 2)->nullable();
            $table->decimal('retrieval_time_ms', 10, 2)->nullable();
            $table->decimal('tool_execution_time_ms', 10, 2)->nullable();

            // Quality & Feedback
            $table->integer('user_rating')->nullable(); // 1-5 stars
            $table->text('user_feedback')->nullable();
            $table->boolean('was_helpful')->nullable();
            $table->json('quality_metrics')->nullable(); // Automated quality assessment

            // Error Handling
            $table->boolean('has_errors')->default(false);
            $table->json('error_details')->nullable();
            $table->text('error_message')->nullable();
            $table->enum('error_type', ['ai_provider', 'mcp_agent', 'tool', 'system', 'validation'])->nullable();
            $table->boolean('was_retried')->default(false);
            $table->integer('retry_count')->default(0);

            // Content Analysis
            $table->json('content_tags')->nullable(); // Auto-generated tags
            $table->enum('content_category', ['general', 'technical', 'creative', 'analytical', 'code'])->nullable();
            $table->enum('sentiment', ['positive', 'neutral', 'negative'])->nullable();
            $table->integer('complexity_score')->nullable(); // 1-10

            // Compliance & Security
            $table->boolean('contains_pii')->default(false);
            $table->boolean('contains_sensitive_data')->default(false);
            $table->json('compliance_flags')->nullable();
            $table->boolean('is_archived')->default(false);
            $table->timestamp('archived_at')->nullable();

            // Context & References
            $table->uuid('parent_message_id')->nullable(); // For threaded conversations
            $table->json('referenced_documents')->nullable(); // Documents used in RAG
            $table->json('external_references')->nullable(); // URLs, APIs called
            $table->json('context_window')->nullable(); // Previous messages used

            // Real-time Information
            $table->timestamp('message_sent_at');
            $table->timestamp('processing_started_at')->nullable();
            $table->timestamp('processing_completed_at')->nullable();
            $table->timestamp('response_delivered_at')->nullable();
            $table->boolean('is_streaming')->default(false);
            $table->json('streaming_chunks')->nullable(); // For streaming responses

            // Status & Metadata
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('websocket_session_id')->references('id')->on('websocket_sessions')->onDelete('set null');
            $table->foreign('ai_provider_id')->references('id')->on('ai_providers')->onDelete('set null');
            $table->foreign('parent_message_id')->references('id')->on('chat_histories')->onDelete('set null');

            // Indexes
            $table->index(['tenant_id', 'user_id']);
            $table->index(['session_id', 'message_sequence']);
            $table->index(['conversation_id', 'message_sequence']);
            $table->index(['tenant_id', 'message_type']);
            $table->index(['ai_provider_id', 'status']);
            $table->index(['message_sent_at', 'status']);
            $table->index(['has_errors', 'error_type']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_histories');
    }
};
