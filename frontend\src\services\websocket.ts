// Axient MCP++ WebSocket Service

import io from 'socket.io-client';
import { config } from '@/lib/config';
import { apiClient } from './api';
import {
    WebSocketEvent,
    WebSocketEventType,
    TypingIndicator,
    UserPresence,
    ChatMessage
} from '@/types';
import { getErrorMessage, retry } from '@/utils';

// ============================================================================
// WebSocket Event Handlers
// ============================================================================

export type WebSocketEventHandler<T = any> = (data: T) => void;

export interface WebSocketEventMap {
    'chat.message.received': ChatMessage;
    'chat.message.processed': ChatMessage;
    'chat.typing.start': TypingIndicator;
    'chat.typing.stop': TypingIndicator;
    'user.presence.online': UserPresence;
    'user.presence.offline': UserPresence;
    'ai.provider.request': any;
    'ai.provider.response': any;
    'system.notification': any;
    'error': { message: string; code?: string };
    'connected': void;
    'disconnected': void;
    'reconnecting': { attempt: number };
    'reconnected': void;
}

// ============================================================================
// WebSocket Service Class
// ============================================================================

class WebSocketService {
    private socket: any = null;
    private isConnected = false;
    private isConnecting = false;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = config.websocket.reconnectAttempts;
    private reconnectDelay = config.websocket.reconnectDelay;
    private heartbeatInterval: NodeJS.Timeout | null = null;
    private eventHandlers = new Map<string, Set<WebSocketEventHandler>>();
    private authToken: string | null = null;

    // ============================================================================
    // Connection Management
    // ============================================================================

    public async connect(): Promise<void> {
        if (this.isConnected || this.isConnecting) {
            return;
        }

        this.isConnecting = true;

        try {
            // Get WebSocket authentication token
            const authResponse = await apiClient.authenticateWebSocket();
            if (!authResponse.success || !authResponse.data?.token) {
                throw new Error('Failed to get WebSocket authentication token');
            }

            this.authToken = authResponse.data.token;

            // Create socket connection
            this.socket = io(config.websocket.url, {
                auth: {
                    token: this.authToken,
                },
                transports: ['websocket'],
                upgrade: true,
                rememberUpgrade: true,
                timeout: 20000,
                forceNew: true,
            });

            this.setupEventListeners();

            // Wait for connection
            await new Promise<void>((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('WebSocket connection timeout'));
                }, 10000);

                this.socket!.on('connect', () => {
                    clearTimeout(timeout);
                    resolve();
                });

                this.socket!.on('connect_error', (error: any) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });

            this.isConnected = true;
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            this.emit('connected', undefined);

        } catch (error) {
            this.isConnecting = false;
            this.handleConnectionError(error);
            throw error;
        }
    }

    public disconnect(): void {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }

        this.isConnected = false;
        this.isConnecting = false;
        this.stopHeartbeat();
        this.emit('disconnected', undefined);
    }

    public async reconnect(): Promise<void> {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }

        this.reconnectAttempts++;
        this.emit('reconnecting', { attempt: this.reconnectAttempts });

        try {
            this.disconnect();
            await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
            await this.connect();
            this.emit('reconnected', undefined);
        } catch (error) {
            console.error('Reconnection failed:', error);
            // Exponential backoff
            this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
            setTimeout(() => this.reconnect(), this.reconnectDelay);
        }
    }

    // ============================================================================
    // Event Handling
    // ============================================================================

    private setupEventListeners(): void {
        if (!this.socket) return;

        // Connection events
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.reconnectDelay = config.websocket.reconnectDelay;
        });

        this.socket.on('disconnect', (reason: any) => {
            this.isConnected = false;
            this.stopHeartbeat();
            this.emit('disconnected', undefined);

            // Auto-reconnect unless manually disconnected
            if (reason !== 'io client disconnect') {
                setTimeout(() => this.reconnect(), this.reconnectDelay);
            }
        });

        this.socket.on('connect_error', (error: any) => {
            this.handleConnectionError(error);
        });

        // Application events
        this.socket.on('chat.message.received', (data: ChatMessage) => {
            this.emit('chat.message.received', data);
        });

        this.socket.on('chat.message.processed', (data: ChatMessage) => {
            this.emit('chat.message.processed', data);
        });

        this.socket.on('chat.typing.start', (data: TypingIndicator) => {
            this.emit('chat.typing.start', data);
        });

        this.socket.on('chat.typing.stop', (data: TypingIndicator) => {
            this.emit('chat.typing.stop', data);
        });

        this.socket.on('user.presence.online', (data: UserPresence) => {
            this.emit('user.presence.online', data);
        });

        this.socket.on('user.presence.offline', (data: UserPresence) => {
            this.emit('user.presence.offline', data);
        });

        this.socket.on('ai.provider.request', (data: any) => {
            this.emit('ai.provider.request', data);
        });

        this.socket.on('ai.provider.response', (data: any) => {
            this.emit('ai.provider.response', data);
        });

        this.socket.on('system.notification', (data: any) => {
            this.emit('system.notification', data);
        });

        this.socket.on('error', (data: any) => {
            this.emit('error', data);
        });

        // Heartbeat response
        this.socket.on('pong', () => {
            // Heartbeat acknowledged
        });
    }

    private handleConnectionError(error: any): void {
        console.error('WebSocket connection error:', error);
        this.isConnected = false;
        this.isConnecting = false;

        this.emit('error', {
            message: getErrorMessage(error),
            code: 'CONNECTION_ERROR',
        });
    }

    // ============================================================================
    // Event Subscription
    // ============================================================================

    public on<K extends keyof WebSocketEventMap>(
        event: K,
        handler: WebSocketEventHandler<WebSocketEventMap[K]>
    ): void {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, new Set());
        }
        this.eventHandlers.get(event)!.add(handler);
    }

    public off<K extends keyof WebSocketEventMap>(
        event: K,
        handler: WebSocketEventHandler<WebSocketEventMap[K]>
    ): void {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            handlers.delete(handler);
            if (handlers.size === 0) {
                this.eventHandlers.delete(event);
            }
        }
    }

    public once<K extends keyof WebSocketEventMap>(
        event: K,
        handler: WebSocketEventHandler<WebSocketEventMap[K]>
    ): void {
        const onceHandler = (data: WebSocketEventMap[K]) => {
            handler(data);
            this.off(event, onceHandler);
        };
        this.on(event, onceHandler);
    }

    private emit<K extends keyof WebSocketEventMap>(
        event: K,
        data: WebSocketEventMap[K]
    ): void {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in WebSocket event handler for ${event}:`, error);
                }
            });
        }
    }

    // ============================================================================
    // Message Sending
    // ============================================================================

    public send<T = any>(event: string, data: T): void {
        if (!this.isConnected || !this.socket) {
            console.warn('WebSocket not connected, message queued:', event, data);
            // TODO: Implement message queuing for offline scenarios
            return;
        }

        this.socket.emit(event, data);
    }

    // ============================================================================
    // Chat-specific Methods
    // ============================================================================

    public sendChatMessage(sessionId: string, content: string): void {
        this.send('chat.message.send', {
            session_id: sessionId,
            content,
            timestamp: new Date().toISOString(),
        });
    }

    public startTyping(sessionId: string): void {
        this.send('chat.typing.start', {
            session_id: sessionId,
            timestamp: new Date().toISOString(),
        });
    }

    public stopTyping(sessionId: string): void {
        this.send('chat.typing.stop', {
            session_id: sessionId,
            timestamp: new Date().toISOString(),
        });
    }

    public joinChatSession(sessionId: string): void {
        this.send('chat.session.join', {
            session_id: sessionId,
        });
    }

    public leaveChatSession(sessionId: string): void {
        this.send('chat.session.leave', {
            session_id: sessionId,
        });
    }

    // ============================================================================
    // Presence Methods
    // ============================================================================

    public updatePresence(status: 'online' | 'away' | 'busy'): void {
        this.send('user.presence.update', {
            status,
            timestamp: new Date().toISOString(),
        });
    }

    public setUserOnline(): void {
        this.updatePresence('online');
    }

    public setUserAway(): void {
        this.updatePresence('away');
    }

    public setUserBusy(): void {
        this.updatePresence('busy');
    }

    // ============================================================================
    // Channel Management
    // ============================================================================

    public joinChannel(channel: string): void {
        this.send('channel.join', { channel });
    }

    public leaveChannel(channel: string): void {
        this.send('channel.leave', { channel });
    }

    public subscribeToTenantChannel(tenantId: string): void {
        this.joinChannel(`tenant.${tenantId}`);
    }

    public subscribeToUserChannel(userId: string): void {
        this.joinChannel(`user.${userId}`);
    }

    // ============================================================================
    // Heartbeat
    // ============================================================================

    private startHeartbeat(): void {
        this.stopHeartbeat();
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.socket) {
                this.socket.emit('ping');
            }
        }, config.websocket.heartbeatInterval);
    }

    private stopHeartbeat(): void {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    // ============================================================================
    // Status Methods
    // ============================================================================

    public getConnectionStatus(): {
        isConnected: boolean;
        isConnecting: boolean;
        reconnectAttempts: number;
    } {
        return {
            isConnected: this.isConnected,
            isConnecting: this.isConnecting,
            reconnectAttempts: this.reconnectAttempts,
        };
    }

    public isSocketConnected(): boolean {
        return this.isConnected && this.socket?.connected === true;
    }

    // ============================================================================
    // Utility Methods
    // ============================================================================

    public async waitForConnection(timeout = 10000): Promise<void> {
        if (this.isConnected) return;

        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                this.off('connected', onConnected);
                reject(new Error('WebSocket connection timeout'));
            }, timeout);

            const onConnected = () => {
                clearTimeout(timeoutId);
                resolve();
            };

            this.once('connected', onConnected);

            if (!this.isConnecting) {
                this.connect().catch(reject);
            }
        });
    }

    public getEventHandlerCount(event: keyof WebSocketEventMap): number {
        return this.eventHandlers.get(event)?.size || 0;
    }

    public clearAllEventHandlers(): void {
        this.eventHandlers.clear();
    }
}

// ============================================================================
// Typing Indicator Manager
// ============================================================================

export class TypingIndicatorManager {
    private typingTimeouts = new Map<string, NodeJS.Timeout>();
    private isTyping = false;
    private currentSessionId: string | null = null;

    constructor(private websocketService: WebSocketService) { }

    public startTyping(sessionId: string): void {
        if (this.isTyping && this.currentSessionId === sessionId) {
            return; // Already typing in this session
        }

        // Stop typing in previous session if different
        if (this.isTyping && this.currentSessionId !== sessionId) {
            this.stopTyping();
        }

        this.isTyping = true;
        this.currentSessionId = sessionId;
        this.websocketService.startTyping(sessionId);

        // Auto-stop typing after timeout
        const timeout = setTimeout(() => {
            this.stopTyping();
        }, config.chat.typingIndicatorTimeout);

        this.typingTimeouts.set(sessionId, timeout);
    }

    public stopTyping(): void {
        if (!this.isTyping || !this.currentSessionId) {
            return;
        }

        this.websocketService.stopTyping(this.currentSessionId);

        const timeout = this.typingTimeouts.get(this.currentSessionId);
        if (timeout) {
            clearTimeout(timeout);
            this.typingTimeouts.delete(this.currentSessionId);
        }

        this.isTyping = false;
        this.currentSessionId = null;
    }

    public isCurrentlyTyping(): boolean {
        return this.isTyping;
    }

    public getCurrentSession(): string | null {
        return this.currentSessionId;
    }
}

// ============================================================================
// Export Singleton Instance
// ============================================================================

export const websocketService = new WebSocketService();
export const typingIndicatorManager = new TypingIndicatorManager(websocketService);

export default websocketService; 