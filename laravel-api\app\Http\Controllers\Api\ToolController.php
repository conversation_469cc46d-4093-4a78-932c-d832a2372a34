<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Core\TenantContextService;
use App\Services\Core\LoggingService;
use App\Services\MCP\ToolService;
use App\Models\Tool;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ToolController extends Controller
{
    protected TenantContextService $tenantContext;
    protected LoggingService $loggingService;
    protected ToolService $toolService;

    public function __construct(
        TenantContextService $tenantContext,
        LoggingService $loggingService,
        ToolService $toolService
    ) {
        $this->tenantContext = $tenantContext;
        $this->loggingService = $loggingService;
        $this->toolService = $toolService;
    }

    /**
     * Get all tools for the tenant
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $tools = Tool::where('tenant_id', $tenant->id)
                ->orderBy('name')
                ->paginate(20);

            $this->loggingService->logHttpRequest('tools_retrieved', [
                'tenant_id' => $tenant->id,
                'tools_count' => $tools->count(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $tools,
                'message' => 'Tools retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tools_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tools',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new tool
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'type' => 'required|string|in:api,webhook,function,database,file_system,web_scraper',
                'configuration' => 'required|array',
                'input_schema' => 'nullable|array',
                'output_schema' => 'nullable|array',
                'is_active' => 'nullable|boolean',
                'timeout_seconds' => 'nullable|integer|min:1|max:300',
                'retry_attempts' => 'nullable|integer|min:0|max:5',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Check if tool name already exists for this tenant
            $existingTool = Tool::where('tenant_id', $tenant->id)
                ->where('name', $request->input('name'))
                ->first();

            if ($existingTool) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool with this name already exists'
                ], 422);
            }

            $tool = Tool::create([
                'id' => Str::uuid(),
                'tenant_id' => $tenant->id,
                'name' => $request->input('name'),
                'description' => $request->input('description'),
                'type' => $request->input('type'),
                'configuration' => $request->input('configuration'),
                'input_schema' => $request->input('input_schema', []),
                'output_schema' => $request->input('output_schema', []),
                'is_active' => $request->input('is_active', true),
                'timeout_seconds' => $request->input('timeout_seconds', 30),
                'retry_attempts' => $request->input('retry_attempts', 3),
                'execution_count' => 0,
                'success_count' => 0,
                'error_count' => 0,
            ]);

            $this->loggingService->logHttpRequest('tool_created', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'tool_id' => $tool->id,
                'tool_name' => $tool->name,
                'tool_type' => $tool->type,
            ]);

            return response()->json([
                'success' => true,
                'data' => $tool,
                'message' => 'Tool created successfully'
            ], 201);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tool_creation_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to create tool',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific tool
     */
    public function show(Request $request, string $toolId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $tool = Tool::where('id', $toolId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$tool) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool not found'
                ], 404);
            }

            $this->loggingService->logHttpRequest('tool_retrieved', [
                'tenant_id' => $tenant->id,
                'tool_id' => $toolId,
            ]);

            return response()->json([
                'success' => true,
                'data' => $tool,
                'message' => 'Tool retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tool_retrieval_error', [
                'tool_id' => $toolId,
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tool',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a tool
     */
    public function update(Request $request, string $toolId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:255',
                'description' => 'nullable|string|max:1000',
                'configuration' => 'nullable|array',
                'input_schema' => 'nullable|array',
                'output_schema' => 'nullable|array',
                'is_active' => 'nullable|boolean',
                'timeout_seconds' => 'nullable|integer|min:1|max:300',
                'retry_attempts' => 'nullable|integer|min:0|max:5',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $tool = Tool::where('id', $toolId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$tool) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool not found'
                ], 404);
            }

            $updateData = [];

            if ($request->filled('name')) {
                // Check if new name conflicts with existing tool
                $existingTool = Tool::where('tenant_id', $tenant->id)
                    ->where('name', $request->input('name'))
                    ->where('id', '!=', $toolId)
                    ->first();

                if ($existingTool) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Tool with this name already exists'
                    ], 422);
                }

                $updateData['name'] = $request->input('name');
            }

            if ($request->filled('description')) {
                $updateData['description'] = $request->input('description');
            }

            if ($request->filled('configuration')) {
                $updateData['configuration'] = $request->input('configuration');
            }

            if ($request->filled('input_schema')) {
                $updateData['input_schema'] = $request->input('input_schema');
            }

            if ($request->filled('output_schema')) {
                $updateData['output_schema'] = $request->input('output_schema');
            }

            if ($request->filled('is_active')) {
                $updateData['is_active'] = $request->input('is_active');
            }

            if ($request->filled('timeout_seconds')) {
                $updateData['timeout_seconds'] = $request->input('timeout_seconds');
            }

            if ($request->filled('retry_attempts')) {
                $updateData['retry_attempts'] = $request->input('retry_attempts');
            }

            $tool->update($updateData);

            $this->loggingService->logHttpRequest('tool_updated', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'tool_id' => $toolId,
                'updated_fields' => array_keys($updateData),
            ]);

            return response()->json([
                'success' => true,
                'data' => $tool->fresh(),
                'message' => 'Tool updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tool_update_error', [
                'tool_id' => $toolId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to update tool',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a tool
     */
    public function destroy(Request $request, string $toolId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $tool = Tool::where('id', $toolId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$tool) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool not found'
                ], 404);
            }

            $toolName = $tool->name;
            $tool->delete();

            $this->loggingService->logHttpRequest('tool_deleted', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'tool_id' => $toolId,
                'tool_name' => $toolName,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Tool deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tool_deletion_error', [
                'tool_id' => $toolId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete tool',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Execute a tool
     */
    public function execute(Request $request, string $toolId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'input_data' => 'required|array',
                'context' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $tool = Tool::where('id', $toolId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$tool) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool not found'
                ], 404);
            }

            if (!$tool->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool is not active'
                ], 422);
            }

            // Execute the tool using ToolService
            $executionResult = $this->toolService->executeTool(
                $tenant->id,
                $toolId,
                $request->input('input_data'),
                $request->input('context', [])
            );

            $this->loggingService->logMCPOperation('tool_executed', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'tool_id' => $toolId,
                'tool_name' => $tool->name,
                'execution_success' => $executionResult['success'] ?? false,
                'execution_time' => $executionResult['execution_time'] ?? 0,
            ]);

            return response()->json([
                'success' => true,
                'data' => $executionResult,
                'message' => 'Tool executed successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logMCPOperation('tool_execution_error', [
                'tool_id' => $toolId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Tool execution failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test a tool configuration
     */
    public function test(Request $request, string $toolId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'test_input' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $tool = Tool::where('id', $toolId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$tool) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool not found'
                ], 404);
            }

            // Test the tool configuration
            $testResult = $this->toolService->testTool(
                $tenant->id,
                $toolId,
                $request->input('test_input', [])
            );

            $this->loggingService->logMCPOperation('tool_tested', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'tool_id' => $toolId,
                'tool_name' => $tool->name,
                'test_success' => $testResult['success'] ?? false,
            ]);

            return response()->json([
                'success' => true,
                'data' => $testResult,
                'message' => 'Tool test completed'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logMCPOperation('tool_test_error', [
                'tool_id' => $toolId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Tool test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tool execution history
     */
    public function getExecutionHistory(Request $request, string $toolId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|integer|min:1|max:100',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();

            $tool = Tool::where('id', $toolId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$tool) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool not found'
                ], 404);
            }

            // Get execution history from ToolService
            $history = $this->toolService->getExecutionHistory(
                $tenant->id,
                $toolId,
                $request->input('limit', 50),
                $request->input('start_date'),
                $request->input('end_date')
            );

            $this->loggingService->logHttpRequest('tool_history_retrieved', [
                'tenant_id' => $tenant->id,
                'tool_id' => $toolId,
                'history_count' => count($history),
            ]);

            return response()->json([
                'success' => true,
                'data' => $history,
                'message' => 'Tool execution history retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tool_history_error', [
                'tool_id' => $toolId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tool execution history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available tool types and templates
     */
    public function getAvailableTypes(Request $request): JsonResponse
    {
        try {
            $toolTypes = [
                'api' => [
                    'name' => 'API Integration',
                    'description' => 'Call external REST APIs',
                    'configuration_schema' => [
                        'url' => 'string|required',
                        'method' => 'string|required|in:GET,POST,PUT,DELETE',
                        'headers' => 'array|optional',
                        'authentication' => 'array|optional',
                    ],
                    'example_configuration' => [
                        'url' => 'https://api.example.com/endpoint',
                        'method' => 'POST',
                        'headers' => ['Content-Type' => 'application/json'],
                        'authentication' => ['type' => 'bearer', 'token' => 'your-token'],
                    ],
                ],
                'webhook' => [
                    'name' => 'Webhook',
                    'description' => 'Send data to webhook endpoints',
                    'configuration_schema' => [
                        'url' => 'string|required',
                        'method' => 'string|required|in:POST,PUT',
                        'headers' => 'array|optional',
                        'secret' => 'string|optional',
                    ],
                    'example_configuration' => [
                        'url' => 'https://hooks.example.com/webhook',
                        'method' => 'POST',
                        'headers' => ['Content-Type' => 'application/json'],
                        'secret' => 'webhook-secret',
                    ],
                ],
                'function' => [
                    'name' => 'Custom Function',
                    'description' => 'Execute custom PHP functions',
                    'configuration_schema' => [
                        'function_name' => 'string|required',
                        'parameters' => 'array|optional',
                    ],
                    'example_configuration' => [
                        'function_name' => 'processData',
                        'parameters' => ['param1', 'param2'],
                    ],
                ],
                'database' => [
                    'name' => 'Database Query',
                    'description' => 'Execute database queries',
                    'configuration_schema' => [
                        'connection' => 'string|required',
                        'query_type' => 'string|required|in:select,insert,update,delete',
                        'table' => 'string|required',
                        'conditions' => 'array|optional',
                    ],
                    'example_configuration' => [
                        'connection' => 'mysql',
                        'query_type' => 'select',
                        'table' => 'users',
                        'conditions' => ['status' => 'active'],
                    ],
                ],
                'file_system' => [
                    'name' => 'File System',
                    'description' => 'Read, write, and manipulate files',
                    'configuration_schema' => [
                        'operation' => 'string|required|in:read,write,delete,list',
                        'path' => 'string|required',
                        'permissions' => 'array|optional',
                    ],
                    'example_configuration' => [
                        'operation' => 'read',
                        'path' => '/storage/documents',
                        'permissions' => ['read', 'write'],
                    ],
                ],
                'web_scraper' => [
                    'name' => 'Web Scraper',
                    'description' => 'Extract data from web pages',
                    'configuration_schema' => [
                        'url' => 'string|required',
                        'selectors' => 'array|required',
                        'wait_time' => 'integer|optional',
                    ],
                    'example_configuration' => [
                        'url' => 'https://example.com',
                        'selectors' => ['title' => 'h1', 'content' => '.content'],
                        'wait_time' => 5,
                    ],
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $toolTypes,
                'message' => 'Available tool types retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tool_types_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve available tool types',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
