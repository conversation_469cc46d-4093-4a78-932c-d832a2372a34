import React from 'react';
import { cn } from '@/utils';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
    label?: string;
    error?: string;
    helperText?: string;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    variant?: 'default' | 'filled' | 'outlined';
    inputSize?: 'sm' | 'md' | 'lg';
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
    ({
        className,
        label,
        error,
        helperText,
        leftIcon,
        rightIcon,
        variant = 'default',
        inputSize = 'md',
        id,
        ...props
    }, ref) => {
        const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

        const sizeClasses = {
            sm: 'h-9 text-sm',
            md: 'h-11 text-sm',
            lg: 'h-12 text-base',
        };

        const paddingClasses = {
            sm: leftIcon ? 'pl-9' : 'pl-3',
            md: leftIcon ? 'pl-10' : 'pl-4',
            lg: leftIcon ? 'pl-12' : 'pl-4',
        };

        const rightPaddingClasses = {
            sm: rightIcon ? 'pr-9' : 'pr-3',
            md: rightIcon ? 'pr-10' : 'pr-4',
            lg: rightIcon ? 'pr-12' : 'pr-4',
        };

        const iconSizes = {
            sm: 'h-4 w-4',
            md: 'h-5 w-5',
            lg: 'h-6 w-6',
        };

        const iconPositions = {
            sm: leftIcon ? 'left-2.5' : '',
            md: leftIcon ? 'left-3' : '',
            lg: leftIcon ? 'left-3' : '',
        };

        const rightIconPositions = {
            sm: rightIcon ? 'right-2.5' : '',
            md: rightIcon ? 'right-3' : '',
            lg: rightIcon ? 'right-3' : '',
        };

        const getVariantClasses = () => {
            const baseClasses = `
                block w-full rounded-lg border transition-all duration-200 ease-in-out
                placeholder:text-gray-400 
                focus:outline-none focus:ring-2 focus:ring-offset-0
                disabled:cursor-not-allowed disabled:opacity-50
            `;

            switch (variant) {
                case 'filled':
                    return cn(
                        baseClasses,
                        'border-transparent bg-gray-50',
                        'hover:bg-gray-100',
                        'focus:bg-white focus:border-blue-500 focus:ring-blue-500/20',
                        error && 'bg-red-50 border-red-200 focus:border-red-500 focus:ring-red-500/20',
                    );
                case 'outlined':
                    return cn(
                        baseClasses,
                        'border-2 border-gray-200 bg-transparent',
                        'hover:border-gray-300',
                        'focus:border-blue-500 focus:ring-blue-500/20',
                        error && 'border-red-300 focus:border-red-500 focus:ring-red-500/20',
                    );
                default:
                    return cn(
                        baseClasses,
                        'border-gray-300 bg-white shadow-sm',
                        'hover:border-gray-400',
                        'focus:border-blue-500 focus:ring-blue-500/20',
                        error && 'border-red-300 focus:border-red-500 focus:ring-red-500/20',
                    );
            }
        };

        return (
            <div className="w-full">
                {label && (
                    <label
                        htmlFor={inputId}
                        className={cn(
                            'block text-sm font-medium mb-2 transition-colors',
                            error ? 'text-red-700' : 'text-gray-700',
                            'hover:text-gray-900'
                        )}
                    >
                        {label}
                        {props.required && (
                            <span className="text-red-500 ml-1" aria-label="required">*</span>
                        )}
                    </label>
                )}

                <div className="relative group">
                    {leftIcon && (
                        <div className={cn(
                            'absolute inset-y-0 flex items-center pointer-events-none z-10',
                            iconPositions[inputSize]
                        )}>
                            <div className={cn(
                                iconSizes[inputSize],
                                'transition-colors duration-200',
                                error ? 'text-red-400' : 'text-gray-400',
                                'group-focus-within:text-blue-500'
                            )}>
                                {leftIcon}
                            </div>
                        </div>
                    )}

                    <input
                        id={inputId}
                        className={cn(
                            getVariantClasses(),
                            sizeClasses[inputSize],
                            paddingClasses[inputSize],
                            rightPaddingClasses[inputSize],
                            className
                        )}
                        ref={ref}
                        aria-invalid={error ? 'true' : 'false'}
                        aria-describedby={
                            error ? `${inputId}-error` :
                                helperText ? `${inputId}-helper` : undefined
                        }
                        {...props}
                    />

                    {rightIcon && (
                        <div className={cn(
                            'absolute inset-y-0 flex items-center z-10',
                            rightIconPositions[inputSize],
                            props.type === 'password' ? 'cursor-pointer' : 'pointer-events-none'
                        )}>
                            <div className={cn(
                                iconSizes[inputSize],
                                'transition-colors duration-200',
                                error ? 'text-red-400' : 'text-gray-400',
                                props.type === 'password' ? 'hover:text-gray-600' : 'group-focus-within:text-blue-500'
                            )}>
                                {rightIcon}
                            </div>
                        </div>
                    )}
                </div>

                {error && (
                    <p
                        id={`${inputId}-error`}
                        className="mt-2 text-sm text-red-600 flex items-center animate-in slide-in-from-top-1 duration-200"
                        role="alert"
                    >
                        <svg className="h-4 w-4 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {error}
                    </p>
                )}

                {helperText && !error && (
                    <p
                        id={`${inputId}-helper`}
                        className="mt-2 text-sm text-gray-500 flex items-center"
                    >
                        <svg className="h-4 w-4 mr-1.5 flex-shrink-0 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        {helperText}
                    </p>
                )}
            </div>
        );
    }
);

Input.displayName = 'Input';

export default Input; 