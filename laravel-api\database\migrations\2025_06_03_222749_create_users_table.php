<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id');

            // Basic Information
            $table->string('name');
            $table->string('email');
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('avatar_url')->nullable();
            $table->string('phone')->nullable();
            $table->string('timezone')->default('UTC');
            $table->string('locale')->default('en');
            $table->string('language')->default('en');
            $table->boolean('is_active')->default(true);
            $table->timestamp('password_changed_at')->nullable();

            // Role & Permissions
            $table->enum('role', ['owner', 'admin', 'manager', 'user', 'viewer'])->default('user');
            $table->json('permissions')->nullable(); // Specific permissions
            $table->json('ai_provider_access')->nullable(); // Which AI providers user can access

            // Authentication & Security
            $table->string('remember_token')->nullable();
            $table->boolean('two_factor_enabled')->default(false);
            $table->text('two_factor_secret')->nullable();
            $table->json('two_factor_recovery_codes')->nullable();
            $table->timestamp('two_factor_confirmed_at')->nullable();

            // API Access
            $table->string('api_key')->nullable()->unique();
            $table->integer('api_rate_limit_per_minute')->nullable();
            $table->boolean('api_access_enabled')->default(false);

            // Usage Tracking
            $table->decimal('tokens_used_current_month', 15, 0)->default(0);
            $table->decimal('tokens_limit_per_month', 15, 0)->nullable();
            $table->integer('chat_sessions_count')->default(0);
            $table->integer('documents_uploaded_count')->default(0);

            // WebSocket & Real-time
            $table->boolean('websocket_enabled')->default(true);
            $table->timestamp('last_websocket_connection')->nullable();
            $table->string('websocket_session_id')->nullable();

            // Activity Tracking
            $table->timestamp('last_login_at')->nullable();
            $table->string('last_login_ip')->nullable();
            $table->string('last_login_user_agent')->nullable();
            $table->timestamp('last_activity_at')->nullable();

            // Preferences
            $table->json('preferences')->nullable(); // UI preferences, notification settings
            $table->json('ai_preferences')->nullable(); // Preferred AI models, settings
            $table->boolean('notifications_enabled')->default(true);
            $table->json('notification_channels')->nullable(); // email, websocket, etc.

            // Status & Metadata
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending_verification'])->default('pending_verification');
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');

            // Indexes
            $table->unique(['tenant_id', 'email']);
            $table->index(['tenant_id', 'role']);
            $table->index(['tenant_id', 'status']);
            $table->index('api_key');
            $table->index('last_activity_at');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
