<?php

namespace App\Services\MCP;

use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;

class GuardrailService extends BaseService
{
    /**
     * Available guardrail types
     */
    protected array $guardrailTypes = [
        'content_safety' => 'validateContentSafety',
        'business_rules' => 'validateBusinessRules',
        'compliance' => 'validateCompliance',
        'data_privacy' => 'validateDataPrivacy',
        'output_quality' => 'validateOutputQuality',
        'rate_limiting' => 'validateRateLimit',
        'content_length' => 'validateContentLength',
        'language_appropriateness' => 'validateLanguageAppropriateness',
    ];

    /**
     * Safety categories and their severity levels
     */
    protected array $safetyCategories = [
        'hate_speech' => ['severity' => 'high', 'action' => 'block'],
        'violence' => ['severity' => 'high', 'action' => 'block'],
        'harassment' => ['severity' => 'medium', 'action' => 'warn'],
        'adult_content' => ['severity' => 'medium', 'action' => 'filter'],
        'spam' => ['severity' => 'low', 'action' => 'flag'],
        'misinformation' => ['severity' => 'high', 'action' => 'block'],
        'personal_info' => ['severity' => 'high', 'action' => 'redact'],
        'profanity' => ['severity' => 'low', 'action' => 'filter'],
    ];

    /**
     * Compliance frameworks
     */
    protected array $complianceFrameworks = [
        'gdpr' => 'validateGDPRCompliance',
        'hipaa' => 'validateHIPAACompliance',
        'pci_dss' => 'validatePCIDSSCompliance',
        'sox' => 'validateSOXCompliance',
        'coppa' => 'validateCOPPACompliance',
    ];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'guardrail_service';
    }

    /**
     * Validate content against all guardrails
     */
    public function validateContent(string $content, array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('validate_content', function () use ($content, $options) {
            $guardrailTypes = $options['guardrail_types'] ?? array_keys($this->guardrailTypes);
            $strictMode = $options['strict_mode'] ?? false;
            $context = $options['context'] ?? [];

            $validationResults = [];
            $overallStatus = 'passed';
            $violations = [];
            $warnings = [];
            $actions = [];

            foreach ($guardrailTypes as $guardrailType) {
                if (!isset($this->guardrailTypes[$guardrailType])) {
                    continue;
                }

                $method = $this->guardrailTypes[$guardrailType];
                $result = $this->$method($content, $context, $strictMode);

                $validationResults[$guardrailType] = $result;

                if ($result['status'] === 'failed') {
                    $overallStatus = 'failed';
                    $violations = array_merge($violations, $result['violations'] ?? []);
                }

                if ($result['status'] === 'warning') {
                    $warnings = array_merge($warnings, $result['warnings'] ?? []);
                    if ($overallStatus === 'passed') {
                        $overallStatus = 'warning';
                    }
                }

                if (!empty($result['actions'])) {
                    $actions = array_merge($actions, $result['actions']);
                }
            }

            // Apply actions if any violations found
            $processedContent = $content;
            if (!empty($actions)) {
                $processedContent = $this->applyGuardrailActions($content, $actions);
            }

            $this->logGuardrailValidation($content, $overallStatus, $violations, $warnings);

            return [
                'original_content' => $content,
                'processed_content' => $processedContent,
                'validation_status' => $overallStatus,
                'guardrails_checked' => $guardrailTypes,
                'validation_results' => $validationResults,
                'violations' => $violations,
                'warnings' => $warnings,
                'actions_applied' => $actions,
                'content_modified' => $content !== $processedContent,
                'validation_timestamp' => now()->toISOString(),
            ];
        }, [
            'content_length' => strlen($content),
            'guardrail_types' => count($options['guardrail_types'] ?? []),
            'strict_mode' => $options['strict_mode'] ?? false,
        ]);
    }

    /**
     * Validate content safety
     */
    protected function validateContentSafety(string $content, array $context, bool $strictMode): array
    {
        $violations = [];
        $warnings = [];
        $actions = [];

        foreach ($this->safetyCategories as $category => $config) {
            $detected = $this->detectSafetyIssue($content, $category);

            if ($detected['found']) {
                $severity = $config['severity'];
                $action = $config['action'];

                $issue = [
                    'category' => $category,
                    'severity' => $severity,
                    'confidence' => $detected['confidence'],
                    'details' => $detected['details'],
                    'action' => $action,
                ];

                if ($severity === 'high' || ($strictMode && $severity === 'medium')) {
                    $violations[] = $issue;
                } else {
                    $warnings[] = $issue;
                }

                $actions[] = [
                    'type' => $action,
                    'category' => $category,
                    'positions' => $detected['positions'] ?? [],
                ];
            }
        }

        $status = !empty($violations) ? 'failed' : (!empty($warnings) ? 'warning' : 'passed');

        return [
            'status' => $status,
            'violations' => $violations,
            'warnings' => $warnings,
            'actions' => $actions,
            'safety_score' => $this->calculateSafetyScore($violations, $warnings),
        ];
    }

    /**
     * Validate business rules
     */
    protected function validateBusinessRules(string $content, array $context, bool $strictMode): array
    {
        $tenantRules = $this->getTenantBusinessRules();
        $violations = [];
        $warnings = [];

        foreach ($tenantRules as $rule) {
            $result = $this->evaluateBusinessRule($content, $rule, $context);

            if (!$result['passed']) {
                $issue = [
                    'rule_id' => $rule['id'],
                    'rule_name' => $rule['name'],
                    'description' => $rule['description'],
                    'severity' => $rule['severity'],
                    'details' => $result['details'],
                ];

                if ($rule['severity'] === 'high' || ($strictMode && $rule['severity'] === 'medium')) {
                    $violations[] = $issue;
                } else {
                    $warnings[] = $issue;
                }
            }
        }

        $status = !empty($violations) ? 'failed' : (!empty($warnings) ? 'warning' : 'passed');

        return [
            'status' => $status,
            'violations' => $violations,
            'warnings' => $warnings,
            'actions' => [],
            'rules_checked' => count($tenantRules),
        ];
    }

    /**
     * Validate compliance
     */
    protected function validateCompliance(string $content, array $context, bool $strictMode): array
    {
        $tenantCompliance = $this->getTenantComplianceRequirements();
        $violations = [];
        $warnings = [];

        foreach ($tenantCompliance as $framework) {
            if (isset($this->complianceFrameworks[$framework])) {
                $method = $this->complianceFrameworks[$framework];
                $result = $this->$method($content, $context);

                if (!$result['compliant']) {
                    $violations = array_merge($violations, $result['violations'] ?? []);
                }

                if (!empty($result['warnings'])) {
                    $warnings = array_merge($warnings, $result['warnings']);
                }
            }
        }

        $status = !empty($violations) ? 'failed' : (!empty($warnings) ? 'warning' : 'passed');

        return [
            'status' => $status,
            'violations' => $violations,
            'warnings' => $warnings,
            'actions' => [],
            'frameworks_checked' => $tenantCompliance,
        ];
    }

    /**
     * Validate data privacy
     */
    protected function validateDataPrivacy(string $content, array $context, bool $strictMode): array
    {
        $violations = [];
        $warnings = [];
        $actions = [];

        // Check for PII
        $piiDetection = $this->detectPII($content);
        if (!empty($piiDetection['found'])) {
            foreach ($piiDetection['found'] as $pii) {
                $violations[] = [
                    'type' => 'pii_detected',
                    'pii_type' => $pii['type'],
                    'confidence' => $pii['confidence'],
                    'position' => $pii['position'],
                ];

                $actions[] = [
                    'type' => 'redact',
                    'target' => $pii['value'],
                    'position' => $pii['position'],
                ];
            }
        }

        // Check for sensitive data patterns
        $sensitiveData = $this->detectSensitiveData($content);
        if (!empty($sensitiveData)) {
            foreach ($sensitiveData as $data) {
                $warnings[] = [
                    'type' => 'sensitive_data',
                    'data_type' => $data['type'],
                    'confidence' => $data['confidence'],
                ];
            }
        }

        $status = !empty($violations) ? 'failed' : (!empty($warnings) ? 'warning' : 'passed');

        return [
            'status' => $status,
            'violations' => $violations,
            'warnings' => $warnings,
            'actions' => $actions,
        ];
    }

    /**
     * Validate output quality
     */
    protected function validateOutputQuality(string $content, array $context, bool $strictMode): array
    {
        $qualityChecks = [
            'coherence' => $this->checkCoherence($content),
            'relevance' => $this->checkRelevance($content, $context),
            'completeness' => $this->checkCompleteness($content, $context),
            'accuracy' => $this->checkAccuracy($content, $context),
        ];

        $violations = [];
        $warnings = [];

        foreach ($qualityChecks as $check => $result) {
            if ($result['score'] < 0.3) {
                $violations[] = [
                    'type' => 'quality_issue',
                    'check' => $check,
                    'score' => $result['score'],
                    'details' => $result['details'],
                ];
            } elseif ($result['score'] < 0.6) {
                $warnings[] = [
                    'type' => 'quality_concern',
                    'check' => $check,
                    'score' => $result['score'],
                    'details' => $result['details'],
                ];
            }
        }

        $overallScore = array_sum(array_column($qualityChecks, 'score')) / count($qualityChecks);
        $status = $overallScore < 0.3 ? 'failed' : ($overallScore < 0.6 ? 'warning' : 'passed');

        return [
            'status' => $status,
            'violations' => $violations,
            'warnings' => $warnings,
            'actions' => [],
            'quality_score' => round($overallScore, 2),
            'quality_checks' => $qualityChecks,
        ];
    }

    /**
     * Validate rate limiting
     */
    protected function validateRateLimit(string $content, array $context, bool $strictMode): array
    {
        $tenantId = $this->getCurrentTenantId();
        $userId = $this->getCurrentUserId();

        $rateLimits = $this->getTenantRateLimits();
        $violations = [];

        foreach ($rateLimits as $limit) {
            $usage = $this->checkRateLimitUsage($tenantId, $userId, $limit);

            if ($usage['exceeded']) {
                $violations[] = [
                    'type' => 'rate_limit_exceeded',
                    'limit_type' => $limit['type'],
                    'limit_value' => $limit['value'],
                    'current_usage' => $usage['current'],
                    'reset_time' => $usage['reset_time'],
                ];
            }
        }

        $status = !empty($violations) ? 'failed' : 'passed';

        return [
            'status' => $status,
            'violations' => $violations,
            'warnings' => [],
            'actions' => [],
        ];
    }

    /**
     * Validate content length
     */
    protected function validateContentLength(string $content, array $context, bool $strictMode): array
    {
        $lengthLimits = $this->getTenantLengthLimits();
        $contentLength = strlen($content);
        $wordCount = str_word_count($content);

        $violations = [];
        $warnings = [];

        if (isset($lengthLimits['max_characters']) && $contentLength > $lengthLimits['max_characters']) {
            $violations[] = [
                'type' => 'content_too_long',
                'metric' => 'characters',
                'current' => $contentLength,
                'limit' => $lengthLimits['max_characters'],
            ];
        }

        if (isset($lengthLimits['max_words']) && $wordCount > $lengthLimits['max_words']) {
            $violations[] = [
                'type' => 'content_too_long',
                'metric' => 'words',
                'current' => $wordCount,
                'limit' => $lengthLimits['max_words'],
            ];
        }

        if (isset($lengthLimits['min_characters']) && $contentLength < $lengthLimits['min_characters']) {
            $warnings[] = [
                'type' => 'content_too_short',
                'metric' => 'characters',
                'current' => $contentLength,
                'minimum' => $lengthLimits['min_characters'],
            ];
        }

        $status = !empty($violations) ? 'failed' : (!empty($warnings) ? 'warning' : 'passed');

        return [
            'status' => $status,
            'violations' => $violations,
            'warnings' => $warnings,
            'actions' => [],
            'content_metrics' => [
                'character_count' => $contentLength,
                'word_count' => $wordCount,
            ],
        ];
    }

    /**
     * Validate language appropriateness
     */
    protected function validateLanguageAppropriateness(string $content, array $context, bool $strictMode): array
    {
        $languageSettings = $this->getTenantLanguageSettings();
        $violations = [];
        $warnings = [];
        $actions = [];

        // Check profanity
        $profanityCheck = $this->detectProfanity($content);
        if (!empty($profanityCheck['found'])) {
            foreach ($profanityCheck['found'] as $profanity) {
                if ($languageSettings['profanity_tolerance'] === 'none') {
                    $violations[] = [
                        'type' => 'profanity_detected',
                        'word' => $profanity['word'],
                        'severity' => $profanity['severity'],
                        'position' => $profanity['position'],
                    ];

                    $actions[] = [
                        'type' => 'filter',
                        'target' => $profanity['word'],
                        'replacement' => str_repeat('*', strlen($profanity['word'])),
                        'position' => $profanity['position'],
                    ];
                } else {
                    $warnings[] = [
                        'type' => 'profanity_detected',
                        'word' => $profanity['word'],
                        'severity' => $profanity['severity'],
                    ];
                }
            }
        }

        // Check language complexity
        $complexityCheck = $this->checkLanguageComplexity($content);
        if ($complexityCheck['level'] !== $languageSettings['target_complexity']) {
            $warnings[] = [
                'type' => 'complexity_mismatch',
                'current_level' => $complexityCheck['level'],
                'target_level' => $languageSettings['target_complexity'],
                'score' => $complexityCheck['score'],
            ];
        }

        $status = !empty($violations) ? 'failed' : (!empty($warnings) ? 'warning' : 'passed');

        return [
            'status' => $status,
            'violations' => $violations,
            'warnings' => $warnings,
            'actions' => $actions,
            'language_analysis' => $complexityCheck,
        ];
    }

    /**
     * Apply guardrail actions to content
     */
    protected function applyGuardrailActions(string $content, array $actions): string
    {
        $processedContent = $content;

        // Sort actions by position (descending) to avoid position shifts
        usort($actions, fn($a, $b) => ($b['position'] ?? 0) <=> ($a['position'] ?? 0));

        foreach ($actions as $action) {
            switch ($action['type']) {
                case 'redact':
                    $processedContent = $this->redactContent($processedContent, $action);
                    break;

                case 'filter':
                    $processedContent = $this->filterContent($processedContent, $action);
                    break;

                case 'block':
                    return '[CONTENT BLOCKED BY GUARDRAILS]';

                case 'warn':
                    $processedContent = '[WARNING: Content may be inappropriate] ' . $processedContent;
                    break;
            }
        }

        return $processedContent;
    }

    /**
     * Helper methods for specific validations
     */
    protected function detectSafetyIssue(string $content, string $category): array
    {
        // Simplified safety detection - in production, this would use ML models
        $patterns = $this->getSafetyPatterns($category);
        $found = false;
        $confidence = 0;
        $details = [];
        $positions = [];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern['regex'], strtolower($content), $matches, PREG_OFFSET_CAPTURE)) {
                $found = true;
                $confidence = max($confidence, $pattern['confidence']);
                $details[] = $pattern['description'];
                $positions[] = $matches[0][1];
            }
        }

        return [
            'found' => $found,
            'confidence' => $confidence,
            'details' => $details,
            'positions' => $positions,
        ];
    }

    protected function getSafetyPatterns(string $category): array
    {
        // Simplified patterns - in production, these would be more sophisticated
        $patterns = [
            'hate_speech' => [
                ['regex' => '/\b(hate|racist|bigot)\b/', 'confidence' => 0.8, 'description' => 'Hate speech indicators'],
            ],
            'violence' => [
                ['regex' => '/\b(kill|murder|violence|attack)\b/', 'confidence' => 0.7, 'description' => 'Violence indicators'],
            ],
            'harassment' => [
                ['regex' => '/\b(harass|bully|threaten)\b/', 'confidence' => 0.6, 'description' => 'Harassment indicators'],
            ],
            'profanity' => [
                ['regex' => '/\b(damn|hell|crap)\b/', 'confidence' => 0.5, 'description' => 'Mild profanity'],
            ],
        ];

        return $patterns[$category] ?? [];
    }

    protected function detectPII(string $content): array
    {
        $piiPatterns = [
            'email' => '/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/',
            'phone' => '/\b\d{3}-\d{3}-\d{4}\b/',
            'ssn' => '/\b\d{3}-\d{2}-\d{4}\b/',
            'credit_card' => '/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/',
        ];

        $found = [];
        foreach ($piiPatterns as $type => $pattern) {
            if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[0] as $match) {
                    $found[] = [
                        'type' => $type,
                        'value' => $match[0],
                        'position' => $match[1],
                        'confidence' => 0.9,
                    ];
                }
            }
        }

        return ['found' => $found];
    }

    protected function detectSensitiveData(string $content): array
    {
        // Simplified sensitive data detection
        $sensitivePatterns = [
            'password' => '/\b(password|pwd|pass)\s*[:=]\s*\S+/i',
            'api_key' => '/\b(api[_-]?key|token)\s*[:=]\s*\S+/i',
        ];

        $found = [];
        foreach ($sensitivePatterns as $type => $pattern) {
            if (preg_match($pattern, $content)) {
                $found[] = [
                    'type' => $type,
                    'confidence' => 0.8,
                ];
            }
        }

        return $found;
    }

    protected function checkCoherence(string $content): array
    {
        // Simplified coherence check
        $sentences = preg_split('/[.!?]+/', $content);
        $score = count($sentences) > 1 ? 0.8 : 0.5;

        return [
            'score' => $score,
            'details' => 'Coherence analysis based on sentence structure',
        ];
    }

    protected function checkRelevance(string $content, array $context): array
    {
        // Simplified relevance check
        $score = !empty($context) ? 0.7 : 0.5;

        return [
            'score' => $score,
            'details' => 'Relevance analysis based on context availability',
        ];
    }

    protected function checkCompleteness(string $content, array $context): array
    {
        // Simplified completeness check
        $score = strlen($content) > 100 ? 0.8 : 0.4;

        return [
            'score' => $score,
            'details' => 'Completeness analysis based on content length',
        ];
    }

    protected function checkAccuracy(string $content, array $context): array
    {
        // Simplified accuracy check
        $score = 0.7; // Default score

        return [
            'score' => $score,
            'details' => 'Accuracy analysis placeholder',
        ];
    }

    protected function detectProfanity(string $content): array
    {
        $profanityWords = ['damn', 'hell', 'crap']; // Simplified list
        $found = [];

        foreach ($profanityWords as $word) {
            if (preg_match_all('/\b' . preg_quote($word, '/') . '\b/i', $content, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[0] as $match) {
                    $found[] = [
                        'word' => $match[0],
                        'position' => $match[1],
                        'severity' => 'mild',
                    ];
                }
            }
        }

        return ['found' => $found];
    }

    protected function checkLanguageComplexity(string $content): array
    {
        $wordCount = str_word_count($content);
        $avgWordLength = $wordCount > 0 ? strlen(str_replace(' ', '', $content)) / $wordCount : 0;

        if ($avgWordLength > 6) {
            $level = 'complex';
            $score = 0.8;
        } elseif ($avgWordLength > 4) {
            $level = 'moderate';
            $score = 0.6;
        } else {
            $level = 'simple';
            $score = 0.4;
        }

        return [
            'level' => $level,
            'score' => $score,
            'avg_word_length' => round($avgWordLength, 2),
            'word_count' => $wordCount,
        ];
    }

    protected function redactContent(string $content, array $action): string
    {
        $target = $action['target'];
        $replacement = '[REDACTED]';

        if (isset($action['position'])) {
            $before = substr($content, 0, $action['position']);
            $after = substr($content, $action['position'] + strlen($target));
            return $before . $replacement . $after;
        }

        return str_replace($target, $replacement, $content);
    }

    protected function filterContent(string $content, array $action): string
    {
        $target = $action['target'];
        $replacement = $action['replacement'] ?? str_repeat('*', strlen($target));

        if (isset($action['position'])) {
            $before = substr($content, 0, $action['position']);
            $after = substr($content, $action['position'] + strlen($target));
            return $before . $replacement . $after;
        }

        return str_replace($target, $replacement, $content);
    }

    protected function calculateSafetyScore(array $violations, array $warnings): float
    {
        $score = 1.0;

        foreach ($violations as $violation) {
            $score -= 0.3; // High penalty for violations
        }

        foreach ($warnings as $warning) {
            $score -= 0.1; // Lower penalty for warnings
        }

        return max(0.0, $score);
    }

    protected function getTenantBusinessRules(): array
    {
        // Mock business rules - in production, these would be stored in database
        return [
            [
                'id' => 'rule_1',
                'name' => 'No competitor mentions',
                'description' => 'Content should not mention competitor names',
                'severity' => 'medium',
                'pattern' => '/\b(competitor|rival)\b/i',
            ],
        ];
    }

    protected function getTenantComplianceRequirements(): array
    {
        // Mock compliance requirements
        return ['gdpr', 'hipaa'];
    }

    protected function getTenantRateLimits(): array
    {
        // Mock rate limits
        return [
            ['type' => 'requests_per_minute', 'value' => 60],
            ['type' => 'tokens_per_hour', 'value' => 10000],
        ];
    }

    protected function getTenantLengthLimits(): array
    {
        // Mock length limits
        return [
            'max_characters' => 10000,
            'max_words' => 2000,
            'min_characters' => 10,
        ];
    }

    protected function getTenantLanguageSettings(): array
    {
        // Mock language settings
        return [
            'profanity_tolerance' => 'low',
            'target_complexity' => 'moderate',
        ];
    }

    protected function evaluateBusinessRule(string $content, array $rule, array $context): array
    {
        $passed = !preg_match($rule['pattern'], $content);

        return [
            'passed' => $passed,
            'details' => $passed ? 'Rule passed' : 'Rule violation detected',
        ];
    }

    protected function validateGDPRCompliance(string $content, array $context): array
    {
        $violations = [];
        $warnings = [];

        // Check for PII without consent
        $piiDetection = $this->detectPII($content);
        if (!empty($piiDetection['found']) && !($context['has_consent'] ?? false)) {
            $violations[] = [
                'type' => 'gdpr_pii_without_consent',
                'description' => 'PII detected without explicit consent',
            ];
        }

        return [
            'compliant' => empty($violations),
            'violations' => $violations,
            'warnings' => $warnings,
        ];
    }

    protected function validateHIPAACompliance(string $content, array $context): array
    {
        $violations = [];
        $warnings = [];

        // Check for health information
        if (preg_match('/\b(medical|health|patient|diagnosis)\b/i', $content)) {
            if (!($context['hipaa_authorized'] ?? false)) {
                $violations[] = [
                    'type' => 'hipaa_phi_unauthorized',
                    'description' => 'Health information detected without authorization',
                ];
            }
        }

        return [
            'compliant' => empty($violations),
            'violations' => $violations,
            'warnings' => $warnings,
        ];
    }

    protected function validatePCIDSSCompliance(string $content, array $context): array
    {
        $violations = [];
        $warnings = [];

        // Check for credit card information
        if (preg_match('/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/', $content)) {
            $violations[] = [
                'type' => 'pci_dss_card_data',
                'description' => 'Credit card data detected',
            ];
        }

        return [
            'compliant' => empty($violations),
            'violations' => $violations,
            'warnings' => $warnings,
        ];
    }

    protected function validateSOXCompliance(string $content, array $context): array
    {
        // Simplified SOX compliance check
        return [
            'compliant' => true,
            'violations' => [],
            'warnings' => [],
        ];
    }

    protected function validateCOPPACompliance(string $content, array $context): array
    {
        // Simplified COPPA compliance check
        return [
            'compliant' => true,
            'violations' => [],
            'warnings' => [],
        ];
    }

    protected function checkRateLimitUsage(string $tenantId, string $userId, array $limit): array
    {
        // Mock rate limit checking - in production, this would check actual usage
        return [
            'exceeded' => false,
            'current' => 10,
            'reset_time' => now()->addMinutes(1)->toISOString(),
        ];
    }

    protected function logGuardrailValidation(string $content, string $status, array $violations, array $warnings): void
    {
        $this->logActivity('Guardrail validation completed', [
            'content_length' => strlen($content),
            'validation_status' => $status,
            'violations_count' => count($violations),
            'warnings_count' => count($warnings),
            'violation_types' => array_unique(array_column($violations, 'type')),
            'warning_types' => array_unique(array_column($warnings, 'type')),
        ], $status === 'failed' ? 'warning' : 'info');
    }
}
