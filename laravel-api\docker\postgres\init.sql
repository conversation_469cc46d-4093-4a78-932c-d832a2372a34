-- Enable pgvector extension for vector operations
CREATE EXTENSION IF NOT EXISTS vector;

-- Create additional extensions that might be useful for the MCP platform
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Set up database configuration for optimal vector operations
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- Create a function to calculate vector similarity
CREATE OR REPLACE FUNCTION cosine_similarity(a vector, b vector)
R<PERSON><PERSON><PERSON> float AS $$
BEGIN
    RETURN 1 - (a <=> b);
END;
$$ LANGUAGE plpgsql IMMUTABLE STRICT;

-- Create a function to calculate euclidean distance
CREATE OR REPLACE FUNCTION euclidean_distance(a vector, b vector)
RETURNS float AS $$
BEGIN
    RETURN a <-> b;
END;
$$ LANGUAGE plpgsql IMMUTABLE STRICT;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE axient_mcp TO postgres;

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'Axient MCP++ Database initialized successfully with pgvector extension';
END $$;
