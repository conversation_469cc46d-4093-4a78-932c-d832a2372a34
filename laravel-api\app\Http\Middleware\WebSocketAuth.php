<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;
use App\Services\Core\TenantContextService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;

class WebSocketAuth
{
    protected TenantContextService $tenantContextService;
    protected LoggingService $loggingService;
    protected ErrorHandlingService $errorHandlingService;

    public function __construct(
        TenantContextService $tenantContextService,
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService
    ) {
        $this->tenantContextService = $tenantContextService;
        $this->loggingService = $loggingService;
        $this->errorHandlingService = $errorHandlingService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        try {
            // Extract token from various sources
            $token = $this->extractToken($request);

            if (!$token) {
                return $this->unauthorizedResponse('Missing authentication token');
            }

            // Validate the token
            $accessToken = $this->validateToken($token);

            if (!$accessToken) {
                return $this->unauthorizedResponse('Invalid authentication token');
            }

            // Get the user
            $user = $accessToken->tokenable;

            if (!$user) {
                return $this->unauthorizedResponse('User not found');
            }

            // Check if user account is active
            if (!$this->isUserAccountActive($user)) {
                return $this->unauthorizedResponse('User account is not active');
            }

            // Set the authenticated user
            Auth::setUser($user);

            // Resolve tenant context
            $this->resolveTenantContext($request, $user);

            // Check WebSocket-specific permissions
            if (!$this->hasWebSocketPermission($user)) {
                return $this->forbiddenResponse('WebSocket access not permitted');
            }

            // Check connection limits
            if (!$this->checkConnectionLimits($user)) {
                return $this->forbiddenResponse('Connection limit exceeded');
            }

            // Log successful authentication
            $this->logWebSocketAuth($request, $user, 'success');

            // Add user and tenant info to request
            $request->merge([
                'websocket_user' => $user,
                'websocket_token' => $accessToken,
                'websocket_tenant_id' => $this->tenantContextService->getCurrentTenantId(),
            ]);

            return $next($request);

        } catch (\Exception $e) {
            $this->errorHandlingService->handleException($e, [
                'context' => 'websocket_authentication',
                'request_data' => $this->sanitizeRequestData($request),
            ]);

            return $this->unauthorizedResponse('Authentication failed');
        }
    }

    /**
     * Extract token from request
     */
    protected function extractToken(Request $request): ?string
    {
        // Try Authorization header first
        $authHeader = $request->header('Authorization');
        if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
            return substr($authHeader, 7);
        }

        // Try query parameter
        $queryToken = $request->query('token');
        if ($queryToken) {
            return $queryToken;
        }

        // Try WebSocket-specific headers
        $wsToken = $request->header('Sec-WebSocket-Protocol');
        if ($wsToken && str_contains($wsToken, 'token.')) {
            $parts = explode('.', $wsToken);
            if (count($parts) >= 2) {
                return $parts[1];
            }
        }

        // Try custom WebSocket token header
        $customToken = $request->header('X-WebSocket-Token');
        if ($customToken) {
            return $customToken;
        }

        return null;
    }

    /**
     * Validate the token
     */
    protected function validateToken(string $token): ?PersonalAccessToken
    {
        try {
            // Find the token in the database
            $accessToken = PersonalAccessToken::findToken($token);

            if (!$accessToken) {
                return null;
            }

            // Check if token is expired
            if ($accessToken->expires_at && $accessToken->expires_at->isPast()) {
                $this->loggingService->logWebSocketEvent('websocket_auth_expired_token', [
                    'token_id' => $accessToken->id,
                    'expires_at' => $accessToken->expires_at,
                ]);
                return null;
            }

            // Check if token has WebSocket abilities
            if (!$this->tokenHasWebSocketAbilities($accessToken)) {
                $this->loggingService->logWebSocketEvent('websocket_auth_insufficient_abilities', [
                    'token_id' => $accessToken->id,
                    'abilities' => $accessToken->abilities,
                ]);
                return null;
            }

            // Update last used timestamp
            $accessToken->forceFill(['last_used_at' => now()])->save();

            return $accessToken;

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_auth_token_validation_error', [
                'error' => $e->getMessage(),
            ], 'error');
            return null;
        }
    }

    /**
     * Check if token has WebSocket abilities
     */
    protected function tokenHasWebSocketAbilities(PersonalAccessToken $token): bool
    {
        $requiredAbilities = ['websocket:connect', 'websocket:send', 'websocket:receive'];

        // If token has '*' ability, it can do everything
        if (in_array('*', $token->abilities)) {
            return true;
        }

        // Check for specific WebSocket abilities
        foreach ($requiredAbilities as $ability) {
            if (!in_array($ability, $token->abilities)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if user account is active
     */
    protected function isUserAccountActive($user): bool
    {
        // Check if user is active
        if (isset($user->is_active) && !$user->is_active) {
            return false;
        }

        // Check if user account is locked
        if (isset($user->account_locked_until) && $user->account_locked_until && $user->account_locked_until->isFuture()) {
            return false;
        }

        // Check if user is verified (if email verification is required)
        if (isset($user->email_verified_at) && !$user->email_verified_at) {
            $requireEmailVerification = config('auth.verification.required', false);
            if ($requireEmailVerification) {
                return false;
            }
        }

        return true;
    }

    /**
     * Resolve tenant context
     */
    protected function resolveTenantContext(Request $request, $user): void
    {
        // Try to resolve tenant from various sources
        $tenantId = $request->header('X-Tenant-ID')
                 ?? $request->query('tenant_id')
                 ?? $user->tenant_id;

        if ($tenantId) {
            $this->tenantContextService->setCurrentTenant($tenantId);
        }

        // Validate that user belongs to the tenant
        if (!$this->tenantContextService->validateUserTenantAccess($user)) {
            throw new \Exception('User does not have access to the specified tenant');
        }
    }

    /**
     * Check if user has WebSocket permission
     */
    protected function hasWebSocketPermission($user): bool
    {
        // Check user role permissions
        if (method_exists($user, 'hasPermission')) {
            if (!$user->hasPermission('websocket.connect')) {
                return false;
            }
        }

        // Check tenant-specific WebSocket settings
        $tenant = $this->tenantContextService->getCurrentTenant();
        if ($tenant) {
            $settings = $tenant->settings ?? [];

            // Check if WebSocket is enabled for tenant
            if (isset($settings['websocket_enabled']) && !$settings['websocket_enabled']) {
                return false;
            }

            // Check if user's role is allowed WebSocket access
            $allowedRoles = $settings['websocket_allowed_roles'] ?? ['owner', 'admin', 'manager', 'user'];
            if (!in_array($user->role, $allowedRoles)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check connection limits
     */
    protected function checkConnectionLimits($user): bool
    {
        $tenantId = $this->tenantContextService->getCurrentTenantId();
        $userId = $user->id;

        // Check tenant connection limit
        $tenantLimit = config('broadcasting.websocket.max_connections_per_tenant', 1000);
        $tenantConnections = $this->getActiveConnectionsCount('tenant', $tenantId);

        if ($tenantConnections >= $tenantLimit) {
            $this->loggingService->logWebSocketEvent('websocket_auth_tenant_limit_exceeded', [
                'tenant_id' => $tenantId,
                'current_connections' => $tenantConnections,
                'limit' => $tenantLimit,
            ]);
            return false;
        }

        // Check user connection limit
        $userLimit = config('broadcasting.websocket.max_connections_per_user', 10);
        $userConnections = $this->getActiveConnectionsCount('user', $userId);

        if ($userConnections >= $userLimit) {
            $this->loggingService->logWebSocketEvent('websocket_auth_user_limit_exceeded', [
                'user_id' => $userId,
                'current_connections' => $userConnections,
                'limit' => $userLimit,
            ]);
            return false;
        }

        return true;
    }

    /**
     * Get active connections count
     */
    protected function getActiveConnectionsCount(string $type, string $id): int
    {
        try {
            // This would integrate with the WebSocket service to get real connection counts
            // For now, return a placeholder count
            return 0;
        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_auth_connection_count_error', [
                'type' => $type,
                'id' => $id,
                'error' => $e->getMessage(),
            ], 'error');
            return 0;
        }
    }

    /**
     * Log WebSocket authentication attempt
     */
    protected function logWebSocketAuth(Request $request, $user, string $status): void
    {
        $this->loggingService->logWebSocketEvent('websocket_authentication', [
            'user_id' => $user->id,
            'tenant_id' => $this->tenantContextService->getCurrentTenantId(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'status' => $status,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Sanitize request data for logging
     */
    protected function sanitizeRequestData(Request $request): array
    {
        $data = [
            'method' => $request->method(),
            'url' => $request->url(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'headers' => [],
        ];

        // Include safe headers only
        $safeHeaders = ['content-type', 'accept', 'origin', 'sec-websocket-version', 'sec-websocket-key'];
        foreach ($safeHeaders as $header) {
            if ($request->hasHeader($header)) {
                $data['headers'][$header] = $request->header($header);
            }
        }

        return $data;
    }

    /**
     * Return unauthorized response
     */
    protected function unauthorizedResponse(string $message): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'error' => 'Unauthorized',
            'message' => $message,
            'code' => 401,
        ], 401);
    }

    /**
     * Return forbidden response
     */
    protected function forbiddenResponse(string $message): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'error' => 'Forbidden',
            'message' => $message,
            'code' => 403,
        ], 403);
    }
}
