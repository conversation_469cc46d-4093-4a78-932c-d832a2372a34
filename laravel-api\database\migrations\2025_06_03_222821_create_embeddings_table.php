<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('embeddings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id');
            $table->uuid('document_id')->nullable();
            $table->uuid('created_by_user_id');

            // Content Information
            $table->text('content'); // Original text content
            $table->string('content_hash', 64); // SHA-256 hash for deduplication (64 chars)
            $table->integer('content_length');
            $table->string('content_type')->default('text'); // text, code, markdown, etc.
            $table->string('language')->nullable(); // en, es, fr, etc.

            // Embedding Information
            $table->string('embedding_model'); // text-embedding-ada-002, etc.
            $table->string('embedding_provider'); // openai, huggingface, etc.
            $table->integer('embedding_dimension')->default(1536);

            // Chunk Information (for large documents)
            $table->integer('chunk_index')->default(0);
            $table->integer('total_chunks')->default(1);
            $table->integer('chunk_overlap')->default(0);
            $table->text('chunk_metadata')->nullable(); // JSON metadata about chunking

            // Document Context
            $table->string('source_type')->nullable(); // file, url, manual, chat
            $table->string('source_path')->nullable(); // File path or URL
            $table->string('document_title')->nullable();
            $table->json('document_metadata')->nullable();
            $table->integer('page_number')->nullable(); // For PDFs
            $table->integer('section_number')->nullable();

            // Processing Information
            $table->timestamp('processed_at');
            $table->decimal('processing_time_ms', 10, 2)->nullable();
            $table->decimal('embedding_cost', 10, 6)->nullable();
            $table->integer('tokens_used')->nullable();

            // Quality & Validation
            $table->decimal('quality_score', 5, 4)->nullable(); // 0-1 quality assessment
            $table->boolean('is_validated')->default(false);
            $table->timestamp('validated_at')->nullable();
            $table->uuid('validated_by_user_id')->nullable();

            // Usage Tracking
            $table->integer('retrieval_count')->default(0);
            $table->timestamp('last_retrieved_at')->nullable();
            $table->decimal('average_similarity_score', 5, 4)->nullable();
            $table->json('usage_statistics')->nullable();

            // Categorization & Tags
            $table->json('tags')->nullable(); // User-defined tags
            $table->json('auto_tags')->nullable(); // AI-generated tags
            $table->string('category')->nullable(); // documentation, code, faq, etc.
            $table->integer('importance_score')->nullable(); // 1-10

            // Access Control
            $table->enum('visibility', ['public', 'private', 'team', 'restricted'])->default('private');
            $table->json('access_permissions')->nullable();
            $table->boolean('is_searchable')->default(true);
            $table->boolean('is_active')->default(true);

            // Relationships
            $table->uuid('parent_embedding_id')->nullable(); // For hierarchical content
            $table->json('related_embeddings')->nullable(); // Similar content IDs
            $table->json('referenced_by')->nullable(); // Chat messages that used this

            // Status & Metadata
            $table->enum('status', ['processing', 'completed', 'failed', 'archived'])->default('processing');
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('document_id')->references('id')->on('documents')->onDelete('cascade');
            $table->foreign('created_by_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('validated_by_user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parent_embedding_id')->references('id')->on('embeddings')->onDelete('set null');

            // Indexes
            $table->unique(['tenant_id', 'content_hash']); // Prevent duplicates
            $table->index(['tenant_id', 'document_id']);
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'is_active', 'is_searchable']);
            $table->index(['embedding_model', 'embedding_provider']);
            $table->index(['source_type', 'category']);
            $table->index('last_retrieved_at');
            $table->index('created_at');
        });

        // TODO: Add the vector column using raw SQL (pgvector extension) when PostgreSQL is configured
        // DB::statement('ALTER TABLE embeddings ADD COLUMN embedding vector(1536)');

        // TODO: Create vector similarity indexes for performance when PostgreSQL is configured
        // DB::statement('CREATE INDEX embeddings_embedding_cosine_idx ON embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100)');
        // DB::statement('CREATE INDEX embeddings_embedding_l2_idx ON embeddings USING ivfflat (embedding vector_l2_ops) WITH (lists = 100)');
        // DB::statement('CREATE INDEX embeddings_embedding_ip_idx ON embeddings USING ivfflat (embedding vector_ip_ops) WITH (lists = 100)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('embeddings');
    }
};
