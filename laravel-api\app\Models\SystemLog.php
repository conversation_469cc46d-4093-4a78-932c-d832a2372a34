<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * SystemLog Model
 *
 * Comprehensive logging model for tracking all system activities,
 * performance metrics, security events, and audit trails.
 *
 * @property string $id
 * @property string|null $tenant_id
 * @property string|null $user_id
 * @property string $log_type
 * @property string $log_level
 * @property string $log_channel
 * @property string|null $method
 * @property string|null $url
 * @property string|null $route_name
 * @property string|null $controller_action
 * @property int|null $status_code
 * @property float|null $response_time_ms
 * @property string $message
 * @property array|null $context
 * @property array|null $request_data
 * @property array|null $response_data
 * @property array|null $headers
 * @property array|null $metadata
 * @property string|null $session_id
 * @property string|null $websocket_session_id
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property string|null $referer
 * @property string|null $ai_provider_id
 * @property string|null $ai_model
 * @property int|null $tokens_used
 * @property float|null $estimated_cost
 * @property array|null $ai_parameters
 * @property string|null $mcp_agent_type
 * @property string|null $mcp_agent_name
 * @property array|null $mcp_input
 * @property array|null $mcp_output
 * @property float|null $mcp_processing_time_ms
 * @property string|null $tool_id
 * @property string|null $tool_name
 * @property array|null $tool_parameters
 * @property array|null $tool_result
 * @property bool|null $tool_success
 * @property string|null $workflow_id
 * @property string|null $workflow_step
 * @property int|null $workflow_step_number
 * @property array|null $workflow_context
 * @property string|null $error_code
 * @property string|null $error_message
 * @property string|null $stack_trace
 * @property string|null $error_file
 * @property int|null $error_line
 * @property array|null $error_context
 * @property string|null $security_event_type
 * @property bool $is_security_incident
 * @property int|null $risk_score
 * @property float|null $memory_usage_mb
 * @property float|null $cpu_usage_percent
 * @property int|null $database_queries_count
 * @property float|null $database_query_time_ms
 * @property int|null $cache_hits
 * @property int|null $cache_misses
 * @property string|null $health_status
 * @property array|null $health_metrics
 * @property string|null $service_name
 * @property string|null $service_version
 * @property string|null $correlation_id
 * @property string|null $trace_id
 * @property string|null $span_id
 * @property string|null $parent_log_id
 * @property bool $contains_pii
 * @property bool $is_audit_log
 * @property array|null $compliance_tags
 * @property Carbon|null $retention_expires_at
 * @property bool $is_processed
 * @property Carbon|null $processed_at
 * @property array|null $analysis_results
 * @property bool $requires_attention
 * @property bool $is_archived
 * @property string $environment
 * @property string|null $server_name
 * @property string|null $application_version
 * @property array|null $tags
 * @property Carbon $logged_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Tenant|null $tenant
 * @property-read User|null $user
 * @property-read AIProvider|null $aiProvider
 * @property-read Tool|null $tool
 * @property-read Workflow|null $workflow
 * @property-read SystemLog|null $parentLog
 */
class SystemLog extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'system_logs';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model's ID is auto-incrementing.
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'tenant_id',
        'user_id',
        'log_type',
        'log_level',
        'log_channel',
        'method',
        'url',
        'route_name',
        'controller_action',
        'status_code',
        'response_time_ms',
        'message',
        'context',
        'request_data',
        'response_data',
        'headers',
        'metadata',
        'session_id',
        'websocket_session_id',
        'ip_address',
        'user_agent',
        'referer',
        'ai_provider_id',
        'ai_model',
        'tokens_used',
        'estimated_cost',
        'ai_parameters',
        'mcp_agent_type',
        'mcp_agent_name',
        'mcp_input',
        'mcp_output',
        'mcp_processing_time_ms',
        'tool_id',
        'tool_name',
        'tool_parameters',
        'tool_result',
        'tool_success',
        'workflow_id',
        'workflow_step',
        'workflow_step_number',
        'workflow_context',
        'error_code',
        'error_message',
        'stack_trace',
        'error_file',
        'error_line',
        'error_context',
        'security_event_type',
        'is_security_incident',
        'risk_score',
        'memory_usage_mb',
        'cpu_usage_percent',
        'database_queries_count',
        'database_query_time_ms',
        'cache_hits',
        'cache_misses',
        'health_status',
        'health_metrics',
        'service_name',
        'service_version',
        'correlation_id',
        'trace_id',
        'span_id',
        'parent_log_id',
        'contains_pii',
        'is_audit_log',
        'compliance_tags',
        'retention_expires_at',
        'is_processed',
        'processed_at',
        'analysis_results',
        'requires_attention',
        'is_archived',
        'environment',
        'server_name',
        'application_version',
        'tags',
        'logged_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'stack_trace',
        'error_context',
        'request_data',
        'response_data',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'context' => 'array',
        'request_data' => 'array',
        'response_data' => 'array',
        'headers' => 'array',
        'metadata' => 'array',
        'ai_parameters' => 'array',
        'mcp_input' => 'array',
        'mcp_output' => 'array',
        'tool_parameters' => 'array',
        'tool_result' => 'array',
        'tool_success' => 'boolean',
        'workflow_context' => 'array',
        'error_context' => 'array',
        'is_security_incident' => 'boolean',
        'health_metrics' => 'array',
        'compliance_tags' => 'array',
        'retention_expires_at' => 'datetime',
        'is_processed' => 'boolean',
        'processed_at' => 'datetime',
        'analysis_results' => 'array',
        'requires_attention' => 'boolean',
        'is_archived' => 'boolean',
        'contains_pii' => 'boolean',
        'is_audit_log' => 'boolean',
        'tags' => 'array',
        'logged_at' => 'datetime',
        'response_time_ms' => 'decimal:2',
        'estimated_cost' => 'decimal:6',
        'mcp_processing_time_ms' => 'decimal:2',
        'memory_usage_mb' => 'decimal:2',
        'cpu_usage_percent' => 'decimal:2',
        'database_query_time_ms' => 'decimal:2',
        'tokens_used' => 'integer',
        'workflow_step_number' => 'integer',
        'error_line' => 'integer',
        'risk_score' => 'integer',
        'database_queries_count' => 'integer',
        'cache_hits' => 'integer',
        'cache_misses' => 'integer',
        'status_code' => 'integer',
    ];

    /**
     * The attributes that should be mutated to dates.
     */
    protected $dates = [
        'logged_at',
        'retention_expires_at',
        'processed_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Log type constants
     */
    public const LOG_TYPES = [
        'http_request',
        'websocket_event',
        'ai_provider_request',
        'ai_provider_response',
        'mcp_agent',
        'tool_execution',
        'workflow_execution',
        'error',
        'security',
        'system_health',
        'performance',
        'audit',
        'debug',
    ];

    /**
     * Log level constants
     */
    public const LOG_LEVELS = [
        'emergency',
        'alert',
        'critical',
        'error',
        'warning',
        'notice',
        'info',
        'debug',
    ];

    /**
     * Security event type constants
     */
    public const SECURITY_EVENT_TYPES = [
        'login_attempt',
        'login_success',
        'login_failure',
        'logout',
        'permission_denied',
        'rate_limit_exceeded',
        'suspicious_activity',
        'data_breach_attempt',
        'unauthorized_access',
    ];

    /**
     * Health status constants
     */
    public const HEALTH_STATUSES = [
        'healthy',
        'warning',
        'critical',
        'down',
    ];

    /**
     * Get the tenant that owns the log.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user that owns the log.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the AI provider associated with the log.
     * Note: AIProvider model needs to be created
     */
    public function aiProvider(): BelongsTo
    {
        // TODO: Create AIProvider model
        return $this->belongsTo('App\Models\AIProvider');
    }

    /**
     * Get the tool associated with the log.
     * Note: Tool model needs to be created
     */
    public function tool(): BelongsTo
    {
        // TODO: Create Tool model
        return $this->belongsTo('App\Models\Tool');
    }

    /**
     * Get the workflow associated with the log.
     * Note: Workflow model needs to be created
     */
    public function workflow(): BelongsTo
    {
        // TODO: Create Workflow model
        return $this->belongsTo('App\Models\Workflow');
    }

    /**
     * Get the parent log.
     */
    public function parentLog(): BelongsTo
    {
        return $this->belongsTo(SystemLog::class, 'parent_log_id');
    }

    /**
     * Get child logs.
     */
    public function childLogs()
    {
        return $this->hasMany(SystemLog::class, 'parent_log_id');
    }

    /**
     * Scope a query to only include logs of a given type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('log_type', $type);
    }

    /**
     * Scope a query to only include logs of a given level.
     */
    public function scopeOfLevel($query, string $level)
    {
        return $query->where('log_level', $level);
    }

    /**
     * Scope a query to only include security incidents.
     */
    public function scopeSecurityIncidents($query)
    {
        return $query->where('is_security_incident', true);
    }

    /**
     * Scope a query to only include error logs.
     */
    public function scopeErrors($query)
    {
        return $query->whereIn('log_level', ['error', 'critical', 'emergency']);
    }

    /**
     * Scope a query to only include logs requiring attention.
     */
    public function scopeRequiringAttention($query)
    {
        return $query->where('requires_attention', true);
    }

    /**
     * Scope a query to only include unprocessed logs.
     */
    public function scopeUnprocessed($query)
    {
        return $query->where('is_processed', false);
    }

    /**
     * Scope a query to filter by tenant.
     */
    public function scopeForTenant($query, string $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Scope a query to filter by user.
     */
    public function scopeForUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeBetweenDates($query, Carbon $from, Carbon $to)
    {
        return $query->whereBetween('logged_at', [$from, $to]);
    }

    /**
     * Scope a query to filter by correlation ID.
     */
    public function scopeByCorrelationId($query, string $correlationId)
    {
        return $query->where('correlation_id', $correlationId);
    }

    /**
     * Scope a query to filter by trace ID.
     */
    public function scopeByTraceId($query, string $traceId)
    {
        return $query->where('trace_id', $traceId);
    }

    /**
     * Check if the log is an error.
     */
    public function isError(): bool
    {
        return in_array($this->log_level, ['error', 'critical', 'emergency']);
    }

    /**
     * Check if the log is a security incident.
     */
    public function isSecurityIncident(): bool
    {
        return $this->is_security_incident;
    }

    /**
     * Check if the log requires attention.
     */
    public function requiresAttention(): bool
    {
        return $this->requires_attention;
    }

    /**
     * Check if the log is processed.
     */
    public function isProcessed(): bool
    {
        return $this->is_processed;
    }

    /**
     * Mark the log as processed.
     */
    public function markAsProcessed(): bool
    {
        return $this->update([
            'is_processed' => true,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark the log as requiring attention.
     */
    public function markAsRequiringAttention(): bool
    {
        return $this->update(['requires_attention' => true]);
    }

    /**
     * Archive the log.
     */
    public function archive(): bool
    {
        return $this->update(['is_archived' => true]);
    }

    /**
     * Get formatted log level for display.
     */
    public function getFormattedLogLevelAttribute(): string
    {
        return ucfirst($this->log_level);
    }

    /**
     * Get formatted log type for display.
     */
    public function getFormattedLogTypeAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->log_type));
    }

    /**
     * Get the log priority based on level.
     */
    public function getPriorityAttribute(): int
    {
        $priorities = [
            'emergency' => 0,
            'alert' => 1,
            'critical' => 2,
            'error' => 3,
            'warning' => 4,
            'notice' => 5,
            'info' => 6,
            'debug' => 7,
        ];

        return $priorities[$this->log_level] ?? 7;
    }

    /**
     * Get a summary of the log for display.
     */
    public function getSummaryAttribute(): string
    {
        $summary = $this->message;

        if ($this->error_message) {
            $summary .= ' - ' . $this->error_message;
        }

        if ($this->tool_name) {
            $summary .= ' (Tool: ' . $this->tool_name . ')';
        }

        if ($this->ai_model) {
            $summary .= ' (AI: ' . $this->ai_model . ')';
        }

        return $summary;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($log) {
            if (empty($log->logged_at)) {
                $log->logged_at = now();
            }

            if (empty($log->environment)) {
                $log->environment = app()->environment();
            }

            if (empty($log->log_channel)) {
                $log->log_channel = 'default';
            }
        });
    }
}
