<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workflows', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id');
            $table->uuid('created_by_user_id');

            // Workflow Information
            $table->string('name');
            $table->string('display_name');
            $table->text('description');
            $table->string('version')->default('1.0.0');
            $table->string('category'); // automation, data_processing, integration, etc.
            $table->json('tags')->nullable();

            // Workflow Definition
            $table->json('workflow_definition'); // Complete workflow structure
            $table->json('steps'); // Array of workflow steps
            $table->json('connections'); // Step connections and flow
            $table->json('conditions'); // Conditional logic
            $table->json('variables')->nullable(); // Workflow variables
            $table->json('constants')->nullable(); // Workflow constants

            // Trigger Configuration
            $table->enum('trigger_type', [
                'manual', 'scheduled', 'webhook', 'event', 'api_call', 'file_upload', 'chat_message'
            ])->default('manual');
            $table->json('trigger_config')->nullable(); // Trigger-specific configuration
            $table->string('cron_schedule')->nullable(); // For scheduled workflows
            $table->boolean('is_enabled')->default(true);

            // Execution Settings
            $table->integer('max_execution_time_minutes')->default(60);
            $table->integer('max_concurrent_executions')->default(1);
            $table->boolean('allow_parallel_execution')->default(false);
            $table->enum('failure_handling', ['stop', 'continue', 'retry', 'rollback'])->default('stop');
            $table->integer('max_retry_attempts')->default(3);
            $table->integer('retry_delay_seconds')->default(60);

            // Access Control
            $table->enum('visibility', ['public', 'private', 'team'])->default('private');
            $table->json('allowed_user_roles')->nullable();
            $table->json('execution_permissions')->nullable();
            $table->boolean('requires_approval')->default(false);
            $table->json('approval_rules')->nullable();

            // Usage Tracking
            $table->integer('execution_count')->default(0);
            $table->integer('success_count')->default(0);
            $table->integer('error_count')->default(0);
            $table->decimal('success_rate_percentage', 5, 2)->default(100);
            $table->timestamp('last_executed_at')->nullable();
            $table->uuid('last_executed_by_user_id')->nullable();

            // Performance Metrics
            $table->decimal('average_execution_time_minutes', 10, 2)->nullable();
            $table->decimal('min_execution_time_minutes', 10, 2)->nullable();
            $table->decimal('max_recorded_execution_time_minutes', 10, 2)->nullable();
            $table->json('performance_history')->nullable(); // Last 100 executions
            $table->json('step_performance')->nullable(); // Per-step performance

            // Error Handling & Monitoring
            $table->text('last_error_message')->nullable();
            $table->timestamp('last_error_at')->nullable();
            $table->json('common_errors')->nullable();
            $table->json('error_patterns')->nullable();
            $table->boolean('monitoring_enabled')->default(true);
            $table->json('alert_thresholds')->nullable();

            // Dependencies & Requirements
            $table->json('required_tools')->nullable(); // Tools this workflow uses
            $table->json('required_ai_providers')->nullable();
            $table->json('required_permissions')->nullable();
            $table->json('environment_requirements')->nullable();

            // Input/Output Schema
            $table->json('input_schema')->nullable(); // Expected input format
            $table->json('output_schema')->nullable(); // Expected output format
            $table->json('default_inputs')->nullable();
            $table->json('sample_inputs')->nullable();
            $table->json('sample_outputs')->nullable();

            // Workflow Context & State
            $table->json('context_variables')->nullable(); // Persistent context
            $table->json('state_management')->nullable(); // State handling config
            $table->boolean('maintains_state')->default(false);
            $table->integer('state_retention_days')->nullable();

            // Integration & Webhooks
            $table->string('webhook_url')->nullable();
            $table->string('webhook_secret')->nullable();
            $table->json('webhook_events')->nullable(); // start, complete, error, etc.
            $table->json('external_integrations')->nullable();

            // Scheduling & Automation
            $table->boolean('is_scheduled')->default(false);
            $table->timestamp('next_scheduled_run')->nullable();
            $table->timestamp('last_scheduled_run')->nullable();
            $table->json('schedule_config')->nullable();
            $table->boolean('auto_retry_failed')->default(false);

            // Documentation & Help
            $table->longText('documentation')->nullable(); // Markdown documentation
            $table->json('step_documentation')->nullable(); // Per-step docs
            $table->json('examples')->nullable();
            $table->string('documentation_url')->nullable();
            $table->json('changelog')->nullable();

            // Versioning & Templates
            $table->uuid('parent_workflow_id')->nullable(); // For workflow versions
            $table->boolean('is_latest_version')->default(true);
            $table->text('version_notes')->nullable();
            $table->boolean('is_template')->default(false);
            $table->json('template_config')->nullable();

            // Collaboration & Sharing
            $table->json('collaborators')->nullable(); // Users who can edit
            $table->json('sharing_settings')->nullable();
            $table->boolean('is_public_template')->default(false);
            $table->integer('template_usage_count')->default(0);
            $table->decimal('template_rating', 3, 2)->nullable();

            // Compliance & Audit
            $table->boolean('handles_sensitive_data')->default(false);
            $table->json('compliance_requirements')->nullable();
            $table->boolean('audit_logging_enabled')->default(true);
            $table->json('data_retention_policy')->nullable();

            // Testing & Validation
            $table->json('test_cases')->nullable();
            $table->timestamp('last_tested_at')->nullable();
            $table->boolean('test_passed')->nullable();
            $table->json('test_results')->nullable();
            $table->json('validation_rules')->nullable();

            // Status & Metadata
            $table->enum('status', ['draft', 'active', 'inactive', 'deprecated', 'maintenance'])->default('draft');
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('created_by_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('last_executed_by_user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parent_workflow_id')->references('id')->on('workflows')->onDelete('set null');

            // Indexes
            $table->unique(['tenant_id', 'name']);
            $table->index(['tenant_id', 'category']);
            $table->index(['tenant_id', 'trigger_type']);
            $table->index(['tenant_id', 'is_enabled']);
            $table->index(['tenant_id', 'status']);
            $table->index(['is_scheduled', 'next_scheduled_run']);
            $table->index(['visibility', 'is_public_template']);
            $table->index('last_executed_at');
            $table->index(['success_rate_percentage', 'execution_count']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workflows');
    }
};
