<?php

namespace App\Models;

use App\Enums\SubscriptionStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Tenant extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'domain',
        'subdomain',
        'email',
        'phone',
        'address',
        'is_active',
        'subscription_plan',
        'subscription_status',
        'trial_ends_at',
        'timezone',
        'language',
        'feature_flags',
        'limits',
        'settings',
        'webhook_secret',
        'subscription_started_at',
        'subscription_expires_at',
        'monthly_limit_tokens',
        'used_tokens_current_month',
        'enabled_features',
        'max_users',
        'max_ai_providers',
        'max_documents',
        'max_workflows',
        'database_name',
        'database_host',
        'database_port',
        'database_config',
        'security_settings',
        'data_retention_enabled',
        'data_retention_days',
        'audit_logging_enabled',
        'api_key',
        'api_rate_limit_per_minute',
        'websocket_connection_limit',
        'metadata',
        'notes'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'api_key',
        'webhook_secret',
        'billing_info'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'subscription_status' => SubscriptionStatus::class,
        'trial_ends_at' => 'datetime',
        'feature_flags' => 'array',
        'limits' => 'array',
        'settings' => 'array',
        'subscription_started_at' => 'datetime',
        'subscription_expires_at' => 'datetime',
        'monthly_limit_tokens' => 'decimal:0',
        'used_tokens_current_month' => 'decimal:0',
        'enabled_features' => 'array',
        'max_users' => 'integer',
        'max_ai_providers' => 'integer',
        'max_documents' => 'integer',
        'max_workflows' => 'integer',
        'database_port' => 'integer',
        'database_config' => 'array',
        'security_settings' => 'array',
        'data_retention_enabled' => 'boolean',
        'data_retention_days' => 'integer',
        'audit_logging_enabled' => 'boolean',
        'api_rate_limit_per_minute' => 'integer',
        'websocket_connection_limit' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be appended to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'is_trial',
        'is_subscription_active',
        'days_until_expiry',
        'user_count',
        'storage_used_mb'
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tenant) {
            if (empty($tenant->slug)) {
                $tenant->slug = Str::slug($tenant->name);
            }
            if (empty($tenant->api_key)) {
                $tenant->api_key = 'tenant_' . Str::random(40);
            }
            if (empty($tenant->webhook_secret)) {
                $tenant->webhook_secret = Str::random(32);
            }
        });
    }

    /**
     * Get the users for the tenant.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the AI providers for the tenant.
     */
    public function aiProviders(): HasMany
    {
        return $this->hasMany(AIProvider::class);
    }

    /**
     * Get the tools for the tenant.
     */
    public function tools(): HasMany
    {
        return $this->hasMany(Tool::class);
    }

    /**
     * Get the workflows for the tenant.
     */
    public function workflows(): HasMany
    {
        return $this->hasMany(Workflow::class);
    }

    /**
     * Get the documents for the tenant.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the chat histories for the tenant.
     */
    public function chatHistories(): HasMany
    {
        return $this->hasMany(ChatHistory::class);
    }

    /**
     * Get the WebSocket sessions for the tenant.
     */
    public function webSocketSessions(): HasMany
    {
        return $this->hasMany(WebSocketSession::class);
    }

    /**
     * Get the system logs for the tenant.
     */
    public function systemLogs(): HasMany
    {
        return $this->hasMany(SystemLog::class);
    }

    /**
     * Check if tenant is on trial
     */
    public function getIsTrialAttribute(): bool
    {
        return $this->subscription_status === 'trial' &&
               $this->trial_ends_at &&
               $this->trial_ends_at->isFuture();
    }

    /**
     * Check if subscription is active
     */
    public function getIsSubscriptionActiveAttribute(): bool
    {
        return in_array($this->subscription_status, ['active', 'trial']) &&
               ($this->subscription_expires_at === null || $this->subscription_expires_at->isFuture());
    }

    /**
     * Get days until subscription expiry
     */
    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->subscription_expires_at) {
            return null;
        }

        return max(0, now()->diffInDays($this->subscription_expires_at, false));
    }

    /**
     * Get user count for the tenant
     */
    public function getUserCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * Get storage used in MB
     */
    public function getStorageUsedMbAttribute(): float
    {
        return $this->usage_stats['storage_used_mb'] ?? 0;
    }

    /**
     * Check if tenant has a specific feature
     */
    public function hasFeature(string $feature): bool
    {
        return $this->feature_flags[$feature] ?? false;
    }

    /**
     * Enable a feature for the tenant
     */
    public function enableFeature(string $feature): void
    {
        $flags = $this->feature_flags ?? [];
        $flags[$feature] = true;
        $this->update(['feature_flags' => $flags]);
    }

    /**
     * Disable a feature for the tenant
     */
    public function disableFeature(string $feature): void
    {
        $flags = $this->feature_flags ?? [];
        $flags[$feature] = false;
        $this->update(['feature_flags' => $flags]);
    }

    /**
     * Get a setting value
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * Set a setting value
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['settings' => $settings]);
    }

    /**
     * Get usage limit for a specific resource
     */
    public function getLimit(string $resource): ?int
    {
        return $this->limits[$resource] ?? null;
    }

    /**
     * Get current usage for a specific resource
     */
    public function getUsage(string $resource): int
    {
        return $this->usage_stats[$resource] ?? 0;
    }

    /**
     * Check if tenant has reached limit for a resource
     */
    public function hasReachedLimit(string $resource): bool
    {
        $limit = $this->getLimit($resource);
        if ($limit === null) {
            return false; // No limit set
        }

        return $this->getUsage($resource) >= $limit;
    }

    /**
     * Increment usage for a resource
     */
    public function incrementUsage(string $resource, int $amount = 1): void
    {
        $stats = $this->usage_stats ?? [];
        $stats[$resource] = ($stats[$resource] ?? 0) + $amount;
        $this->update(['usage_stats' => $stats]);
    }

    /**
     * Reset usage for a resource
     */
    public function resetUsage(string $resource): void
    {
        $stats = $this->usage_stats ?? [];
        $stats[$resource] = 0;
        $this->update(['usage_stats' => $stats]);
    }

    /**
     * Get owner user
     */
    public function owner(): ?User
    {
        return $this->users()->where('role', 'owner')->first();
    }

    /**
     * Get admin users
     */
    public function admins()
    {
        return $this->users()->whereIn('role', ['owner', 'admin']);
    }

    /**
     * Regenerate API key
     */
    public function regenerateApiKey(): string
    {
        $apiKey = 'tenant_' . Str::random(40);
        $this->update(['api_key' => $apiKey]);
        return $apiKey;
    }

    /**
     * Regenerate webhook secret
     */
    public function regenerateWebhookSecret(): string
    {
        $secret = Str::random(32);
        $this->update(['webhook_secret' => $secret]);
        return $secret;
    }

    /**
     * Scope query to active tenants
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope query to tenants with active subscriptions
     */
    public function scopeWithActiveSubscription($query)
    {
        return $query->where('subscription_status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('subscription_expires_at')
                          ->orWhere('subscription_expires_at', '>', now());
                    });
    }

    /**
     * Scope query to trial tenants
     */
    public function scopeOnTrial($query)
    {
        return $query->where('subscription_status', 'trial')
                    ->where('trial_ends_at', '>', now());
    }

    /**
     * Scope query to expired tenants
     */
    public function scopeExpired($query)
    {
        return $query->where(function ($q) {
            $q->where('subscription_expires_at', '<=', now())
              ->orWhere('trial_ends_at', '<=', now());
        });
    }
}
