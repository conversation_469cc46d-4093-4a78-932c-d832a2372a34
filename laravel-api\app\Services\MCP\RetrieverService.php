<?php

namespace App\Services\MCP;

use App\Models\Document;
use App\Models\Embedding;
use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;
use Illuminate\Support\Facades\DB;

class RetrieverService extends BaseService
{
    /**
     * Available retrieval strategies
     */
    protected array $retrievalStrategies = [
        'vector_similarity',
        'keyword_search',
        'hybrid_search',
        'semantic_search',
        'contextual_search',
    ];

    /**
     * Similarity functions for vector search
     */
    protected array $similarityFunctions = [
        'cosine' => '<=>',
        'l2' => '<->',
        'inner_product' => '<#>',
    ];

    /**
     * Document types and their processing strategies
     */
    protected array $documentTypes = [
        'text' => ['chunk_size' => 1000, 'overlap' => 200],
        'code' => ['chunk_size' => 500, 'overlap' => 100],
        'markdown' => ['chunk_size' => 1500, 'overlap' => 300],
        'json' => ['chunk_size' => 800, 'overlap' => 150],
        'csv' => ['chunk_size' => 2000, 'overlap' => 400],
        'pdf' => ['chunk_size' => 1200, 'overlap' => 250],
    ];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'retriever_service';
    }

    /**
     * Retrieve relevant knowledge based on query
     */
    public function retrieveRelevantKnowledge(string $query, array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('retrieve_relevant_knowledge', function () use ($query, $options) {
            // Parse options with defaults
            $strategy = $options['strategy'] ?? $this->getConfig('default_strategy', 'hybrid_search');
            $maxResults = $options['max_results'] ?? $this->getConfig('max_results', 10);
            $similarityThreshold = $options['similarity_threshold'] ?? $this->getConfig('similarity_threshold', 0.7);
            $includeMetadata = $options['include_metadata'] ?? true;

            // Generate query embedding if needed for vector-based strategies
            $queryEmbedding = null;
            if (in_array($strategy, ['vector_similarity', 'hybrid_search', 'semantic_search'])) {
                $queryEmbedding = $this->generateQueryEmbedding($query);
            }

            // Execute retrieval strategy
            $results = match ($strategy) {
                'vector_similarity' => $this->executeVectorSimilaritySearch($queryEmbedding, $maxResults, $similarityThreshold),
                'keyword_search' => $this->executeKeywordSearch($query, $maxResults),
                'hybrid_search' => $this->executeHybridSearch($query, $queryEmbedding, $maxResults, $similarityThreshold),
                'semantic_search' => $this->executeSemanticSearch($queryEmbedding, $options),
                'contextual_search' => $this->executeContextualSearch($query, $options),
                default => throw new \InvalidArgumentException("Unknown retrieval strategy: {$strategy}"),
            };

            // Post-process results
            $processedResults = $this->postProcessResults($results, $query, $options);

            // Add metadata if requested
            if ($includeMetadata) {
                $processedResults = $this->addRetrievalMetadata($processedResults, $query, $strategy, $options);
            }

            return [
                'query' => $query,
                'strategy' => $strategy,
                'results' => $processedResults,
                'total_found' => count($processedResults),
                'retrieval_metadata' => [
                    'similarity_threshold' => $similarityThreshold,
                    'max_results' => $maxResults,
                    'tenant_id' => $this->getCurrentTenantId(),
                    'timestamp' => now()->toISOString(),
                ],
            ];
        }, [
            'query_length' => strlen($query),
            'strategy' => $options['strategy'] ?? 'default',
            'max_results' => $options['max_results'] ?? 10,
        ]);
    }

    /**
     * Store document and generate embeddings
     */
    public function storeDocument(array $documentData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_documents');

        return $this->executeWithTracking('store_document', function () use ($documentData) {
            return $this->executeInTransaction(function () use ($documentData) {
                // Create document record
                $document = Document::create([
                    'tenant_id' => $this->getCurrentTenantId(),
                    'user_id' => $this->getCurrentUserId(),
                    'title' => $documentData['title'],
                    'content' => $documentData['content'],
                    'document_type' => $documentData['type'] ?? 'text',
                    'source_url' => $documentData['source_url'] ?? null,
                    'metadata' => $documentData['metadata'] ?? [],
                    'processing_status' => 'processing',
                ]);

                // Process document content into chunks
                $chunks = $this->processDocumentIntoChunks($document);

                // Generate embeddings for each chunk
                $embeddings = $this->generateEmbeddingsForChunks($document, $chunks);

                // Update document status
                $document->update([
                    'processing_status' => 'completed',
                    'chunk_count' => count($chunks),
                    'embedding_count' => count($embeddings),
                    'processed_at' => now(),
                ]);

                $this->logActivity('Document stored and processed', [
                    'document_id' => $document->id,
                    'chunk_count' => count($chunks),
                    'embedding_count' => count($embeddings),
                    'document_type' => $document->document_type,
                ]);

                return [
                    'document_id' => $document->id,
                    'chunks_created' => count($chunks),
                    'embeddings_created' => count($embeddings),
                    'processing_status' => 'completed',
                ];
            });
        }, [
            'document_type' => $documentData['type'] ?? 'text',
            'content_length' => strlen($documentData['content'] ?? ''),
        ]);
    }

    /**
     * Update document embeddings
     */
    public function updateDocumentEmbeddings(string $documentId): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_documents');

        return $this->executeWithTracking('update_document_embeddings', function () use ($documentId) {
            $document = Document::where('tenant_id', $this->getCurrentTenantId())
                              ->findOrFail($documentId);

            return $this->executeInTransaction(function () use ($document) {
                // Delete existing embeddings
                Embedding::where('document_id', $document->id)->delete();

                // Reprocess document
                $chunks = $this->processDocumentIntoChunks($document);
                $embeddings = $this->generateEmbeddingsForChunks($document, $chunks);

                // Update document
                $document->update([
                    'chunk_count' => count($chunks),
                    'embedding_count' => count($embeddings),
                    'processed_at' => now(),
                ]);

                return [
                    'document_id' => $document->id,
                    'chunks_updated' => count($chunks),
                    'embeddings_updated' => count($embeddings),
                ];
            });
        }, [
            'document_id' => $documentId,
        ]);
    }

    /**
     * Search documents with advanced filtering
     */
    public function searchDocuments(array $filters = [], array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('search_documents', function () use ($filters, $options) {
            $query = Document::where('tenant_id', $this->getCurrentTenantId());

            // Apply filters
            if (isset($filters['document_type'])) {
                $query->where('document_type', $filters['document_type']);
            }

            if (isset($filters['title'])) {
                $query->where('title', 'like', '%' . $filters['title'] . '%');
            }

            if (isset($filters['content'])) {
                $query->where('content', 'like', '%' . $filters['content'] . '%');
            }

            if (isset($filters['date_from'])) {
                $query->where('created_at', '>=', $filters['date_from']);
            }

            if (isset($filters['date_to'])) {
                $query->where('created_at', '<=', $filters['date_to']);
            }

            if (isset($filters['processing_status'])) {
                $query->where('processing_status', $filters['processing_status']);
            }

            // Apply sorting
            $sortBy = $options['sort_by'] ?? 'created_at';
            $sortOrder = $options['sort_order'] ?? 'desc';
            $query->orderBy($sortBy, $sortOrder);

            // Apply pagination
            $limit = $options['limit'] ?? 20;
            $offset = $options['offset'] ?? 0;
            $query->limit($limit)->offset($offset);

            $documents = $query->get();

            return [
                'documents' => $documents->toArray(),
                'total_found' => $documents->count(),
                'filters_applied' => $filters,
                'search_metadata' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder,
                ],
            ];
        }, [
            'filters_count' => count($filters),
            'limit' => $options['limit'] ?? 20,
        ]);
    }

    /**
     * Get retrieval analytics
     */
    public function getRetrievalAnalytics(array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('get_retrieval_analytics', function () use ($options) {
            $tenantId = $this->getCurrentTenantId();
            $dateFrom = $options['date_from'] ?? now()->subDays(30);
            $dateTo = $options['date_to'] ?? now();

            // Document statistics
            $totalDocuments = Document::where('tenant_id', $tenantId)->count();
            $processedDocuments = Document::where('tenant_id', $tenantId)
                                        ->where('processing_status', 'completed')
                                        ->count();

            // Embedding statistics
            $totalEmbeddings = Embedding::whereHas('document', function ($query) use ($tenantId) {
                $query->where('tenant_id', $tenantId);
            })->count();

            // Document type distribution
            $documentTypeDistribution = Document::where('tenant_id', $tenantId)
                                              ->selectRaw('document_type, COUNT(*) as count')
                                              ->groupBy('document_type')
                                              ->pluck('count', 'document_type')
                                              ->toArray();

            // Recent activity
            $recentDocuments = Document::where('tenant_id', $tenantId)
                                     ->whereBetween('created_at', [$dateFrom, $dateTo])
                                     ->count();

            return [
                'tenant_id' => $tenantId,
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo,
                ],
                'document_statistics' => [
                    'total_documents' => $totalDocuments,
                    'processed_documents' => $processedDocuments,
                    'processing_rate' => $totalDocuments > 0 ? round($processedDocuments / $totalDocuments * 100, 2) : 0,
                    'recent_documents' => $recentDocuments,
                ],
                'embedding_statistics' => [
                    'total_embeddings' => $totalEmbeddings,
                    'average_embeddings_per_document' => $processedDocuments > 0 ? round($totalEmbeddings / $processedDocuments, 2) : 0,
                ],
                'document_type_distribution' => $documentTypeDistribution,
                'retrieval_strategies' => $this->retrievalStrategies,
                'analysis_timestamp' => now()->toISOString(),
            ];
        }, [
            'date_range_days' => $options['date_from'] ? now()->diffInDays($options['date_from']) : 30,
        ]);
    }

    /**
     * Execute vector similarity search
     */
    protected function executeVectorSimilaritySearch(array $queryEmbedding, int $maxResults, float $threshold): array
    {
        $similarityFunction = $this->getConfig('similarity_function', 'cosine');
        $operator = $this->similarityFunctions[$similarityFunction];

        $embeddings = DB::table('embeddings')
            ->join('documents', 'embeddings.document_id', '=', 'documents.id')
            ->where('documents.tenant_id', $this->getCurrentTenantId())
            ->where('documents.processing_status', 'completed')
            ->selectRaw("
                embeddings.*,
                documents.title,
                documents.document_type,
                documents.metadata,
                (embeddings.embedding {$operator} ?) as similarity
            ", [json_encode($queryEmbedding)])
            ->havingRaw('similarity < ?', [1 - $threshold]) // Adjust based on similarity function
            ->orderBy('similarity')
            ->limit($maxResults)
            ->get();

        return $embeddings->map(function ($embedding) {
            return [
                'document_id' => $embedding->document_id,
                'chunk_id' => $embedding->id,
                'content' => $embedding->content,
                'similarity_score' => 1 - $embedding->similarity, // Convert to similarity score
                'document_title' => $embedding->title,
                'document_type' => $embedding->document_type,
                'metadata' => json_decode($embedding->metadata, true),
                'chunk_index' => $embedding->chunk_index,
            ];
        })->toArray();
    }

    /**
     * Execute keyword search
     */
    protected function executeKeywordSearch(string $query, int $maxResults): array
    {
        $keywords = $this->extractKeywords($query);
        $searchPattern = '%' . implode('%', $keywords) . '%';

        $results = DB::table('embeddings')
            ->join('documents', 'embeddings.document_id', '=', 'documents.id')
            ->where('documents.tenant_id', $this->getCurrentTenantId())
            ->where('documents.processing_status', 'completed')
            ->where(function ($q) use ($searchPattern, $keywords) {
                $q->where('embeddings.content', 'like', $searchPattern)
                  ->orWhere('documents.title', 'like', $searchPattern);

                foreach ($keywords as $keyword) {
                    $q->orWhere('embeddings.content', 'like', "%{$keyword}%");
                }
            })
            ->select([
                'embeddings.*',
                'documents.title',
                'documents.document_type',
                'documents.metadata'
            ])
            ->limit($maxResults)
            ->get();

        return $results->map(function ($result) use ($keywords) {
            $relevanceScore = $this->calculateKeywordRelevance($result->content, $keywords);

            return [
                'document_id' => $result->document_id,
                'chunk_id' => $result->id,
                'content' => $result->content,
                'relevance_score' => $relevanceScore,
                'document_title' => $result->title,
                'document_type' => $result->document_type,
                'metadata' => json_decode($result->metadata, true),
                'chunk_index' => $result->chunk_index,
                'matched_keywords' => $this->findMatchedKeywords($result->content, $keywords),
            ];
        })->sortByDesc('relevance_score')->values()->toArray();
    }

    /**
     * Execute hybrid search (combination of vector and keyword)
     */
    protected function executeHybridSearch(string $query, array $queryEmbedding, int $maxResults, float $threshold): array
    {
        // Get vector similarity results
        $vectorResults = $this->executeVectorSimilaritySearch($queryEmbedding, $maxResults * 2, $threshold);

        // Get keyword search results
        $keywordResults = $this->executeKeywordSearch($query, $maxResults * 2);

        // Combine and rank results
        $combinedResults = $this->combineSearchResults($vectorResults, $keywordResults);

        // Apply hybrid scoring
        $scoredResults = array_map(function ($result) {
            $vectorWeight = $this->getConfig('hybrid.vector_weight', 0.7);
            $keywordWeight = $this->getConfig('hybrid.keyword_weight', 0.3);

            $vectorScore = $result['similarity_score'] ?? 0;
            $keywordScore = $result['relevance_score'] ?? 0;

            $result['hybrid_score'] = ($vectorScore * $vectorWeight) + ($keywordScore * $keywordWeight);
            return $result;
        }, $combinedResults);

        // Sort by hybrid score and limit results
        usort($scoredResults, fn($a, $b) => $b['hybrid_score'] <=> $a['hybrid_score']);

        return array_slice($scoredResults, 0, $maxResults);
    }

    /**
     * Execute semantic search with context awareness
     */
    protected function executeSemanticSearch(array $queryEmbedding, array $options): array
    {
        $context = $options['context'] ?? [];
        $intent = $options['intent'] ?? 'general';
        $maxResults = $options['max_results'] ?? 10;

        // Adjust search based on intent
        $intentWeights = $this->getIntentWeights($intent);

        // Execute base vector search
        $baseResults = $this->executeVectorSimilaritySearch($queryEmbedding, $maxResults * 2, 0.5);

        // Apply semantic filtering and ranking
        $semanticResults = array_map(function ($result) use ($intentWeights, $context) {
            $semanticScore = $this->calculateSemanticScore($result, $intentWeights, $context);
            $result['semantic_score'] = $semanticScore;
            return $result;
        }, $baseResults);

        // Sort by semantic score
        usort($semanticResults, fn($a, $b) => $b['semantic_score'] <=> $a['semantic_score']);

        return array_slice($semanticResults, 0, $maxResults);
    }

    /**
     * Execute contextual search with conversation history
     */
    protected function executeContextualSearch(string $query, array $options): array
    {
        $sessionId = $options['session_id'] ?? null;
        $conversationContext = $options['conversation_context'] ?? [];
        $maxResults = $options['max_results'] ?? 10;

        // Build contextual query
        $contextualQuery = $this->buildContextualQuery($query, $conversationContext);

        // Generate embedding for contextual query
        $contextualEmbedding = $this->generateQueryEmbedding($contextualQuery);

        // Execute search with context boost
        $results = $this->executeVectorSimilaritySearch($contextualEmbedding, $maxResults, 0.6);

        // Apply contextual scoring
        return array_map(function ($result) use ($conversationContext) {
            $contextualScore = $this->calculateContextualScore($result, $conversationContext);
            $result['contextual_score'] = $contextualScore;
            $result['final_score'] = ($result['similarity_score'] + $contextualScore) / 2;
            return $result;
        }, $results);
    }

    /**
     * Process document into chunks
     */
    protected function processDocumentIntoChunks(Document $document): array
    {
        $content = $document->content;
        $documentType = $document->document_type;
        $chunkConfig = $this->documentTypes[$documentType] ?? $this->documentTypes['text'];

        $chunkSize = $chunkConfig['chunk_size'];
        $overlap = $chunkConfig['overlap'];

        $chunks = [];
        $contentLength = strlen($content);

        for ($i = 0; $i < $contentLength; $i += $chunkSize - $overlap) {
            $chunk = substr($content, $i, $chunkSize);

            if (strlen(trim($chunk)) > 50) { // Skip very small chunks
                $chunks[] = [
                    'content' => $chunk,
                    'start_position' => $i,
                    'end_position' => min($i + $chunkSize, $contentLength),
                    'chunk_index' => count($chunks),
                ];
            }
        }

        return $chunks;
    }

    /**
     * Generate embeddings for document chunks
     */
    protected function generateEmbeddingsForChunks(Document $document, array $chunks): array
    {
        $embeddings = [];

        foreach ($chunks as $chunk) {
            // Generate embedding vector (this would integrate with actual embedding service)
            $embeddingVector = $this->generateEmbeddingVector($chunk['content']);

            $embedding = Embedding::create([
                'document_id' => $document->id,
                'content' => $chunk['content'],
                'embedding' => $embeddingVector,
                'chunk_index' => $chunk['chunk_index'],
                'start_position' => $chunk['start_position'],
                'end_position' => $chunk['end_position'],
                'metadata' => [
                    'chunk_size' => strlen($chunk['content']),
                    'document_type' => $document->document_type,
                ],
            ]);

            $embeddings[] = $embedding;
        }

        return $embeddings;
    }

    /**
     * Generate query embedding
     */
    protected function generateQueryEmbedding(string $query): array
    {
        // This would integrate with actual embedding service (OpenAI, Hugging Face, etc.)
        // For now, return a placeholder vector
        return $this->generateEmbeddingVector($query);
    }

    /**
     * Generate embedding vector (placeholder implementation)
     */
    protected function generateEmbeddingVector(string $text): array
    {
        // This is a placeholder - in production, this would call an actual embedding service
        $dimensions = $this->getConfig('embedding_dimensions', 1536);
        $vector = [];

        // Simple hash-based vector generation for demonstration
        $hash = hash('sha256', $text);
        for ($i = 0; $i < $dimensions; $i++) {
            $vector[] = (hexdec(substr($hash, $i % 64, 2)) / 255) * 2 - 1;
        }

        return $vector;
    }

    /**
     * Extract keywords from query
     */
    protected function extractKeywords(string $query): array
    {
        // Remove stop words and extract meaningful keywords
        $stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        $words = preg_split('/\s+/', strtolower($query));

        return array_filter($words, function ($word) use ($stopWords) {
            return strlen($word) > 2 && !in_array($word, $stopWords);
        });
    }

    /**
     * Calculate keyword relevance score
     */
    protected function calculateKeywordRelevance(string $content, array $keywords): float
    {
        $content = strtolower($content);
        $totalMatches = 0;
        $totalKeywords = count($keywords);

        foreach ($keywords as $keyword) {
            $matches = substr_count($content, strtolower($keyword));
            $totalMatches += $matches;
        }

        return $totalKeywords > 0 ? min(1.0, $totalMatches / $totalKeywords) : 0;
    }

    /**
     * Find matched keywords in content
     */
    protected function findMatchedKeywords(string $content, array $keywords): array
    {
        $content = strtolower($content);
        $matched = [];

        foreach ($keywords as $keyword) {
            if (str_contains($content, strtolower($keyword))) {
                $matched[] = $keyword;
            }
        }

        return $matched;
    }

    /**
     * Combine search results from different strategies
     */
    protected function combineSearchResults(array $vectorResults, array $keywordResults): array
    {
        $combined = [];
        $seen = [];

        // Add vector results
        foreach ($vectorResults as $result) {
            $key = $result['chunk_id'];
            if (!isset($seen[$key])) {
                $combined[$key] = $result;
                $seen[$key] = true;
            }
        }

        // Add keyword results, merging scores if already present
        foreach ($keywordResults as $result) {
            $key = $result['chunk_id'];
            if (isset($combined[$key])) {
                // Merge scores
                $combined[$key]['relevance_score'] = $result['relevance_score'];
                $combined[$key]['matched_keywords'] = $result['matched_keywords'];
            } else {
                $combined[$key] = $result;
            }
        }

        return array_values($combined);
    }

    /**
     * Get intent-based weights for semantic search
     */
    protected function getIntentWeights(string $intent): array
    {
        $weights = [
            'question' => ['factual' => 0.8, 'explanatory' => 0.6, 'procedural' => 0.4],
            'command' => ['procedural' => 0.9, 'technical' => 0.7, 'factual' => 0.3],
            'search' => ['factual' => 0.7, 'comprehensive' => 0.8, 'recent' => 0.6],
            'analysis' => ['technical' => 0.8, 'comprehensive' => 0.9, 'comparative' => 0.7],
            'general' => ['factual' => 0.5, 'explanatory' => 0.5, 'procedural' => 0.5],
        ];

        return $weights[$intent] ?? $weights['general'];
    }

    /**
     * Calculate semantic score based on intent and context
     */
    protected function calculateSemanticScore(array $result, array $intentWeights, array $context): float
    {
        $baseScore = $result['similarity_score'] ?? 0;
        $contentType = $this->classifyContentType($result['content']);

        $intentBoost = $intentWeights[$contentType] ?? 0.5;
        $contextBoost = $this->calculateContextBoost($result, $context);

        return min(1.0, $baseScore * (1 + $intentBoost + $contextBoost));
    }

    /**
     * Classify content type for semantic scoring
     */
    protected function classifyContentType(string $content): string
    {
        $content = strtolower($content);

        if (preg_match('/\b(how to|step|process|procedure)\b/', $content)) {
            return 'procedural';
        }

        if (preg_match('/\b(code|function|class|method|api)\b/', $content)) {
            return 'technical';
        }

        if (preg_match('/\b(what|definition|meaning|explanation)\b/', $content)) {
            return 'explanatory';
        }

        if (preg_match('/\b(compare|versus|difference|better)\b/', $content)) {
            return 'comparative';
        }

        return 'factual';
    }

    /**
     * Calculate context boost for contextual search
     */
    protected function calculateContextBoost(array $result, array $context): float
    {
        if (empty($context)) {
            return 0;
        }

        $boost = 0;
        $content = strtolower($result['content']);

        // Check for topic continuity
        if (isset($context['current_topic'])) {
            $topic = strtolower($context['current_topic']);
            if (str_contains($content, $topic)) {
                $boost += 0.2;
            }
        }

        // Check for entity mentions
        if (isset($context['entities'])) {
            foreach ($context['entities'] as $entity) {
                if (str_contains($content, strtolower($entity))) {
                    $boost += 0.1;
                }
            }
        }

        return min(0.5, $boost); // Cap boost at 0.5
    }

    /**
     * Build contextual query with conversation history
     */
    protected function buildContextualQuery(string $query, array $conversationContext): string
    {
        if (empty($conversationContext)) {
            return $query;
        }

        $contextualParts = [$query];

        // Add recent topics
        if (isset($conversationContext['recent_topics'])) {
            $contextualParts = array_merge($contextualParts, array_slice($conversationContext['recent_topics'], -3));
        }

        // Add important entities
        if (isset($conversationContext['entities'])) {
            $contextualParts = array_merge($contextualParts, array_slice($conversationContext['entities'], -5));
        }

        return implode(' ', array_unique($contextualParts));
    }

    /**
     * Calculate contextual score
     */
    protected function calculateContextualScore(array $result, array $conversationContext): float
    {
        return $this->calculateContextBoost($result, $conversationContext);
    }

    /**
     * Post-process retrieval results
     */
    protected function postProcessResults(array $results, string $query, array $options): array
    {
        // Remove duplicates based on content similarity
        $results = $this->removeDuplicateResults($results);

        // Apply relevance filtering
        $minRelevance = $options['min_relevance'] ?? 0.1;
        $results = array_filter($results, function ($result) use ($minRelevance) {
            $score = $result['similarity_score'] ?? $result['relevance_score'] ?? $result['hybrid_score'] ?? 0;
            return $score >= $minRelevance;
        });

        // Highlight query terms in content
        if ($options['highlight'] ?? false) {
            $results = $this->highlightQueryTerms($results, $query);
        }

        return array_values($results);
    }

    /**
     * Remove duplicate results based on content similarity
     */
    protected function removeDuplicateResults(array $results): array
    {
        $unique = [];
        $seen = [];

        foreach ($results as $result) {
            $contentHash = md5($result['content']);

            if (!isset($seen[$contentHash])) {
                $unique[] = $result;
                $seen[$contentHash] = true;
            }
        }

        return $unique;
    }

    /**
     * Highlight query terms in content
     */
    protected function highlightQueryTerms(array $results, string $query): array
    {
        $keywords = $this->extractKeywords($query);

        return array_map(function ($result) use ($keywords) {
            $content = $result['content'];

            foreach ($keywords as $keyword) {
                $content = preg_replace(
                    '/\b' . preg_quote($keyword, '/') . '\b/i',
                    '<mark>$0</mark>',
                    $content
                );
            }

            $result['highlighted_content'] = $content;
            return $result;
        }, $results);
    }

    /**
     * Add retrieval metadata to results
     */
    protected function addRetrievalMetadata(array $results, string $query, string $strategy, array $options): array
    {
        $metadata = [
            'query_analysis' => [
                'query_length' => strlen($query),
                'extracted_keywords' => $this->extractKeywords($query),
                'query_complexity' => $this->assessQueryComplexity($query),
            ],
            'retrieval_strategy' => $strategy,
            'performance_metrics' => [
                'results_count' => count($results),
                'average_score' => $this->calculateAverageScore($results),
                'score_distribution' => $this->calculateScoreDistribution($results),
            ],
            'tenant_context' => [
                'tenant_id' => $this->getCurrentTenantId(),
                'user_id' => $this->getCurrentUserId(),
            ],
        ];

        return array_map(function ($result) use ($metadata) {
            $result['retrieval_metadata'] = $metadata;
            return $result;
        }, $results);
    }

    /**
     * Assess query complexity
     */
    protected function assessQueryComplexity(string $query): string
    {
        $wordCount = str_word_count($query);
        $hasQuestions = str_contains($query, '?');
        $hasOperators = preg_match('/\b(and|or|not)\b/i', $query);

        if ($wordCount > 10 || $hasOperators) {
            return 'complex';
        } elseif ($wordCount > 5 || $hasQuestions) {
            return 'medium';
        } else {
            return 'simple';
        }
    }

    /**
     * Calculate average score across results
     */
    protected function calculateAverageScore(array $results): float
    {
        if (empty($results)) {
            return 0;
        }

        $totalScore = 0;
        foreach ($results as $result) {
            $score = $result['similarity_score'] ?? $result['relevance_score'] ?? $result['hybrid_score'] ?? 0;
            $totalScore += $score;
        }

        return round($totalScore / count($results), 3);
    }

    /**
     * Calculate score distribution
     */
    protected function calculateScoreDistribution(array $results): array
    {
        $distribution = ['high' => 0, 'medium' => 0, 'low' => 0];

        foreach ($results as $result) {
            $score = $result['similarity_score'] ?? $result['relevance_score'] ?? $result['hybrid_score'] ?? 0;

            if ($score >= 0.8) {
                $distribution['high']++;
            } elseif ($score >= 0.5) {
                $distribution['medium']++;
            } else {
                $distribution['low']++;
            }
        }

        return $distribution;
    }
}
