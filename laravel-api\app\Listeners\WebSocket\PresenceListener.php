<?php

namespace App\Listeners\WebSocket;

use App\Events\WebSocket\PresenceEvent;
use App\Services\Core\LoggingService;
use App\Services\WebSocket\ChannelManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;

class PresenceListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected LoggingService $loggingService;
    protected ChannelManager $channelManager;

    /**
     * Create the event listener.
     */
    public function __construct(
        LoggingService $loggingService,
        ChannelManager $channelManager
    ) {
        $this->loggingService = $loggingService;
        $this->channelManager = $channelManager;
    }

    /**
     * Handle the event.
     */
    public function handle(PresenceEvent $event): void
    {
        try {
            // Log the presence event
            $this->loggingService->logWebSocketEvent('presence_event_processed', [
                'tenant_id' => $event->tenantId,
                'user_id' => $event->userId,
                'presence_type' => $event->presenceType,
                'status' => $event->status,
                'timestamp' => $event->timestamp,
            ]);

            // Update user presence status
            $this->updateUserPresence($event);

            // Handle specific presence types
            $this->processPresenceByType($event);

            // Update channel activity
            $this->updateChannelActivity($event);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('presence_listener_error', [
                'tenant_id' => $event->tenantId,
                'user_id' => $event->userId,
                'presence_type' => $event->presenceType,
                'error' => $e->getMessage(),
            ], 'error');

            // Re-throw for queue retry mechanism
            throw $e;
        }
    }

    /**
     * Update user presence status
     */
    protected function updateUserPresence(PresenceEvent $event): void
    {
        $cacheKey = "user_presence:{$event->tenantId}:{$event->userId}";

        $presenceData = [
            'user_id' => $event->userId,
            'tenant_id' => $event->tenantId,
            'status' => $event->status,
            'presence_type' => $event->presenceType,
            'user_info' => $event->userInfo,
            'last_seen' => $event->timestamp,
            'metadata' => $event->metadata,
        ];

        // Cache presence data for 5 minutes
        Cache::put($cacheKey, $presenceData, 300);

        // Also update tenant-wide presence list
        $this->updateTenantPresenceList($event, $presenceData);
    }

    /**
     * Update tenant-wide presence list
     */
    protected function updateTenantPresenceList(PresenceEvent $event, array $presenceData): void
    {
        $tenantPresenceKey = "tenant_presence:{$event->tenantId}";
        $tenantPresence = Cache::get($tenantPresenceKey, []);

        if ($event->status === 'online') {
            $tenantPresence[$event->userId] = $presenceData;
        } else {
            unset($tenantPresence[$event->userId]);
        }

        // Cache tenant presence for 5 minutes
        Cache::put($tenantPresenceKey, $tenantPresence, 300);
    }

    /**
     * Process presence by type
     */
    protected function processPresenceByType(PresenceEvent $event): void
    {
        switch ($event->presenceType) {
            case 'join':
                $this->handleUserJoin($event);
                break;

            case 'leave':
                $this->handleUserLeave($event);
                break;

            case 'status_change':
                $this->handleStatusChange($event);
                break;

            case 'activity':
                $this->handleUserActivity($event);
                break;
        }
    }

    /**
     * Handle user join
     */
    protected function handleUserJoin(PresenceEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('user_joined', [
            'tenant_id' => $event->tenantId,
            'user_id' => $event->userId,
            'user_name' => $event->userInfo['name'] ?? 'Unknown',
            'timestamp' => $event->timestamp,
        ]);

        // Update channel subscriptions
        $presenceChannel = "presence.{$event->tenantId}";
        $this->channelManager->subscribeUserToChannel(
            $event->tenantId,
            $event->userId,
            $presenceChannel,
            'presence'
        );
    }

    /**
     * Handle user leave
     */
    protected function handleUserLeave(PresenceEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('user_left', [
            'tenant_id' => $event->tenantId,
            'user_id' => $event->userId,
            'user_name' => $event->userInfo['name'] ?? 'Unknown',
            'timestamp' => $event->timestamp,
        ]);

        // Clean up user channels
        $this->channelManager->cleanupUserChannels($event->tenantId, $event->userId);

        // Remove from presence cache
        $cacheKey = "user_presence:{$event->tenantId}:{$event->userId}";
        Cache::forget($cacheKey);
    }

    /**
     * Handle status change
     */
    protected function handleStatusChange(PresenceEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('user_status_changed', [
            'tenant_id' => $event->tenantId,
            'user_id' => $event->userId,
            'new_status' => $event->status,
            'timestamp' => $event->timestamp,
        ]);
    }

    /**
     * Handle user activity
     */
    protected function handleUserActivity(PresenceEvent $event): void
    {
        // Update last activity timestamp
        $cacheKey = "user_activity:{$event->tenantId}:{$event->userId}";
        Cache::put($cacheKey, $event->timestamp, 300);

        // Update channel activity if specified
        if (isset($event->metadata['channel_name'])) {
            $this->channelManager->updateUserActivity(
                $event->tenantId,
                $event->userId,
                $event->metadata['channel_name']
            );
        }
    }

    /**
     * Update channel activity
     */
    protected function updateChannelActivity(PresenceEvent $event): void
    {
        // Get all channels for the tenant
        $channelStats = $this->channelManager->getChannelStatistics($event->tenantId);

        // Log channel activity update
        $this->loggingService->logWebSocketEvent('channel_activity_updated', [
            'tenant_id' => $event->tenantId,
            'user_id' => $event->userId,
            'active_channels' => $channelStats['active_channels'],
            'users_online' => $channelStats['users_online'],
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(PresenceEvent $event, \Throwable $exception): void
    {
        $this->loggingService->logWebSocketEvent('presence_listener_failed', [
            'tenant_id' => $event->tenantId,
            'user_id' => $event->userId,
            'presence_type' => $event->presenceType,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');
    }
}
