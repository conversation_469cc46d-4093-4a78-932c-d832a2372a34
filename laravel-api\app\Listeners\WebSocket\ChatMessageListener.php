<?php

namespace App\Listeners\WebSocket;

use App\Events\WebSocket\ChatMessageEvent;
use App\Services\Core\LoggingService;
use App\Services\WebSocket\SessionManager;
use App\Models\ChatHistory;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ChatMessageListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected LoggingService $loggingService;
    protected SessionManager $sessionManager;

    /**
     * Create the event listener.
     */
    public function __construct(
        LoggingService $loggingService,
        SessionManager $sessionManager
    ) {
        $this->loggingService = $loggingService;
        $this->sessionManager = $sessionManager;
    }

    /**
     * Handle the event.
     */
    public function handle(ChatMessageEvent $event): void
    {
        try {
            // Log the chat message event
            $this->loggingService->logWebSocketEvent('chat_message_broadcasted', [
                'tenant_id' => $event->tenantId,
                'session_id' => $event->sessionId,
                'message_id' => $event->messageId,
                'message_type' => $event->messageType,
                'sender_id' => $event->sender['user_id'] ?? null,
                'timestamp' => $event->timestamp,
            ]);

            // Store chat message in database
            $this->storeChatMessage($event);

            // Update session metrics
            $this->updateSessionMetrics($event);

            // Handle message-specific processing
            $this->processMessageByType($event);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('chat_message_listener_error', [
                'tenant_id' => $event->tenantId,
                'session_id' => $event->sessionId,
                'message_id' => $event->messageId,
                'error' => $e->getMessage(),
            ], 'error');

            // Re-throw for queue retry mechanism
            throw $e;
        }
    }

    /**
     * Store chat message in database
     */
    protected function storeChatMessage(ChatMessageEvent $event): void
    {
        ChatHistory::create([
            'tenant_id' => $event->tenantId,
            'session_id' => $event->sessionId,
            'message_id' => $event->messageId,
            'user_id' => $event->sender['user_id'] ?? null,
            'message_type' => $event->messageType,
            'message_content' => $event->messageData,
            'sender_info' => $event->sender,
            'metadata' => $event->metadata,
            'created_at' => now(),
        ]);
    }

    /**
     * Update session metrics
     */
    protected function updateSessionMetrics(ChatMessageEvent $event): void
    {
        // Find session by session_id and update metrics
        $sessions = $this->sessionManager->getTenantSessions($event->tenantId);

        foreach ($sessions['active_sessions'] as $session) {
            if (isset($session['metadata']['session_id']) &&
                $session['metadata']['session_id'] === $event->sessionId) {

                $this->sessionManager->updateSessionMetrics($session['session_id'], [
                    'messages_sent' => 1,
                    'bytes_sent' => strlen(json_encode($event->messageData)),
                ]);
                break;
            }
        }
    }

    /**
     * Process message by type
     */
    protected function processMessageByType(ChatMessageEvent $event): void
    {
        switch ($event->messageType) {
            case 'user_message':
                $this->handleUserMessage($event);
                break;

            case 'ai_response':
                $this->handleAIResponse($event);
                break;

            case 'system_message':
                $this->handleSystemMessage($event);
                break;

            case 'file_upload':
                $this->handleFileUpload($event);
                break;

            case 'mcp_result':
                $this->handleMCPResult($event);
                break;
        }
    }

    /**
     * Handle user message
     */
    protected function handleUserMessage(ChatMessageEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('user_message_processed', [
            'tenant_id' => $event->tenantId,
            'session_id' => $event->sessionId,
            'message_id' => $event->messageId,
            'user_id' => $event->sender['user_id'] ?? null,
        ]);
    }

    /**
     * Handle AI response
     */
    protected function handleAIResponse(ChatMessageEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('ai_response_processed', [
            'tenant_id' => $event->tenantId,
            'session_id' => $event->sessionId,
            'message_id' => $event->messageId,
            'ai_provider' => $event->metadata['ai_provider'] ?? 'unknown',
            'response_time_ms' => $event->metadata['response_time_ms'] ?? null,
        ]);
    }

    /**
     * Handle system message
     */
    protected function handleSystemMessage(ChatMessageEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('system_message_processed', [
            'tenant_id' => $event->tenantId,
            'session_id' => $event->sessionId,
            'message_id' => $event->messageId,
            'system_type' => $event->metadata['system_type'] ?? 'general',
        ]);
    }

    /**
     * Handle file upload
     */
    protected function handleFileUpload(ChatMessageEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('file_upload_processed', [
            'tenant_id' => $event->tenantId,
            'session_id' => $event->sessionId,
            'message_id' => $event->messageId,
            'file_name' => $event->messageData['file_name'] ?? 'unknown',
            'file_size' => $event->messageData['file_size'] ?? 0,
            'file_type' => $event->messageData['file_type'] ?? 'unknown',
        ]);
    }

    /**
     * Handle MCP result
     */
    protected function handleMCPResult(ChatMessageEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('mcp_result_processed', [
            'tenant_id' => $event->tenantId,
            'session_id' => $event->sessionId,
            'message_id' => $event->messageId,
            'mcp_operation' => $event->metadata['mcp_operation'] ?? 'unknown',
            'execution_time_ms' => $event->metadata['execution_time_ms'] ?? null,
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(ChatMessageEvent $event, \Throwable $exception): void
    {
        $this->loggingService->logWebSocketEvent('chat_message_listener_failed', [
            'tenant_id' => $event->tenantId,
            'session_id' => $event->sessionId,
            'message_id' => $event->messageId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');
    }
}
