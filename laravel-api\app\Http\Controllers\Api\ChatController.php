<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\MCP\MCPOrchestrator;
use App\Services\WebSocket\WebSocketService;
use App\Services\Core\LoggingService;
use App\Services\Core\TenantContextService;
use App\Events\WebSocket\ChatMessageEvent;
use App\Models\ChatHistory;
use App\Models\WebSocketSession;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ChatController extends Controller
{
    protected MCPOrchestrator $mcpOrchestrator;
    protected WebSocketService $webSocketService;
    protected LoggingService $loggingService;
    protected TenantContextService $tenantContext;

    public function __construct(
        MCPOrchestrator $mcpOrchestrator,
        WebSocketService $webSocketService,
        LoggingService $loggingService,
        TenantContextService $tenantContext
    ) {
        $this->mcpOrchestrator = $mcpOrchestrator;
        $this->webSocketService = $webSocketService;
        $this->loggingService = $loggingService;
        $this->tenantContext = $tenantContext;
    }

    /**
     * Get all chat sessions for the authenticated user
     */
    public function sessions(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $sessions = WebSocketSession::where('tenant_id', $tenant->id)
                ->where('user_id', $user->id)
                ->where('session_type', 'chat')
                ->with(['chatHistories' => function ($query) {
                    $query->latest()->limit(1);
                }])
                ->orderBy('last_activity_at', 'desc')
                ->paginate(20);

            $this->loggingService->logHttpRequest('chat_sessions_retrieved', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'sessions_count' => $sessions->count(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $sessions,
                'message' => 'Chat sessions retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('chat_sessions_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve chat sessions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new chat session
     */
    public function createSession(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'nullable|string|max:255',
                'metadata' => 'nullable|array',
                'ai_provider' => 'nullable|string|max:50',
                'model' => 'nullable|string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            DB::beginTransaction();

            // Create WebSocket session
            $session = WebSocketSession::create([
                'id' => Str::uuid(),
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_type' => 'chat',
                'channel_name' => "private-chat.{$tenant->id}.{$user->id}." . Str::random(8),
                'connection_id' => Str::uuid(),
                'status' => 'active',
                'metadata' => array_merge($request->input('metadata', []), [
                    'title' => $request->input('title', 'New Chat'),
                    'ai_provider' => $request->input('ai_provider', 'openai'),
                    'model' => $request->input('model', 'gpt-4'),
                    'created_via' => 'api',
                ]),
                'connected_at' => now(),
                'last_activity_at' => now(),
            ]);

            // Initialize session in WebSocket service
            $this->webSocketService->initializeSession(
                $session->id,
                $tenant->id,
                $user->id,
                $session->channel_name,
                $session->metadata
            );

            DB::commit();

            $this->loggingService->logHttpRequest('chat_session_created', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $session->id,
                'channel_name' => $session->channel_name,
            ]);

            return response()->json([
                'success' => true,
                'data' => $session,
                'message' => 'Chat session created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            $this->loggingService->logHttpRequest('chat_session_creation_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to create chat session',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific chat session
     */
    public function getSession(Request $request, string $sessionId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $session = WebSocketSession::where('id', $sessionId)
                ->where('tenant_id', $tenant->id)
                ->where('user_id', $user->id)
                ->with(['chatHistories' => function ($query) {
                    $query->orderBy('created_at', 'asc');
                }])
                ->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat session not found'
                ], 404);
            }

            $this->loggingService->logHttpRequest('chat_session_retrieved', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $sessionId,
            ]);

            return response()->json([
                'success' => true,
                'data' => $session,
                'message' => 'Chat session retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('chat_session_retrieval_error', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve chat session',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a message in a chat session
     */
    public function sendMessage(Request $request, string $sessionId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'message' => 'required|string|max:10000',
                'message_type' => 'nullable|string|in:text,image,file,system',
                'metadata' => 'nullable|array',
                'stream' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Verify session exists and belongs to user
            $session = WebSocketSession::where('id', $sessionId)
                ->where('tenant_id', $tenant->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat session not found'
                ], 404);
            }

            DB::beginTransaction();

            // Create user message record
            $userMessage = ChatHistory::create([
                'id' => Str::uuid(),
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $sessionId,
                'message_type' => $request->input('message_type', 'text'),
                'role' => 'user',
                'content' => $request->input('message'),
                'metadata' => $request->input('metadata', []),
                'processing_status' => 'completed',
            ]);

            // Update session activity
            $session->update([
                'last_activity_at' => now(),
                'message_count' => $session->message_count + 1,
            ]);

            // Broadcast user message via WebSocket
            broadcast(new ChatMessageEvent(
                $tenant->id,
                $sessionId,
                $user->id,
                $user->name,
                $request->input('message'),
                'user',
                $userMessage->id,
                $request->input('metadata', [])
            ));

            DB::commit();

            // Process message with MCP Orchestrator (async)
            $this->processMCPResponse($tenant, $user, $session, $userMessage, $request->input('stream', false));

            $this->loggingService->logHttpRequest('chat_message_sent', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $sessionId,
                'message_id' => $userMessage->id,
                'message_length' => strlen($request->input('message')),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'message' => $userMessage,
                    'session' => $session->fresh(),
                ],
                'message' => 'Message sent successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            $this->loggingService->logHttpRequest('chat_message_send_error', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get messages for a chat session
     */
    public function getMessages(Request $request, string $sessionId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Verify session exists and belongs to user
            $session = WebSocketSession::where('id', $sessionId)
                ->where('tenant_id', $tenant->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat session not found'
                ], 404);
            }

            $messages = ChatHistory::where('session_id', $sessionId)
                ->where('tenant_id', $tenant->id)
                ->orderBy('created_at', 'asc')
                ->paginate(50);

            $this->loggingService->logHttpRequest('chat_messages_retrieved', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $sessionId,
                'messages_count' => $messages->count(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $messages,
                'message' => 'Messages retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('chat_messages_retrieval_error', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a chat session
     */
    public function deleteSession(Request $request, string $sessionId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $session = WebSocketSession::where('id', $sessionId)
                ->where('tenant_id', $tenant->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat session not found'
                ], 404);
            }

            DB::beginTransaction();

            // Soft delete related chat history
            ChatHistory::where('session_id', $sessionId)->delete();

            // Delete the session
            $session->delete();

            // Clean up WebSocket session
            $this->webSocketService->cleanupSession($sessionId, $tenant->id);

            DB::commit();

            $this->loggingService->logHttpRequest('chat_session_deleted', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $sessionId,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Chat session deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            $this->loggingService->logHttpRequest('chat_session_deletion_error', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete chat session',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process MCP response for a user message
     */
    protected function processMCPResponse($tenant, $user, $session, $userMessage, bool $stream = false): void
    {
        try {
            // Process with MCP Orchestrator
            $mcpResponse = $this->mcpOrchestrator->processMessage(
                $tenant->id,
                $user->id,
                $session->id,
                $userMessage->content,
                [
                    'session_metadata' => $session->metadata,
                    'message_metadata' => $userMessage->metadata,
                    'stream' => $stream,
                ]
            );

            // Create AI response message
            $aiMessage = ChatHistory::create([
                'id' => Str::uuid(),
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $session->id,
                'message_type' => 'text',
                'role' => 'assistant',
                'content' => $mcpResponse['response'] ?? 'I apologize, but I encountered an error processing your message.',
                'metadata' => [
                    'mcp_processing_time' => $mcpResponse['processing_time'] ?? 0,
                    'mcp_operations' => $mcpResponse['operations'] ?? [],
                    'ai_provider' => $mcpResponse['ai_provider'] ?? 'unknown',
                    'model' => $mcpResponse['model'] ?? 'unknown',
                    'tokens_used' => $mcpResponse['tokens_used'] ?? 0,
                    'cost' => $mcpResponse['cost'] ?? 0,
                ],
                'processing_status' => 'completed',
                'ai_provider' => $mcpResponse['ai_provider'] ?? null,
                'model_used' => $mcpResponse['model'] ?? null,
                'tokens_used' => $mcpResponse['tokens_used'] ?? 0,
                'processing_time_ms' => $mcpResponse['processing_time'] ?? 0,
            ]);

            // Update session
            $session->update([
                'last_activity_at' => now(),
                'message_count' => $session->message_count + 1,
            ]);

            // Broadcast AI response via WebSocket
            broadcast(new ChatMessageEvent(
                $tenant->id,
                $session->id,
                'system',
                'AI Assistant',
                $aiMessage->content,
                'assistant',
                $aiMessage->id,
                $aiMessage->metadata
            ));

            $this->loggingService->logMCPOperation('chat_mcp_response_processed', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $session->id,
                'user_message_id' => $userMessage->id,
                'ai_message_id' => $aiMessage->id,
                'processing_time' => $mcpResponse['processing_time'] ?? 0,
                'tokens_used' => $mcpResponse['tokens_used'] ?? 0,
            ]);

        } catch (\Exception $e) {
            // Create error message
            $errorMessage = ChatHistory::create([
                'id' => Str::uuid(),
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $session->id,
                'message_type' => 'text',
                'role' => 'assistant',
                'content' => 'I apologize, but I encountered an error processing your message. Please try again.',
                'metadata' => [
                    'error' => $e->getMessage(),
                    'error_type' => 'mcp_processing_error',
                ],
                'processing_status' => 'error',
            ]);

            // Broadcast error message
            broadcast(new ChatMessageEvent(
                $tenant->id,
                $session->id,
                'system',
                'AI Assistant',
                $errorMessage->content,
                'assistant',
                $errorMessage->id,
                $errorMessage->metadata
            ));

            $this->loggingService->logMCPOperation('chat_mcp_response_error', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $session->id,
                'user_message_id' => $userMessage->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');
        }
    }
}
