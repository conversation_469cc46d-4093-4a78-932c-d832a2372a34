<?php

namespace App\Services\MCP;

use App\Models\Workflow;
use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;
use Illuminate\Support\Str;

class WorkflowService extends BaseService
{
    /**
     * Available step types
     */
    protected array $stepTypes = [
        'condition' => 'executeConditionStep',
        'action' => 'executeActionStep',
        'loop' => 'executeLoopStep',
        'parallel' => 'executeParallelStep',
        'delay' => 'executeDelayStep',
        'input' => 'executeInputStep',
        'output' => 'executeOutputStep',
        'transform' => 'executeTransformStep',
        'validation' => 'executeValidationStep',
        'notification' => 'executeNotificationStep',
    ];

    /**
     * Workflow execution context
     */
    protected array $executionContexts = [];

    /**
     * Step execution results
     */
    protected array $stepResults = [];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'workflow_service';
    }

    /**
     * Execute a workflow
     */
    public function executeWorkflow(string $workflowId, array $inputData = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('execute_workflow', function () use ($workflowId, $inputData) {
            // Get workflow configuration
            $workflow = $this->getWorkflowConfiguration($workflowId);

            // Validate workflow permissions
            $this->validateWorkflowPermissions($workflow);

            // Initialize execution context
            $executionId = $this->generateExecutionId();
            $context = $this->initializeExecutionContext($workflow, $inputData, $executionId);

            try {
                // Execute workflow steps
                $result = $this->executeWorkflowSteps($workflow, $context);

                // Log successful execution
                $this->logWorkflowExecution($executionId, $workflow, $inputData, $result, 'completed');

                return [
                    'execution_id' => $executionId,
                    'workflow_id' => $workflowId,
                    'status' => 'completed',
                    'result' => $result,
                    'steps_executed' => $context['steps_executed'] ?? [],
                    'execution_time_ms' => $context['execution_time_ms'] ?? 0,
                    'metadata' => $context['metadata'] ?? [],
                ];
            } catch (\Exception $e) {
                // Log failed execution
                $this->logWorkflowExecution($executionId, $workflow, $inputData, null, 'failed', $e->getMessage());

                // Attempt error recovery
                $recoveryResult = $this->attemptWorkflowRecovery($workflow, $context, $e);

                if ($recoveryResult) {
                    return $recoveryResult;
                }

                throw $e;
            }
        }, [
            'workflow_id' => $workflowId,
            'input_data_size' => strlen(json_encode($inputData)),
        ]);
    }

    /**
     * Create a new workflow
     */
    public function createWorkflow(array $workflowData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_workflows');

        return $this->executeWithTracking('create_workflow', function () use ($workflowData) {
            // Validate workflow structure
            $this->validateWorkflowStructure($workflowData);

            return $this->executeInTransaction(function () use ($workflowData) {
                $workflow = Workflow::create([
                    'tenant_id' => $this->getCurrentTenantId(),
                    'user_id' => $this->getCurrentUserId(),
                    'name' => $workflowData['name'],
                    'description' => $workflowData['description'],
                    'workflow_definition' => $workflowData['workflow_definition'],
                    'input_schema' => $workflowData['input_schema'] ?? [],
                    'output_schema' => $workflowData['output_schema'] ?? [],
                    'permissions_required' => $workflowData['permissions_required'] ?? [],
                    'timeout_seconds' => $workflowData['timeout_seconds'] ?? 300,
                    'retry_attempts' => $workflowData['retry_attempts'] ?? 3,
                    'is_enabled' => $workflowData['is_enabled'] ?? true,
                    'metadata' => $workflowData['metadata'] ?? [],
                ]);

                $this->logActivity('Workflow created', [
                    'workflow_id' => $workflow->id,
                    'workflow_name' => $workflow->name,
                    'steps_count' => count($workflowData['workflow_definition']['steps'] ?? []),
                ]);

                return [
                    'workflow_id' => $workflow->id,
                    'workflow_name' => $workflow->name,
                    'status' => 'created',
                ];
            });
        }, [
            'workflow_name' => $workflowData['name'],
            'steps_count' => count($workflowData['workflow_definition']['steps'] ?? []),
        ]);
    }

    /**
     * Update workflow
     */
    public function updateWorkflow(string $workflowId, array $updateData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_workflows');

        return $this->executeWithTracking('update_workflow', function () use ($workflowId, $updateData) {
            $workflow = Workflow::where('tenant_id', $this->getCurrentTenantId())
                              ->findOrFail($workflowId);

            // Validate updated workflow structure if provided
            if (isset($updateData['workflow_definition'])) {
                $this->validateWorkflowDefinition($updateData['workflow_definition']);
            }

            $workflow->update($updateData);

            $this->logActivity('Workflow updated', [
                'workflow_id' => $workflow->id,
                'workflow_name' => $workflow->name,
                'updated_fields' => array_keys($updateData),
            ]);

            return [
                'workflow_id' => $workflow->id,
                'workflow_name' => $workflow->name,
                'status' => 'updated',
            ];
        }, [
            'workflow_id' => $workflowId,
            'update_fields' => array_keys($updateData),
        ]);
    }

    /**
     * Get available workflows
     */
    public function getAvailableWorkflows(array $filters = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('get_available_workflows', function () use ($filters) {
            $query = Workflow::where('tenant_id', $this->getCurrentTenantId())
                            ->where('is_enabled', true);

            // Apply filters
            if (isset($filters['name'])) {
                $query->where('name', 'like', '%' . $filters['name'] . '%');
            }

            if (isset($filters['permissions'])) {
                foreach ($filters['permissions'] as $permission) {
                    $query->whereJsonContains('permissions_required', $permission);
                }
            }

            $workflows = $query->orderBy('name')->get();

            return [
                'workflows' => $workflows->map(function ($workflow) {
                    return [
                        'id' => $workflow->id,
                        'name' => $workflow->name,
                        'description' => $workflow->description,
                        'input_schema' => $workflow->input_schema,
                        'output_schema' => $workflow->output_schema,
                        'permissions_required' => $workflow->permissions_required,
                        'timeout_seconds' => $workflow->timeout_seconds,
                        'steps_count' => count($workflow->workflow_definition['steps'] ?? []),
                        'metadata' => $workflow->metadata,
                    ];
                })->toArray(),
                'total_count' => $workflows->count(),
                'step_types_available' => array_keys($this->stepTypes),
            ];
        }, [
            'filters_applied' => count($filters),
        ]);
    }

    /**
     * Get workflow execution history
     */
    public function getExecutionHistory(array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('get_execution_history', function () use ($options) {
            $dateFrom = $options['date_from'] ?? now()->subDays(7);
            $dateTo = $options['date_to'] ?? now();
            $limit = $options['limit'] ?? 100;

            // Get execution logs from system logs
            $executionLogs = $this->loggingService->getLogs([
                'tenant_id' => $this->getCurrentTenantId(),
                'log_type' => 'service',
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
            ], $limit);

            // Filter for workflow executions
            $workflowExecutions = array_filter($executionLogs, function ($log) {
                return isset($log['context']['workflow_execution']) && $log['context']['workflow_execution'] === true;
            });

            // Calculate statistics
            $totalExecutions = count($workflowExecutions);
            $completedExecutions = count(array_filter($workflowExecutions, fn($log) => $log['context']['status'] === 'completed'));
            $failedExecutions = $totalExecutions - $completedExecutions;

            // Group by workflow name
            $executionsByWorkflow = [];
            foreach ($workflowExecutions as $execution) {
                $workflowName = $execution['context']['workflow_name'] ?? 'unknown';
                $executionsByWorkflow[$workflowName] = ($executionsByWorkflow[$workflowName] ?? 0) + 1;
            }

            return [
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo,
                ],
                'statistics' => [
                    'total_executions' => $totalExecutions,
                    'completed_executions' => $completedExecutions,
                    'failed_executions' => $failedExecutions,
                    'success_rate' => $totalExecutions > 0 ? round($completedExecutions / $totalExecutions * 100, 2) : 0,
                ],
                'executions_by_workflow' => $executionsByWorkflow,
                'recent_executions' => array_slice($workflowExecutions, 0, 20),
            ];
        }, [
            'date_range_days' => $options['date_from'] ? now()->diffInDays($options['date_from']) : 7,
            'limit' => $options['limit'] ?? 100,
        ]);
    }

    /**
     * Test workflow
     */
    public function testWorkflow(string $workflowId, array $testInputData = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('test_workflows');

        return $this->executeWithTracking('test_workflow', function () use ($workflowId, $testInputData) {
            $workflow = Workflow::where('tenant_id', $this->getCurrentTenantId())
                              ->findOrFail($workflowId);

            // Use test input data or generate default test data
            $inputData = !empty($testInputData) ? $testInputData : $this->generateTestInputData($workflow);

            // Execute workflow in test mode
            $testContext = $this->initializeExecutionContext($workflow, $inputData, 'TEST_' . time());
            $testContext['test_mode'] = true;

            try {
                $result = $this->executeWorkflowSteps($workflow, $testContext);

                return [
                    'workflow_id' => $workflow->id,
                    'workflow_name' => $workflow->name,
                    'test_status' => 'passed',
                    'test_result' => $result,
                    'test_input_data' => $inputData,
                    'steps_executed' => $testContext['steps_executed'] ?? [],
                    'execution_time_ms' => $testContext['execution_time_ms'] ?? 0,
                ];
            } catch (\Exception $e) {
                return [
                    'workflow_id' => $workflow->id,
                    'workflow_name' => $workflow->name,
                    'test_status' => 'failed',
                    'error_message' => $e->getMessage(),
                    'test_input_data' => $inputData,
                    'steps_executed' => $testContext['steps_executed'] ?? [],
                ];
            }
        }, [
            'workflow_id' => $workflowId,
            'test_input_provided' => !empty($testInputData),
        ]);
    }

    /**
     * Get workflow configuration
     */
    protected function getWorkflowConfiguration(string $workflowId): Workflow
    {
        $workflow = Workflow::where('tenant_id', $this->getCurrentTenantId())
                           ->where('id', $workflowId)
                           ->where('is_enabled', true)
                           ->first();

        if (!$workflow) {
            throw new \InvalidArgumentException("Workflow not found or disabled: {$workflowId}");
        }

        return $workflow;
    }

    /**
     * Validate workflow permissions
     */
    protected function validateWorkflowPermissions(Workflow $workflow): void
    {
        $requiredPermissions = $workflow->permissions_required ?? [];

        foreach ($requiredPermissions as $permission) {
            if (!$this->hasPermission($permission)) {
                throw $this->errorHandlingService->createPermissionException($permission);
            }
        }
    }

    /**
     * Initialize execution context
     */
    protected function initializeExecutionContext(Workflow $workflow, array $inputData, string $executionId): array
    {
        return [
            'execution_id' => $executionId,
            'workflow' => $workflow,
            'input_data' => $inputData,
            'variables' => $inputData, // Variables can be modified during execution
            'tenant_id' => $this->getCurrentTenantId(),
            'user_id' => $this->getCurrentUserId(),
            'start_time' => microtime(true),
            'timeout' => $workflow->timeout_seconds,
            'retry_attempts' => $workflow->retry_attempts,
            'steps_executed' => [],
            'step_results' => [],
            'metadata' => [],
            'test_mode' => false,
            'current_step_index' => 0,
        ];
    }

    /**
     * Execute workflow steps
     */
    protected function executeWorkflowSteps(Workflow $workflow, array $context): array
    {
        $definition = $workflow->workflow_definition;
        $steps = $definition['steps'] ?? [];

        $startTime = microtime(true);

        try {
            // Execute steps sequentially or based on flow control
            $result = $this->executeStepSequence($steps, $context);

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $context['execution_time_ms'] = $executionTime;

            return [
                'workflow_result' => $result,
                'final_variables' => $context['variables'],
                'steps_executed' => $context['steps_executed'],
                'execution_summary' => [
                    'total_steps' => count($steps),
                    'executed_steps' => count($context['steps_executed']),
                    'execution_time_ms' => $executionTime,
                ],
            ];
        } catch (\Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $context['execution_time_ms'] = $executionTime;
            throw $e;
        }
    }

    /**
     * Execute step sequence
     */
    protected function executeStepSequence(array $steps, array &$context): array
    {
        $results = [];

        for ($i = 0; $i < count($steps); $i++) {
            $step = $steps[$i];
            $context['current_step_index'] = $i;

            // Check if step should be executed based on conditions
            if (!$this->shouldExecuteStep($step, $context)) {
                continue;
            }

            // Execute the step
            $stepResult = $this->executeStep($step, $context);

            // Store step result
            $context['steps_executed'][] = [
                'step_index' => $i,
                'step_name' => $step['name'] ?? "step_{$i}",
                'step_type' => $step['type'],
                'result' => $stepResult,
                'timestamp' => now()->toISOString(),
            ];

            $context['step_results'][$i] = $stepResult;
            $results[] = $stepResult;

            // Handle flow control
            $flowControl = $this->handleStepFlowControl($step, $stepResult, $context);
            if ($flowControl) {
                if ($flowControl['action'] === 'jump') {
                    $i = $flowControl['target_index'] - 1; // -1 because loop will increment
                } elseif ($flowControl['action'] === 'break') {
                    break;
                } elseif ($flowControl['action'] === 'continue') {
                    continue;
                }
            }
        }

        return $results;
    }

    /**
     * Check if step should be executed
     */
    protected function shouldExecuteStep(array $step, array $context): bool
    {
        if (!isset($step['condition'])) {
            return true;
        }

        return $this->evaluateCondition($step['condition'], $context);
    }

    /**
     * Execute individual step
     */
    protected function executeStep(array $step, array &$context): array
    {
        $stepType = $step['type'];
        $executionMethod = $this->stepTypes[$stepType] ?? null;

        if (!$executionMethod || !method_exists($this, $executionMethod)) {
            throw new \InvalidArgumentException("Unsupported step type: {$stepType}");
        }

        $stepStartTime = microtime(true);

        try {
            $result = $this->$executionMethod($step, $context);
            $executionTime = round((microtime(true) - $stepStartTime) * 1000, 2);

            return [
                'step_type' => $stepType,
                'step_name' => $step['name'] ?? "step_{$context['current_step_index']}",
                'result' => $result,
                'execution_time_ms' => $executionTime,
                'success' => true,
            ];
        } catch (\Exception $e) {
            $executionTime = round((microtime(true) - $stepStartTime) * 1000, 2);

            return [
                'step_type' => $stepType,
                'step_name' => $step['name'] ?? "step_{$context['current_step_index']}",
                'result' => null,
                'execution_time_ms' => $executionTime,
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Execute condition step
     */
    protected function executeConditionStep(array $step, array &$context): array
    {
        $condition = $step['condition'];
        $result = $this->evaluateCondition($condition, $context);

        return [
            'condition_result' => $result,
            'condition' => $condition,
        ];
    }

    /**
     * Execute action step
     */
    protected function executeActionStep(array $step, array &$context): array
    {
        $action = $step['action'];
        $parameters = $this->interpolateParameters($step['parameters'] ?? [], $context);

        switch ($action) {
            case 'set_variable':
                $context['variables'][$parameters['name']] = $parameters['value'];
                return ['variable_set' => $parameters['name'], 'value' => $parameters['value']];

            case 'call_api':
                return $this->callApi($parameters, $context);

            case 'send_notification':
                return $this->sendNotification($parameters, $context);

            default:
                throw new \InvalidArgumentException("Unsupported action: {$action}");
        }
    }

    /**
     * Execute loop step
     */
    protected function executeLoopStep(array $step, array &$context): array
    {
        $loopType = $step['loop_type']; // 'for', 'while', 'foreach'
        $loopSteps = $step['steps'] ?? [];
        $results = [];

        switch ($loopType) {
            case 'for':
                $iterations = $step['iterations'] ?? 1;
                for ($i = 0; $i < $iterations; $i++) {
                    $context['variables']['loop_index'] = $i;
                    $results[] = $this->executeStepSequence($loopSteps, $context);
                }
                break;

            case 'while':
                $condition = $step['condition'];
                $maxIterations = $step['max_iterations'] ?? 100;
                $iteration = 0;

                while ($this->evaluateCondition($condition, $context) && $iteration < $maxIterations) {
                    $context['variables']['loop_index'] = $iteration;
                    $results[] = $this->executeStepSequence($loopSteps, $context);
                    $iteration++;
                }
                break;

            case 'foreach':
                $array = $this->resolveVariable($step['array'], $context);
                if (is_array($array)) {
                    foreach ($array as $index => $item) {
                        $context['variables']['loop_index'] = $index;
                        $context['variables']['loop_item'] = $item;
                        $results[] = $this->executeStepSequence($loopSteps, $context);
                    }
                }
                break;
        }

        return [
            'loop_type' => $loopType,
            'iterations_completed' => count($results),
            'results' => $results,
        ];
    }

    /**
     * Execute parallel step
     */
    protected function executeParallelStep(array $step, array &$context): array
    {
        $parallelSteps = $step['parallel_steps'] ?? [];
        $results = [];

        // For now, execute sequentially (in production, this would use actual parallel processing)
        foreach ($parallelSteps as $parallelStep) {
            $results[] = $this->executeStep($parallelStep, $context);
        }

        return [
            'parallel_results' => $results,
            'steps_count' => count($parallelSteps),
        ];
    }

    /**
     * Execute delay step
     */
    protected function executeDelayStep(array $step, array &$context): array
    {
        $delaySeconds = $step['delay_seconds'] ?? 1;

        if (!$context['test_mode']) {
            sleep($delaySeconds);
        }

        return [
            'delay_seconds' => $delaySeconds,
            'executed_at' => now()->toISOString(),
        ];
    }

    /**
     * Execute input step
     */
    protected function executeInputStep(array $step, array &$context): array
    {
        $inputName = $step['input_name'];
        $defaultValue = $step['default_value'] ?? null;

        // In test mode or if input is provided in context
        $value = $context['variables'][$inputName] ?? $defaultValue;

        return [
            'input_name' => $inputName,
            'value' => $value,
            'source' => isset($context['variables'][$inputName]) ? 'provided' : 'default',
        ];
    }

    /**
     * Execute output step
     */
    protected function executeOutputStep(array $step, array &$context): array
    {
        $outputName = $step['output_name'];
        $value = $this->resolveVariable($step['value'], $context);

        $context['variables'][$outputName] = $value;

        return [
            'output_name' => $outputName,
            'value' => $value,
        ];
    }

    /**
     * Execute transform step
     */
    protected function executeTransformStep(array $step, array &$context): array
    {
        $transformType = $step['transform_type'];
        $inputValue = $this->resolveVariable($step['input'], $context);

        switch ($transformType) {
            case 'json_encode':
                $result = json_encode($inputValue);
                break;

            case 'json_decode':
                $result = json_decode($inputValue, true);
                break;

            case 'uppercase':
                $result = strtoupper($inputValue);
                break;

            case 'lowercase':
                $result = strtolower($inputValue);
                break;

            default:
                throw new \InvalidArgumentException("Unsupported transform type: {$transformType}");
        }

        if (isset($step['output_variable'])) {
            $context['variables'][$step['output_variable']] = $result;
        }

        return [
            'transform_type' => $transformType,
            'input_value' => $inputValue,
            'output_value' => $result,
        ];
    }

    /**
     * Execute validation step
     */
    protected function executeValidationStep(array $step, array &$context): array
    {
        $value = $this->resolveVariable($step['value'], $context);
        $validationType = $step['validation_type'];
        $validationRule = $step['validation_rule'] ?? null;

        $isValid = match ($validationType) {
            'required' => !empty($value),
            'email' => filter_var($value, FILTER_VALIDATE_EMAIL) !== false,
            'numeric' => is_numeric($value),
            'regex' => $validationRule ? preg_match($validationRule, $value) : false,
            default => true,
        };

        if (!$isValid && ($step['fail_on_invalid'] ?? true)) {
            throw new \Exception("Validation failed for {$validationType}");
        }

        return [
            'validation_type' => $validationType,
            'value' => $value,
            'is_valid' => $isValid,
        ];
    }

    /**
     * Execute notification step
     */
    protected function executeNotificationStep(array $step, array &$context): array
    {
        $notificationType = $step['notification_type'];
        $message = $this->interpolateString($step['message'], $context);

        // This would integrate with actual notification services
        return [
            'notification_type' => $notificationType,
            'message' => $message,
            'status' => 'sent',
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Evaluate condition
     */
    protected function evaluateCondition(array $condition, array $context): bool
    {
        $operator = $condition['operator'];
        $left = $this->resolveVariable($condition['left'], $context);
        $right = $this->resolveVariable($condition['right'], $context);

        return match ($operator) {
            '==' => $left == $right,
            '!=' => $left != $right,
            '>' => $left > $right,
            '<' => $left < $right,
            '>=' => $left >= $right,
            '<=' => $left <= $right,
            'contains' => str_contains($left, $right),
            'starts_with' => str_starts_with($left, $right),
            'ends_with' => str_ends_with($left, $right),
            default => false,
        };
    }

    /**
     * Resolve variable value
     */
    protected function resolveVariable($value, array $context)
    {
        if (is_string($value) && str_starts_with($value, '$')) {
            $variableName = substr($value, 1);
            return $context['variables'][$variableName] ?? null;
        }

        return $value;
    }

    /**
     * Interpolate parameters with context variables
     */
    protected function interpolateParameters(array $parameters, array $context): array
    {
        $interpolated = [];

        foreach ($parameters as $key => $value) {
            if (is_string($value)) {
                $interpolated[$key] = $this->interpolateString($value, $context);
            } elseif (is_array($value)) {
                $interpolated[$key] = $this->interpolateParameters($value, $context);
            } else {
                $interpolated[$key] = $value;
            }
        }

        return $interpolated;
    }

    /**
     * Interpolate string with context variables
     */
    protected function interpolateString(string $string, array $context): string
    {
        return preg_replace_callback('/\{\{(\w+)\}\}/', function ($matches) use ($context) {
            $variableName = $matches[1];
            return $context['variables'][$variableName] ?? $matches[0];
        }, $string);
    }

    /**
     * Handle step flow control
     */
    protected function handleStepFlowControl(array $step, array $stepResult, array $context): ?array
    {
        if (!isset($step['flow_control'])) {
            return null;
        }

        $flowControl = $step['flow_control'];
        $condition = $flowControl['condition'] ?? null;

        if ($condition && !$this->evaluateCondition($condition, $context)) {
            return null;
        }

        return $flowControl;
    }

    /**
     * Call API (placeholder implementation)
     */
    protected function callApi(array $parameters, array $context): array
    {
        // This would integrate with actual API calling logic
        return [
            'api_call' => 'mock',
            'url' => $parameters['url'] ?? 'unknown',
            'method' => $parameters['method'] ?? 'GET',
            'response' => 'Mock API response',
        ];
    }

    /**
     * Send notification (placeholder implementation)
     */
    protected function sendNotification(array $parameters, array $context): array
    {
        // This would integrate with actual notification services
        return [
            'notification_sent' => true,
            'type' => $parameters['type'] ?? 'email',
            'recipient' => $parameters['recipient'] ?? 'unknown',
            'message' => $parameters['message'] ?? 'No message',
        ];
    }

    /**
     * Validate workflow structure
     */
    protected function validateWorkflowStructure(array $workflowData): void
    {
        $requiredFields = ['name', 'workflow_definition'];
        foreach ($requiredFields as $field) {
            if (!isset($workflowData[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        $this->validateWorkflowDefinition($workflowData['workflow_definition']);
    }

    /**
     * Validate workflow definition
     */
    protected function validateWorkflowDefinition(array $definition): void
    {
        if (!isset($definition['steps']) || !is_array($definition['steps'])) {
            throw new \InvalidArgumentException("Workflow definition must contain 'steps' array");
        }

        foreach ($definition['steps'] as $index => $step) {
            $this->validateStep($step, $index);
        }
    }

    /**
     * Validate individual step
     */
    protected function validateStep(array $step, int $index): void
    {
        if (!isset($step['type'])) {
            throw new \InvalidArgumentException("Step {$index} missing 'type' field");
        }

        $stepType = $step['type'];
        if (!isset($this->stepTypes[$stepType])) {
            throw new \InvalidArgumentException("Unsupported step type: {$stepType}");
        }

        // Additional validation based on step type
        switch ($stepType) {
            case 'condition':
                if (!isset($step['condition'])) {
                    throw new \InvalidArgumentException("Condition step {$index} missing 'condition' field");
                }
                break;

            case 'action':
                if (!isset($step['action'])) {
                    throw new \InvalidArgumentException("Action step {$index} missing 'action' field");
                }
                break;

            case 'loop':
                if (!isset($step['loop_type'])) {
                    throw new \InvalidArgumentException("Loop step {$index} missing 'loop_type' field");
                }
                break;
        }
    }

    /**
     * Generate test input data
     */
    protected function generateTestInputData(Workflow $workflow): array
    {
        $inputSchema = $workflow->input_schema ?? [];
        $testData = [];

        foreach ($inputSchema as $fieldName => $fieldConfig) {
            $testData[$fieldName] = $fieldConfig['test_value'] ?? $this->generateTestValue($fieldConfig['type'] ?? 'string');
        }

        return $testData;
    }

    /**
     * Generate test value based on type
     */
    protected function generateTestValue(string $type): mixed
    {
        return match ($type) {
            'string' => 'test_value',
            'integer' => 42,
            'boolean' => true,
            'array' => ['test', 'array'],
            'object' => ['test' => 'object'],
            default => 'test_value',
        };
    }

    /**
     * Attempt workflow recovery
     */
    protected function attemptWorkflowRecovery(Workflow $workflow, array $context, \Exception $exception): ?array
    {
        $retryAttempts = $context['retry_attempts'] ?? 0;
        $currentAttempt = $context['current_attempt'] ?? 0;

        if ($currentAttempt < $retryAttempts) {
            $context['current_attempt'] = $currentAttempt + 1;

            // Wait before retry
            sleep(1);

            try {
                return $this->executeWorkflowSteps($workflow, $context);
            } catch (\Exception $retryException) {
                return null;
            }
        }

        return null;
    }

    /**
     * Log workflow execution
     */
    protected function logWorkflowExecution(string $executionId, Workflow $workflow, array $inputData, ?array $result, string $status, ?string $errorMessage = null): void
    {
        $this->logActivity('Workflow execution', [
            'workflow_execution' => true,
            'execution_id' => $executionId,
            'workflow_id' => $workflow->id,
            'workflow_name' => $workflow->name,
            'status' => $status,
            'input_data_size' => strlen(json_encode($inputData)),
            'result_size' => $result ? strlen(json_encode($result)) : 0,
            'steps_executed' => $result['steps_executed'] ?? [],
            'error_message' => $errorMessage,
        ], $status === 'failed' ? 'error' : 'info');
    }

    /**
     * Generate execution ID
     */
    protected function generateExecutionId(): string
    {
        return 'WORKFLOW_' . strtoupper(Str::random(8)) . '_' . time();
    }
}
