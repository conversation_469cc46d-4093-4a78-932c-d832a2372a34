<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use App\Models\Tenant;
use App\Services\Core\TenantContextService;
use Symfony\Component\HttpFoundation\Response;

class TenantContext
{
    protected TenantContextService $tenantContextService;

    public function __construct(TenantContextService $tenantContextService)
    {
        $this->tenantContextService = $tenantContextService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get tenant from various sources
        $tenant = $this->resolveTenant($request);

        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant not found or invalid',
                'message' => 'Please provide a valid tenant identifier'
            ], 400);
        }

        // Check if tenant is active
        if (!$tenant->is_active) {
            return response()->json([
                'error' => 'Tenant inactive',
                'message' => 'This tenant account is currently inactive'
            ], 403);
        }

        // Check subscription status
        if ($tenant->subscription_status !== 'active' && $tenant->subscription_status !== 'trial') {
            return response()->json([
                'error' => 'Subscription inactive',
                'message' => 'This tenant subscription is not active'
            ], 402);
        }

        // Set tenant context
        $this->tenantContextService->setTenant($tenant);

        // Set database connection with tenant isolation if needed
        $this->configureTenantDatabase($tenant);

        // Add tenant to request for easy access
        $request->merge(['tenant' => $tenant]);

        // Add tenant headers to response
        $response = $next($request);

        if ($response instanceof \Illuminate\Http\JsonResponse) {
            $response->header('X-Tenant-ID', $tenant->id);
            $response->header('X-Tenant-Name', $tenant->name);
        }

        return $response;
    }

    /**
     * Resolve tenant from request
     */
    protected function resolveTenant(Request $request): ?Tenant
    {
        // Try to get tenant from authenticated user first
        if (Auth::check()) {
            $user = Auth::user();
            if ($user && $user->tenant_id) {
                return Tenant::find($user->tenant_id);
            }
        }

        // Try to get tenant from header
        $tenantId = $request->header('X-Tenant-ID');
        if ($tenantId) {
            return Tenant::find($tenantId);
        }

        // Try to get tenant from subdomain
        $subdomain = $this->extractSubdomain($request);
        if ($subdomain) {
            return Tenant::where('subdomain', $subdomain)->first();
        }

        // Try to get tenant from query parameter
        $tenantSlug = $request->query('tenant');
        if ($tenantSlug) {
            return Tenant::where('slug', $tenantSlug)->first();
        }

        // Try to get tenant from API key
        $apiKey = $request->bearerToken() ?: $request->header('X-API-Key');
        if ($apiKey) {
            return $this->resolveTenantFromApiKey($apiKey);
        }

        return null;
    }

    /**
     * Extract subdomain from request
     */
    protected function extractSubdomain(Request $request): ?string
    {
        $host = $request->getHost();
        $parts = explode('.', $host);

        // If we have at least 3 parts (subdomain.domain.tld)
        if (count($parts) >= 3) {
            return $parts[0];
        }

        return null;
    }

    /**
     * Resolve tenant from API key
     */
    protected function resolveTenantFromApiKey(string $apiKey): ?Tenant
    {
        // Check if it's a Sanctum token
        if (str_starts_with($apiKey, config('sanctum.token_prefix', ''))) {
            $token = \Laravel\Sanctum\PersonalAccessToken::findToken($apiKey);
            if ($token && $token->tokenable) {
                return $token->tokenable->tenant;
            }
        }

        // Check if it's a tenant API key
        return Tenant::where('api_key', $apiKey)->first();
    }

    /**
     * Configure tenant-specific database settings
     */
    protected function configureTenantDatabase(Tenant $tenant): void
    {
        // Set tenant-specific database configuration if needed
        // This could be used for tenant-specific database connections

        // For now, we'll use the tenant_id for query scoping
        // which is handled in the models and services

        Config::set('tenant.current_id', $tenant->id);
        Config::set('tenant.current_name', $tenant->name);
        Config::set('tenant.current_settings', $tenant->settings);
    }
}
