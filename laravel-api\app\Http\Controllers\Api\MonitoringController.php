<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Core\LoggingService;
use App\Services\Core\TenantContextService;
use App\Services\WebSocket\WebSocketService;
use App\Services\MCP\MCPOrchestrator;
use App\Models\SystemLog;
use App\Models\WebSocketSession;
use App\Models\ChatHistory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class MonitoringController extends Controller
{
    protected LoggingService $loggingService;
    protected TenantContextService $tenantContext;
    protected WebSocketService $webSocketService;
    protected MCPOrchestrator $mcpOrchestrator;

    public function __construct(
        LoggingService $loggingService,
        TenantContextService $tenantContext,
        WebSocketService $webSocketService,
        MCPOrchestrator $mcpOrchestrator
    ) {
        $this->loggingService = $loggingService;
        $this->tenantContext = $tenantContext;
        $this->webSocketService = $webSocketService;
        $this->mcpOrchestrator = $mcpOrchestrator;
    }

    /**
     * Get system health status
     */
    public function health(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            // Check database connectivity
            $dbHealth = $this->checkDatabaseHealth();

            // Check cache connectivity
            $cacheHealth = $this->checkCacheHealth();

            // Check WebSocket service health
            $websocketHealth = $this->checkWebSocketHealth($tenant->id);

            // Check MCP service health
            $mcpHealth = $this->checkMCPHealth($tenant->id);

            // Check storage health
            $storageHealth = $this->checkStorageHealth();

            // Calculate overall health score
            $healthChecks = [$dbHealth, $cacheHealth, $websocketHealth, $mcpHealth, $storageHealth];
            $healthyCount = count(array_filter($healthChecks, fn($check) => $check['status'] === 'healthy'));
            $overallHealth = $healthyCount === count($healthChecks) ? 'healthy' :
                           ($healthyCount >= count($healthChecks) * 0.8 ? 'degraded' : 'unhealthy');

            $healthData = [
                'overall_status' => $overallHealth,
                'timestamp' => now()->toISOString(),
                'tenant_id' => $tenant->id,
                'checks' => [
                    'database' => $dbHealth,
                    'cache' => $cacheHealth,
                    'websocket' => $websocketHealth,
                    'mcp' => $mcpHealth,
                    'storage' => $storageHealth,
                ],
                'summary' => [
                    'total_checks' => count($healthChecks),
                    'healthy_checks' => $healthyCount,
                    'health_percentage' => round(($healthyCount / count($healthChecks)) * 100, 2),
                ],
            ];

            $this->loggingService->logSystemEvent('health_check_performed', [
                'tenant_id' => $tenant->id,
                'overall_status' => $overallHealth,
                'health_percentage' => $healthData['summary']['health_percentage'],
            ]);

            return response()->json([
                'success' => true,
                'data' => $healthData,
                'message' => 'System health check completed'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logSystemEvent('health_check_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Health check failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system logs with filtering
     */
    public function getLogs(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'level' => 'nullable|string|in:debug,info,warning,error,critical',
                'channel' => 'nullable|string|in:http,websocket,mcp,ai_provider,system',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'search' => 'nullable|string|max:255',
                'per_page' => 'nullable|integer|min:10|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();

            $query = SystemLog::where('tenant_id', $tenant->id);

            // Apply filters
            if ($request->filled('level')) {
                $query->where('level', $request->input('level'));
            }

            if ($request->filled('channel')) {
                $query->where('channel', $request->input('channel'));
            }

            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->input('start_date'));
            }

            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->input('end_date'));
            }

            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('message', 'LIKE', "%{$search}%")
                      ->orWhere('context', 'LIKE', "%{$search}%");
                });
            }

            $perPage = $request->input('per_page', 50);
            $logs = $query->orderBy('created_at', 'desc')->paginate($perPage);

            $this->loggingService->logSystemEvent('logs_retrieved', [
                'tenant_id' => $tenant->id,
                'filters' => $request->only(['level', 'channel', 'start_date', 'end_date', 'search']),
                'results_count' => $logs->count(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $logs,
                'message' => 'Logs retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logSystemEvent('logs_retrieval_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system metrics and statistics
     */
    public function getMetrics(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'period' => 'nullable|string|in:1h,6h,24h,7d,30d',
                'metrics' => 'nullable|array',
                'metrics.*' => 'string|in:requests,websocket,chat,mcp,errors,performance',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $period = $request->input('period', '24h');
            $requestedMetrics = $request->input('metrics', ['requests', 'websocket', 'chat', 'mcp', 'errors']);

            $startTime = $this->getPeriodStartTime($period);
            $metrics = [];

            // HTTP Request Metrics
            if (in_array('requests', $requestedMetrics)) {
                $metrics['requests'] = $this->getHttpRequestMetrics($tenant->id, $startTime);
            }

            // WebSocket Metrics
            if (in_array('websocket', $requestedMetrics)) {
                $metrics['websocket'] = $this->getWebSocketMetrics($tenant->id, $startTime);
            }

            // Chat Metrics
            if (in_array('chat', $requestedMetrics)) {
                $metrics['chat'] = $this->getChatMetrics($tenant->id, $startTime);
            }

            // MCP Metrics
            if (in_array('mcp', $requestedMetrics)) {
                $metrics['mcp'] = $this->getMCPMetrics($tenant->id, $startTime);
            }

            // Error Metrics
            if (in_array('errors', $requestedMetrics)) {
                $metrics['errors'] = $this->getErrorMetrics($tenant->id, $startTime);
            }

            // Performance Metrics
            if (in_array('performance', $requestedMetrics)) {
                $metrics['performance'] = $this->getPerformanceMetrics($tenant->id, $startTime);
            }

            $metricsData = [
                'tenant_id' => $tenant->id,
                'period' => $period,
                'start_time' => $startTime->toISOString(),
                'end_time' => now()->toISOString(),
                'metrics' => $metrics,
                'generated_at' => now()->toISOString(),
            ];

            $this->loggingService->logSystemEvent('metrics_retrieved', [
                'tenant_id' => $tenant->id,
                'period' => $period,
                'requested_metrics' => $requestedMetrics,
            ]);

            return response()->json([
                'success' => true,
                'data' => $metricsData,
                'message' => 'Metrics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logSystemEvent('metrics_retrieval_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve metrics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time system status
     */
    public function getRealtimeStatus(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            // Get current active connections
            $activeConnections = WebSocketSession::where('tenant_id', $tenant->id)
                ->where('status', 'active')
                ->count();

            // Get recent activity (last 5 minutes)
            $recentActivity = [
                'chat_messages' => ChatHistory::where('tenant_id', $tenant->id)
                    ->where('created_at', '>=', now()->subMinutes(5))
                    ->count(),
                'websocket_connections' => WebSocketSession::where('tenant_id', $tenant->id)
                    ->where('connected_at', '>=', now()->subMinutes(5))
                    ->count(),
                'system_errors' => SystemLog::where('tenant_id', $tenant->id)
                    ->where('level', 'error')
                    ->where('created_at', '>=', now()->subMinutes(5))
                    ->count(),
            ];

            // Get system resource usage
            $resourceUsage = [
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'cpu_load' => sys_getloadavg()[0] ?? 0,
                'disk_usage' => $this->getDiskUsage(),
            ];

            // Get cache statistics
            $cacheStats = $this->getCacheStatistics();

            $statusData = [
                'tenant_id' => $tenant->id,
                'timestamp' => now()->toISOString(),
                'active_connections' => $activeConnections,
                'recent_activity' => $recentActivity,
                'resource_usage' => $resourceUsage,
                'cache_statistics' => $cacheStats,
                'uptime' => $this->getSystemUptime(),
            ];

            return response()->json([
                'success' => true,
                'data' => $statusData,
                'message' => 'Real-time status retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logSystemEvent('realtime_status_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve real-time status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear system logs (admin only)
     */
    public function clearLogs(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'older_than_days' => 'nullable|integer|min:1|max:365',
                'level' => 'nullable|string|in:debug,info,warning,error,critical',
                'channel' => 'nullable|string|in:http,websocket,mcp,ai_provider,system',
                'confirm' => 'required|boolean|accepted',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $query = SystemLog::where('tenant_id', $tenant->id);

            // Apply filters
            if ($request->filled('older_than_days')) {
                $cutoffDate = now()->subDays($request->input('older_than_days'));
                $query->where('created_at', '<', $cutoffDate);
            }

            if ($request->filled('level')) {
                $query->where('level', $request->input('level'));
            }

            if ($request->filled('channel')) {
                $query->where('channel', $request->input('channel'));
            }

            $logsCount = $query->count();
            $deletedCount = $query->delete();

            $this->loggingService->logSystemEvent('logs_cleared', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'logs_count' => $logsCount,
                'deleted_count' => $deletedCount,
                'filters' => $request->only(['older_than_days', 'level', 'channel']),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'logs_count' => $logsCount,
                    'deleted_count' => $deletedCount,
                ],
                'message' => "Successfully cleared {$deletedCount} log entries"
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logSystemEvent('logs_clear_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to clear logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check database health
     */
    protected function checkDatabaseHealth(): array
    {
        try {
            $startTime = microtime(true);
            DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'message' => 'Database connection successful',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'response_time_ms' => null,
                'message' => 'Database connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache health
     */
    protected function checkCacheHealth(): array
    {
        try {
            $startTime = microtime(true);
            $testKey = 'health_check_' . time();
            Cache::put($testKey, 'test', 60);
            $value = Cache::get($testKey);
            Cache::forget($testKey);
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'status' => $value === 'test' ? 'healthy' : 'degraded',
                'response_time_ms' => $responseTime,
                'message' => $value === 'test' ? 'Cache working correctly' : 'Cache read/write issue',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'response_time_ms' => null,
                'message' => 'Cache connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check WebSocket service health
     */
    protected function checkWebSocketHealth(string $tenantId): array
    {
        try {
            $stats = $this->webSocketService->getConnectionStatistics($tenantId);
            return [
                'status' => 'healthy',
                'active_connections' => $stats['total_connections'] ?? 0,
                'message' => 'WebSocket service operational',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'active_connections' => 0,
                'message' => 'WebSocket service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check MCP service health
     */
    protected function checkMCPHealth(string $tenantId): array
    {
        try {
            // Test MCP orchestrator with a simple health check
            $healthCheck = $this->mcpOrchestrator->healthCheck($tenantId);
            return [
                'status' => $healthCheck['status'] ?? 'healthy',
                'services_active' => $healthCheck['services_active'] ?? 0,
                'message' => $healthCheck['message'] ?? 'MCP services operational',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'services_active' => 0,
                'message' => 'MCP service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage health
     */
    protected function checkStorageHealth(): array
    {
        try {
            $testFile = 'health_check_' . time() . '.txt';
            Storage::put($testFile, 'health check');
            $content = Storage::get($testFile);
            Storage::delete($testFile);

            return [
                'status' => $content === 'health check' ? 'healthy' : 'degraded',
                'message' => $content === 'health check' ? 'Storage working correctly' : 'Storage read/write issue',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Storage error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get period start time based on period string
     */
    protected function getPeriodStartTime(string $period): \Carbon\Carbon
    {
        return match ($period) {
            '1h' => now()->subHour(),
            '6h' => now()->subHours(6),
            '24h' => now()->subDay(),
            '7d' => now()->subWeek(),
            '30d' => now()->subMonth(),
            default => now()->subDay(),
        };
    }

    /**
     * Get HTTP request metrics
     */
    protected function getHttpRequestMetrics(string $tenantId, \Carbon\Carbon $startTime): array
    {
        $logs = SystemLog::where('tenant_id', $tenantId)
            ->where('channel', 'http')
            ->where('created_at', '>=', $startTime)
            ->get();

        return [
            'total_requests' => $logs->count(),
            'success_rate' => $logs->where('level', '!=', 'error')->count() / max($logs->count(), 1) * 100,
            'average_response_time' => $logs->avg('response_time_ms') ?? 0,
            'error_count' => $logs->where('level', 'error')->count(),
        ];
    }

    /**
     * Get WebSocket metrics
     */
    protected function getWebSocketMetrics(string $tenantId, \Carbon\Carbon $startTime): array
    {
        $sessions = WebSocketSession::where('tenant_id', $tenantId)
            ->where('created_at', '>=', $startTime)
            ->get();

        return [
            'total_connections' => $sessions->count(),
            'active_connections' => $sessions->where('status', 'active')->count(),
            'average_duration' => $sessions->avg('duration_seconds') ?? 0,
            'total_messages' => $sessions->sum('message_count'),
        ];
    }

    /**
     * Get chat metrics
     */
    protected function getChatMetrics(string $tenantId, \Carbon\Carbon $startTime): array
    {
        $messages = ChatHistory::where('tenant_id', $tenantId)
            ->where('created_at', '>=', $startTime)
            ->get();

        return [
            'total_messages' => $messages->count(),
            'user_messages' => $messages->where('role', 'user')->count(),
            'ai_messages' => $messages->where('role', 'assistant')->count(),
            'average_processing_time' => $messages->avg('processing_time_ms') ?? 0,
        ];
    }

    /**
     * Get MCP metrics
     */
    protected function getMCPMetrics(string $tenantId, \Carbon\Carbon $startTime): array
    {
        $logs = SystemLog::where('tenant_id', $tenantId)
            ->where('channel', 'mcp')
            ->where('created_at', '>=', $startTime)
            ->get();

        return [
            'total_operations' => $logs->count(),
            'success_rate' => $logs->where('level', '!=', 'error')->count() / max($logs->count(), 1) * 100,
            'average_processing_time' => $logs->avg('processing_time_ms') ?? 0,
            'error_count' => $logs->where('level', 'error')->count(),
        ];
    }

    /**
     * Get error metrics
     */
    protected function getErrorMetrics(string $tenantId, \Carbon\Carbon $startTime): array
    {
        $errors = SystemLog::where('tenant_id', $tenantId)
            ->where('level', 'error')
            ->where('created_at', '>=', $startTime)
            ->get();

        return [
            'total_errors' => $errors->count(),
            'error_rate' => $errors->count() / max(SystemLog::where('tenant_id', $tenantId)->where('created_at', '>=', $startTime)->count(), 1) * 100,
            'by_channel' => $errors->groupBy('channel')->map->count(),
            'recent_errors' => $errors->sortByDesc('created_at')->take(5)->values(),
        ];
    }

    /**
     * Get performance metrics
     */
    protected function getPerformanceMetrics(string $tenantId, \Carbon\Carbon $startTime): array
    {
        return [
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
            ],
            'cpu_load' => sys_getloadavg(),
            'disk_usage' => $this->getDiskUsage(),
            'cache_hit_rate' => $this->getCacheHitRate(),
        ];
    }

    /**
     * Get disk usage information
     */
    protected function getDiskUsage(): array
    {
        $bytes = disk_free_space('/');
        $total = disk_total_space('/');

        return [
            'free_bytes' => $bytes,
            'total_bytes' => $total,
            'used_bytes' => $total - $bytes,
            'usage_percentage' => round((($total - $bytes) / $total) * 100, 2),
        ];
    }

    /**
     * Get cache statistics
     */
    protected function getCacheStatistics(): array
    {
        try {
            return [
                'driver' => config('cache.default'),
                'status' => 'operational',
            ];
        } catch (\Exception $e) {
            return [
                'driver' => 'unknown',
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get cache hit rate
     */
    protected function getCacheHitRate(): float
    {
        // This would need to be implemented based on your cache driver
        // For now, return a placeholder
        return 85.5;
    }

    /**
     * Get system uptime
     */
    protected function getSystemUptime(): array
    {
        $uptime = file_get_contents('/proc/uptime');
        $uptimeSeconds = floatval(explode(' ', $uptime)[0]);

        return [
            'seconds' => $uptimeSeconds,
            'formatted' => gmdate('H:i:s', $uptimeSeconds),
            'days' => floor($uptimeSeconds / 86400),
        ];
    }
}
