<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatHistory extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $fillable = [
        'tenant_id',
        'user_id',
        'session_id',
        'message_type',
        'message_content',
        'ai_response',
        'ai_provider_id',
        'model_used',
        'tokens_used',
        'response_time_ms',
        'user_ip',
        'user_agent',
        'metadata'
    ];

    protected $casts = [
        'message_content' => 'array',
        'ai_response' => 'array',
        'tokens_used' => 'integer',
        'response_time_ms' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the chat history.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user that owns the chat history.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the AI provider used for this chat.
     */
    public function aiProvider(): BelongsTo
    {
        return $this->belongsTo(AIProvider::class);
    }
}
