<?php

namespace App\Listeners\WebSocket;

use App\Events\WebSocket\TypingIndicatorEvent;
use App\Services\Core\LoggingService;
use App\Services\WebSocket\ChannelManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;

class TypingIndicatorListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected LoggingService $loggingService;
    protected ChannelManager $channelManager;

    /**
     * Create the event listener.
     */
    public function __construct(
        LoggingService $loggingService,
        ChannelManager $channelManager
    ) {
        $this->loggingService = $loggingService;
        $this->channelManager = $channelManager;
    }

    /**
     * Handle the event.
     */
    public function handle(TypingIndicatorEvent $event): void
    {
        try {
            // Log the typing indicator event
            $this->loggingService->logWebSocketEvent('typing_indicator_processed', [
                'tenant_id' => $event->tenantId,
                'session_id' => $event->sessionId,
                'user_id' => $event->userId,
                'is_typing' => $event->isTyping,
                'timestamp' => $event->timestamp,
            ]);

            // Update typing state
            $this->updateTypingState($event);

            // Update user activity in channel
            $this->updateChannelActivity($event);

            // Clean up expired typing indicators
            $this->cleanupExpiredTypingIndicators($event);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('typing_indicator_listener_error', [
                'tenant_id' => $event->tenantId,
                'session_id' => $event->sessionId,
                'user_id' => $event->userId,
                'error' => $e->getMessage(),
            ], 'error');

            // Don't re-throw for typing indicators as they're not critical
            // and should not cause queue failures
        }
    }

    /**
     * Update typing state
     */
    protected function updateTypingState(TypingIndicatorEvent $event): void
    {
        $typingKey = "typing:{$event->tenantId}:{$event->sessionId}";
        $typingUsers = Cache::get($typingKey, []);

        if ($event->isTyping) {
            // Add user to typing list
            $typingUsers[$event->userId] = [
                'user_id' => $event->userId,
                'user_name' => $event->userName,
                'started_typing_at' => $event->timestamp,
                'metadata' => $event->metadata,
            ];

            // Cache for 30 seconds (typing indicators expire quickly)
            Cache::put($typingKey, $typingUsers, 30);

            $this->loggingService->logWebSocketEvent('user_started_typing', [
                'tenant_id' => $event->tenantId,
                'session_id' => $event->sessionId,
                'user_id' => $event->userId,
                'user_name' => $event->userName,
            ]);
        } else {
            // Remove user from typing list
            unset($typingUsers[$event->userId]);

            if (empty($typingUsers)) {
                Cache::forget($typingKey);
            } else {
                Cache::put($typingKey, $typingUsers, 30);
            }

            $this->loggingService->logWebSocketEvent('user_stopped_typing', [
                'tenant_id' => $event->tenantId,
                'session_id' => $event->sessionId,
                'user_id' => $event->userId,
                'user_name' => $event->userName,
            ]);
        }

        // Update session-wide typing count
        $this->updateSessionTypingCount($event, count($typingUsers));
    }

    /**
     * Update session typing count
     */
    protected function updateSessionTypingCount(TypingIndicatorEvent $event, int $typingCount): void
    {
        $sessionTypingKey = "session_typing_count:{$event->tenantId}:{$event->sessionId}";

        if ($typingCount > 0) {
            Cache::put($sessionTypingKey, $typingCount, 30);
        } else {
            Cache::forget($sessionTypingKey);
        }
    }

    /**
     * Update channel activity
     */
    protected function updateChannelActivity(TypingIndicatorEvent $event): void
    {
        // Update user activity in the chat channel
        $chatChannel = "private-chat.{$event->tenantId}.{$event->sessionId}";

        $this->channelManager->updateUserActivity(
            $event->tenantId,
            $event->userId,
            $chatChannel
        );
    }

    /**
     * Clean up expired typing indicators
     */
    protected function cleanupExpiredTypingIndicators(TypingIndicatorEvent $event): void
    {
        $typingKey = "typing:{$event->tenantId}:{$event->sessionId}";
        $typingUsers = Cache::get($typingKey, []);

        if (empty($typingUsers)) {
            return;
        }

        $now = now();
        $expiredUsers = [];
        $activeUsers = [];

        foreach ($typingUsers as $userId => $typingData) {
            $startedAt = \Carbon\Carbon::parse($typingData['started_typing_at']);

            // Typing indicators expire after 10 seconds of inactivity
            if ($now->diffInSeconds($startedAt) > 10) {
                $expiredUsers[] = $userId;
            } else {
                $activeUsers[$userId] = $typingData;
            }
        }

        // Update cache with only active users
        if (empty($activeUsers)) {
            Cache::forget($typingKey);
        } else {
            Cache::put($typingKey, $activeUsers, 30);
        }

        // Log cleanup if any users were expired
        if (!empty($expiredUsers)) {
            $this->loggingService->logWebSocketEvent('typing_indicators_expired', [
                'tenant_id' => $event->tenantId,
                'session_id' => $event->sessionId,
                'expired_users' => $expiredUsers,
                'active_users_count' => count($activeUsers),
            ]);
        }
    }

    /**
     * Get current typing users for a session
     */
    public function getTypingUsers(string $tenantId, string $sessionId): array
    {
        $typingKey = "typing:{$tenantId}:{$sessionId}";
        return Cache::get($typingKey, []);
    }

    /**
     * Check if user is currently typing
     */
    public function isUserTyping(string $tenantId, string $sessionId, string $userId): bool
    {
        $typingUsers = $this->getTypingUsers($tenantId, $sessionId);
        return isset($typingUsers[$userId]);
    }

    /**
     * Force stop typing for a user (useful for cleanup)
     */
    public function forceStopTyping(string $tenantId, string $sessionId, string $userId): bool
    {
        $typingKey = "typing:{$tenantId}:{$sessionId}";
        $typingUsers = Cache::get($typingKey, []);

        if (isset($typingUsers[$userId])) {
            unset($typingUsers[$userId]);

            if (empty($typingUsers)) {
                Cache::forget($typingKey);
            } else {
                Cache::put($typingKey, $typingUsers, 30);
            }

            $this->loggingService->logWebSocketEvent('typing_force_stopped', [
                'tenant_id' => $tenantId,
                'session_id' => $sessionId,
                'user_id' => $userId,
            ]);

            return true;
        }

        return false;
    }

    /**
     * Get typing statistics for a tenant
     */
    public function getTypingStatistics(string $tenantId): array
    {
        // This would scan all typing indicators for the tenant
        // For performance, we'll implement a simplified version

        $stats = [
            'tenant_id' => $tenantId,
            'total_active_sessions_with_typing' => 0,
            'total_users_typing' => 0,
            'sessions_with_typing' => [],
        ];

        // In a real implementation, you'd scan Redis keys or maintain a tenant-wide index
        // For now, return basic structure

        return $stats;
    }

    /**
     * Handle a job failure.
     */
    public function failed(TypingIndicatorEvent $event, \Throwable $exception): void
    {
        $this->loggingService->logWebSocketEvent('typing_indicator_listener_failed', [
            'tenant_id' => $event->tenantId,
            'session_id' => $event->sessionId,
            'user_id' => $event->userId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');
    }
}
