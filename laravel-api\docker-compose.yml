version: '3.8'

services:
  # Laravel Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: axient_mcp_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./storage:/var/www/storage
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
    depends_on:
      - postgres
      - redis
    networks:
      - axient_network

  # PostgreSQL with pgvector extension
  postgres:
    image: pgvector/pgvector:pg16
    container_name: axient_mcp_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: axient_mcp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - axient_network

  # Redis for caching, queues, and WebSocket sessions
  redis:
    image: redis:7-alpine
    container_name: axient_mcp_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - axient_network

  # WebSocket Server (Laravel Echo Server)
  websocket:
    image: node:18-alpine
    container_name: axient_mcp_websocket
    restart: unless-stopped
    working_dir: /app
    volumes:
      - ./websocket:/app
    ports:
      - "6001:6001"
    command: npm start
    depends_on:
      - redis
    networks:
      - axient_network

  # Nginx (Optional - for production)
  nginx:
    image: nginx:alpine
    container_name: axient_mcp_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites/:/etc/nginx/sites-available
      - ./docker/nginx/ssl/:/etc/ssl/certs
    depends_on:
      - app
    networks:
      - axient_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  axient_network:
    driver: bridge
