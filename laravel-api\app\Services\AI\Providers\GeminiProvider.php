<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class GeminiProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            $response = Http::timeout(10)->get($this->baseUrl . '/models', [
                'key' => $this->apiKey
            ]);

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'Gemini API connection successful',
                    'models_count' => count($response->json('models', [])),
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'Gemini API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Gemini API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        try {
            $response = Http::get($this->baseUrl . '/models', [
                'key' => $this->apiKey
            ]);

            if ($response->successful()) {
                $models = $response->json('models', []);
                return array_map(function ($model) {
                    $modelId = str_replace('models/', '', $model['name']);
                    return [
                        'id' => $modelId,
                        'name' => $model['displayName'] ?? $modelId,
                        'description' => $model['description'] ?? $this->getModelDescription($modelId),
                        'capabilities' => $this->getModelCapabilities($modelId),
                        'context_length' => $this->getModelContextLength($modelId),
                        'cost_per_token' => $this->getModelCostPerToken($modelId),
                    ];
                }, $models);
            }

            return $this->getDefaultModels();

        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('gemini_models_fetch_error', [
                'error' => $e->getMessage(),
            ], 'error');
            return $this->getDefaultModels();
        }
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'gemini-pro';

        try {
            $payload = [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $prompt]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => $options['temperature'] ?? 0.7,
                    'maxOutputTokens' => $options['max_tokens'] ?? 1000,
                ]
            ];

            if (isset($options['stop_sequences'])) {
                $payload['generationConfig']['stopSequences'] = $options['stop_sequences'];
            }

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . "/models/{$model}:generateContent", array_merge($payload, [
                'key' => $this->apiKey
            ]));

            if ($response->successful()) {
                $data = $response->json();

                $content = '';
                if (isset($data['candidates'][0]['content']['parts'])) {
                    foreach ($data['candidates'][0]['content']['parts'] as $part) {
                        $content .= $part['text'] ?? '';
                    }
                }

                return [
                    'success' => true,
                    'content' => $content,
                    'model' => $model,
                    'usage' => $data['usageMetadata'] ?? [],
                    'cost' => $this->calculateCost($data['usageMetadata'] ?? []),
                    'finish_reason' => $data['candidates'][0]['finishReason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Gemini API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'gemini-pro';

        try {
            // Convert messages to Gemini format
            $contents = [];
            foreach ($messages as $message) {
                $role = $message['role'] === 'assistant' ? 'model' : 'user';
                $contents[] = [
                    'role' => $role,
                    'parts' => [
                        ['text' => $message['content']]
                    ]
                ];
            }

            $payload = [
                'contents' => $contents,
                'generationConfig' => [
                    'temperature' => $options['temperature'] ?? 0.7,
                    'maxOutputTokens' => $options['max_tokens'] ?? 1000,
                ]
            ];

            if (isset($options['system'])) {
                $payload['systemInstruction'] = [
                    'parts' => [['text' => $options['system']]]
                ];
            }

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . "/models/{$model}:generateContent", array_merge($payload, [
                'key' => $this->apiKey
            ]));

            if ($response->successful()) {
                $data = $response->json();

                $content = '';
                if (isset($data['candidates'][0]['content']['parts'])) {
                    foreach ($data['candidates'][0]['content']['parts'] as $part) {
                        $content .= $part['text'] ?? '';
                    }
                }

                return [
                    'success' => true,
                    'content' => $content,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $content,
                    ],
                    'model' => $model,
                    'usage' => $data['usageMetadata'] ?? [],
                    'cost' => $this->calculateCost($data['usageMetadata'] ?? []),
                    'finish_reason' => $data['candidates'][0]['finishReason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Gemini API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        $model = $options['model'] ?? 'embedding-001';

        try {
            $embeddings = [];

            foreach ($texts as $text) {
                $response = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->timeout(60)->post($this->baseUrl . "/models/{$model}:embedContent", [
                    'content' => [
                        'parts' => [
                            ['text' => $text]
                        ]
                    ],
                    'key' => $this->apiKey
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    $embeddings[] = $data['embedding']['values'] ?? [];
                } else {
                    throw new \Exception('Gemini embedding error: ' . $response->body());
                }
            }

            return [
                'success' => true,
                'embeddings' => $embeddings,
                'model' => $model,
                'usage' => ['total_tokens' => array_sum(array_map('str_word_count', $texts))],
                'cost' => $this->calculateCost(['total_tokens' => array_sum(array_map('str_word_count', $texts))]),
            ];

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'multimodal',
            'vision',
            'function_calling',
            'safety_settings',
        ];
    }

    public function getName(): string
    {
        return 'gemini';
    }

    public function getDisplayName(): string
    {
        return 'Google Gemini';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 60,
            'tokens_per_minute' => 32000,
            'requests_per_day' => 1500,
        ];
    }

    public function calculateCost(array $usage): float
    {
        $inputTokens = $usage['promptTokenCount'] ?? $usage['total_tokens'] ?? 0;
        $outputTokens = $usage['candidatesTokenCount'] ?? 0;

        // Gemini Pro pricing
        $inputCost = $inputTokens * 0.0000005; // $0.50 per 1M tokens
        $outputCost = $outputTokens * 0.0000015; // $1.50 per 1M tokens

        return $inputCost + $outputCost;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('gemini_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'gemini',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'gemini-pro',
                'name' => 'Gemini Pro',
                'description' => 'Best model for text-only prompts',
                'capabilities' => ['text_generation', 'chat', 'function_calling'],
                'context_length' => 30720,
                'cost_per_token' => 0.0000005,
            ],
            [
                'id' => 'gemini-pro-vision',
                'name' => 'Gemini Pro Vision',
                'description' => 'Best model for text and image prompts',
                'capabilities' => ['text_generation', 'chat', 'multimodal', 'vision'],
                'context_length' => 12288,
                'cost_per_token' => 0.00000025,
            ],
            [
                'id' => 'gemini-ultra',
                'name' => 'Gemini Ultra',
                'description' => 'Most capable model for complex tasks',
                'capabilities' => ['text_generation', 'chat', 'multimodal', 'function_calling'],
                'context_length' => 30720,
                'cost_per_token' => 0.000001,
            ],
            [
                'id' => 'embedding-001',
                'name' => 'Text Embedding 001',
                'description' => 'Text embedding model',
                'capabilities' => ['embeddings'],
                'context_length' => 2048,
                'cost_per_token' => 0.0000001,
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $descriptions = [
            'gemini-pro' => 'Best model for text-only prompts',
            'gemini-pro-vision' => 'Best model for text and image prompts',
            'gemini-ultra' => 'Most capable model for complex tasks',
            'embedding-001' => 'Text embedding model',
        ];

        return $descriptions[$modelId] ?? 'Gemini model';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        if (str_contains($modelId, 'embedding')) {
            return ['embeddings'];
        }

        if (str_contains($modelId, 'vision')) {
            return ['text_generation', 'chat', 'multimodal', 'vision'];
        }

        return ['text_generation', 'chat', 'function_calling'];
    }

    protected function getModelContextLength(string $modelId): int
    {
        $contextLengths = [
            'gemini-pro' => 30720,
            'gemini-pro-vision' => 12288,
            'gemini-ultra' => 30720,
            'embedding-001' => 2048,
        ];

        return $contextLengths[$modelId] ?? 30720;
    }

    protected function getModelCostPerToken(string $modelId): float
    {
        $costs = [
            'gemini-pro' => 0.0000005,
            'gemini-pro-vision' => 0.00000025,
            'gemini-ultra' => 0.000001,
            'embedding-001' => 0.0000001,
        ];

        return $costs[$modelId] ?? 0.0000005;
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'multimodal',
            'vision',
            'function_calling',
            'safety_settings',
        ];
    }
}
