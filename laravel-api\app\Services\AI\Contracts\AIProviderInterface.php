<?php

namespace App\Services\AI\Contracts;

interface AIProviderInterface
{
    /**
     * Initialize the provider with configuration
     */
    public function initialize(array $config): bool;

    /**
     * Test the provider connection
     */
    public function testConnection(): array;

    /**
     * Get available models for this provider
     */
    public function getAvailableModels(): array;

    /**
     * Generate text completion
     */
    public function generateCompletion(
        string $prompt,
        array $options = [],
        bool $stream = false
    ): array;

    /**
     * Generate chat completion
     */
    public function generateChatCompletion(
        array $messages,
        array $options = [],
        bool $stream = false
    ): array;

    /**
     * Generate embeddings (if supported)
     */
    public function generateEmbeddings(array $texts, array $options = []): array;

    /**
     * Get provider capabilities
     */
    public function getCapabilities(): array;

    /**
     * Get provider name
     */
    public function getName(): string;

    /**
     * Get provider display name
     */
    public function getDisplayName(): string;

    /**
     * Get rate limits for this provider
     */
    public function getRateLimits(): array;

    /**
     * Calculate cost for a request
     */
    public function calculateCost(array $usage): float;

    /**
     * Get provider health status
     */
    public function getHealthStatus(): array;

    /**
     * Handle provider-specific errors
     */
    public function handleError(\Exception $exception): array;
}
