<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tools', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id');
            $table->uuid('created_by_user_id');

            // Tool Information
            $table->string('name');
            $table->string('display_name');
            $table->text('description');
            $table->string('version')->default('1.0.0');
            $table->string('category'); // api, database, file, web_scraping, etc.
            $table->json('tags')->nullable();

            // Tool Type & Implementation
            $table->enum('tool_type', [
                'api_call', 'database_query', 'file_operation', 'web_scraping',
                'data_processing', 'external_service', 'custom_function', 'webhook'
            ]);
            $table->enum('implementation_type', ['built_in', 'custom', 'plugin', 'external'])->default('built_in');
            $table->string('implementation_class')->nullable(); // PHP class for custom tools
            $table->text('implementation_code')->nullable(); // Code for custom tools

            // Configuration
            $table->json('configuration'); // Tool-specific configuration
            $table->json('parameters_schema'); // JSON schema for parameters
            $table->json('response_schema')->nullable(); // Expected response format
            $table->json('default_parameters')->nullable();
            $table->boolean('requires_authentication')->default(false);
            $table->json('authentication_config')->nullable();

            // API Configuration (for API tools)
            $table->string('api_endpoint')->nullable();
            $table->enum('http_method', ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])->nullable();
            $table->json('headers')->nullable();
            $table->integer('timeout_seconds')->default(30);
            $table->integer('retry_attempts')->default(3);
            $table->json('rate_limiting')->nullable();

            // Execution Settings
            $table->boolean('is_async')->default(false);
            $table->integer('max_execution_time_seconds')->default(300);
            $table->integer('memory_limit_mb')->default(128);
            $table->boolean('requires_approval')->default(false);
            $table->json('approval_rules')->nullable();

            // Access Control
            $table->enum('visibility', ['public', 'private', 'team'])->default('private');
            $table->json('allowed_user_roles')->nullable();
            $table->json('restricted_parameters')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);

            // Usage Tracking
            $table->integer('execution_count')->default(0);
            $table->integer('success_count')->default(0);
            $table->integer('error_count')->default(0);
            $table->decimal('success_rate_percentage', 5, 2)->default(100);
            $table->timestamp('last_executed_at')->nullable();
            $table->uuid('last_executed_by_user_id')->nullable();

            // Performance Metrics
            $table->decimal('average_execution_time_ms', 10, 2)->nullable();
            $table->decimal('min_execution_time_ms', 10, 2)->nullable();
            $table->decimal('max_execution_time_ms', 10, 2)->nullable();
            $table->decimal('average_memory_usage_mb', 10, 2)->nullable();
            $table->json('performance_history')->nullable(); // Last 100 executions

            // Error Handling
            $table->text('last_error_message')->nullable();
            $table->timestamp('last_error_at')->nullable();
            $table->json('common_errors')->nullable(); // Frequent error patterns
            $table->json('error_handling_config')->nullable();

            // Dependencies & Requirements
            $table->json('dependencies')->nullable(); // Other tools this depends on
            $table->json('required_permissions')->nullable();
            $table->json('required_environment')->nullable(); // Environment variables, etc.
            $table->string('minimum_php_version')->nullable();
            $table->json('required_extensions')->nullable();

            // Documentation
            $table->longText('documentation')->nullable(); // Markdown documentation
            $table->json('examples')->nullable(); // Usage examples
            $table->string('documentation_url')->nullable();
            $table->json('changelog')->nullable();

            // Integration & Webhooks
            $table->string('webhook_url')->nullable();
            $table->string('webhook_secret')->nullable();
            $table->json('webhook_events')->nullable(); // Which events to send
            $table->boolean('supports_streaming')->default(false);
            $table->json('streaming_config')->nullable();

            // Validation & Testing
            $table->json('validation_rules')->nullable();
            $table->json('test_cases')->nullable();
            $table->timestamp('last_tested_at')->nullable();
            $table->boolean('test_passed')->nullable();
            $table->json('test_results')->nullable();

            // Versioning & Updates
            $table->uuid('parent_tool_id')->nullable(); // For tool versions
            $table->boolean('is_latest_version')->default(true);
            $table->text('version_notes')->nullable();
            $table->boolean('auto_update_enabled')->default(false);
            $table->string('update_source')->nullable(); // URL or repository

            // Monitoring & Alerts
            $table->boolean('monitoring_enabled')->default(true);
            $table->json('alert_thresholds')->nullable(); // Error rates, response times
            $table->json('notification_channels')->nullable();
            $table->boolean('health_check_enabled')->default(true);
            $table->string('health_check_endpoint')->nullable();

            // Compliance & Security
            $table->boolean('handles_sensitive_data')->default(false);
            $table->json('compliance_requirements')->nullable();
            $table->json('security_settings')->nullable();
            $table->boolean('audit_logging_enabled')->default(true);

            // Status & Metadata
            $table->enum('status', ['active', 'inactive', 'deprecated', 'maintenance'])->default('active');
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('created_by_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('last_executed_by_user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parent_tool_id')->references('id')->on('tools')->onDelete('set null');

            // Indexes
            $table->unique(['tenant_id', 'name']);
            $table->index(['tenant_id', 'category']);
            $table->index(['tenant_id', 'tool_type']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['tenant_id', 'is_featured']);
            $table->index(['visibility', 'is_active']);
            $table->index('last_executed_at');
            $table->index(['success_rate_percentage', 'execution_count']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tools');
    }
};
