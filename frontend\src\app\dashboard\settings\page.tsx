'use client';

import { useState, useEffect } from 'react';
import {
    Cog6ToothIcon,
    UserIcon,
    BellIcon,
    ShieldCheckIcon,
    CreditCardIcon,
    GlobeAltIcon,
    KeyIcon,
    DocumentTextIcon,
    ChartBarIcon,
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

interface Settings {
    profile: {
        name: string;
        email: string;
        phone: string;
        timezone: string;
        language: string;
        avatar?: string;
    };
    notifications: {
        emailNotifications: boolean;
        pushNotifications: boolean;
        workflowAlerts: boolean;
        securityAlerts: boolean;
        weeklyReports: boolean;
    };
    security: {
        twoFactorEnabled: boolean;
        sessionTimeout: number;
        ipWhitelist: string[];
        apiKeyRotation: number;
    };
    billing: {
        plan: string;
        usage: {
            requests: number;
            tokens: number;
            storage: number;
        };
        limits: {
            requests: number;
            tokens: number;
            storage: number;
        };
    };
    integrations: {
        webhookUrl: string;
        slackEnabled: boolean;
        teamsEnabled: boolean;
        discordEnabled: boolean;
    };
}

export default function SettingsPage() {
    const [settings, setSettings] = useState<Settings | null>(null);
    const [activeTab, setActiveTab] = useState('profile');
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);

    const tabs = [
        { id: 'profile', name: 'Profile', icon: UserIcon },
        { id: 'notifications', name: 'Notifications', icon: BellIcon },
        { id: 'security', name: 'Security', icon: ShieldCheckIcon },
        { id: 'billing', name: 'Billing', icon: CreditCardIcon },
        { id: 'integrations', name: 'Integrations', icon: GlobeAltIcon },
    ];

    useEffect(() => {
        // Load settings
        const loadSettings = async () => {
            try {
                // TODO: Replace with actual API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                setSettings({
                    profile: {
                        name: 'John Smith',
                        email: '<EMAIL>',
                        phone: '+****************',
                        timezone: 'America/New_York',
                        language: 'en',
                    },
                    notifications: {
                        emailNotifications: true,
                        pushNotifications: true,
                        workflowAlerts: true,
                        securityAlerts: true,
                        weeklyReports: false,
                    },
                    security: {
                        twoFactorEnabled: false,
                        sessionTimeout: 30,
                        ipWhitelist: ['***********/24'],
                        apiKeyRotation: 90,
                    },
                    billing: {
                        plan: 'Professional',
                        usage: {
                            requests: 12450,
                            tokens: 2847392,
                            storage: 5.2,
                        },
                        limits: {
                            requests: 50000,
                            tokens: 10000000,
                            storage: 100,
                        },
                    },
                    integrations: {
                        webhookUrl: 'https://api.company.com/webhooks/ai',
                        slackEnabled: true,
                        teamsEnabled: false,
                        discordEnabled: false,
                    },
                });
            } catch (error) {
                console.error('Error loading settings:', error);
            } finally {
                setLoading(false);
            }
        };

        loadSettings();
    }, []);

    const handleSave = async () => {
        setSaving(true);
        try {
            // TODO: Replace with actual API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('Settings saved:', settings);
        } catch (error) {
            console.error('Error saving settings:', error);
        } finally {
            setSaving(false);
        }
    };

    const updateSettings = (section: keyof Settings, field: string, value: any) => {
        if (!settings) return;
        
        setSettings({
            ...settings,
            [section]: {
                ...settings[section],
                [field]: value,
            },
        });
    };

    const formatBytes = (bytes: number) => {
        if (bytes === 0) return '0 GB';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes * 1024 * 1024 * 1024) / Math.log(k));
        return parseFloat(((bytes * 1024 * 1024 * 1024) / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    if (loading || !settings) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="h-64 bg-gray-200 rounded"></div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
                    <p className="mt-1 text-sm text-gray-500">
                        Manage your account settings and preferences.
                    </p>
                </div>
                <button
                    onClick={handleSave}
                    disabled={saving}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                    {saving ? 'Saving...' : 'Save Changes'}
                </button>
            </div>

            <div className="flex flex-col lg:flex-row gap-6">
                {/* Sidebar */}
                <div className="lg:w-64">
                    <nav className="space-y-1">
                        {tabs.map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                                        activeTab === tab.id
                                            ? 'bg-blue-100 text-blue-700'
                                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                                    }`}
                                >
                                    <Icon className="h-5 w-5 mr-3" />
                                    {tab.name}
                                </button>
                            );
                        })}
                    </nav>
                </div>

                {/* Content */}
                <div className="flex-1">
                    {activeTab === 'profile' && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Profile Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Name</label>
                                        <input
                                            type="text"
                                            value={settings.profile.name}
                                            onChange={(e) => updateSettings('profile', 'name', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Email</label>
                                        <input
                                            type="email"
                                            value={settings.profile.email}
                                            onChange={(e) => updateSettings('profile', 'email', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Phone</label>
                                        <input
                                            type="tel"
                                            value={settings.profile.phone}
                                            onChange={(e) => updateSettings('profile', 'phone', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Timezone</label>
                                        <select
                                            value={settings.profile.timezone}
                                            onChange={(e) => updateSettings('profile', 'timezone', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        >
                                            <option value="America/New_York">Eastern Time</option>
                                            <option value="America/Chicago">Central Time</option>
                                            <option value="America/Denver">Mountain Time</option>
                                            <option value="America/Los_Angeles">Pacific Time</option>
                                            <option value="UTC">UTC</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Language</label>
                                        <select
                                            value={settings.profile.language}
                                            onChange={(e) => updateSettings('profile', 'language', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        >
                                            <option value="en">English</option>
                                            <option value="es">Spanish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                            <option value="zh">Chinese</option>
                                        </select>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {activeTab === 'notifications' && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Notification Preferences</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="space-y-4">
                                    {Object.entries(settings.notifications).map(([key, value]) => (
                                        <div key={key} className="flex items-center justify-between">
                                            <div>
                                                <h4 className="text-sm font-medium text-gray-900">
                                                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                                                </h4>
                                                <p className="text-sm text-gray-500">
                                                    {key === 'emailNotifications' && 'Receive notifications via email'}
                                                    {key === 'pushNotifications' && 'Receive push notifications in browser'}
                                                    {key === 'workflowAlerts' && 'Get alerts when workflows complete or fail'}
                                                    {key === 'securityAlerts' && 'Receive security-related notifications'}
                                                    {key === 'weeklyReports' && 'Get weekly usage and performance reports'}
                                                </p>
                                            </div>
                                            <button
                                                type="button"
                                                onClick={() => updateSettings('notifications', key, !value)}
                                                className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                                                    value ? 'bg-blue-600' : 'bg-gray-200'
                                                }`}
                                            >
                                                <span
                                                    className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                                                        value ? 'translate-x-5' : 'translate-x-0'
                                                    }`}
                                                />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {activeTab === 'security' && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Security Settings</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
                                            <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
                                        </div>
                                        <button
                                            type="button"
                                            onClick={() => updateSettings('security', 'twoFactorEnabled', !settings.security.twoFactorEnabled)}
                                            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                                                settings.security.twoFactorEnabled ? 'bg-blue-600' : 'bg-gray-200'
                                            }`}
                                        >
                                            <span
                                                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                                                    settings.security.twoFactorEnabled ? 'translate-x-5' : 'translate-x-0'
                                                }`}
                                            />
                                        </button>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Session Timeout (minutes)</label>
                                        <input
                                            type="number"
                                            value={settings.security.sessionTimeout}
                                            onChange={(e) => updateSettings('security', 'sessionTimeout', parseInt(e.target.value))}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">API Key Rotation (days)</label>
                                        <input
                                            type="number"
                                            value={settings.security.apiKeyRotation}
                                            onChange={(e) => updateSettings('security', 'apiKeyRotation', parseInt(e.target.value))}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {activeTab === 'billing' && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Billing & Usage</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div>
                                    <h4 className="text-lg font-medium text-gray-900">Current Plan: {settings.billing.plan}</h4>
                                    <p className="text-sm text-gray-500">Manage your subscription and view usage</p>
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
                                    <div className="bg-gray-50 p-4 rounded-lg">
                                        <div className="flex items-center">
                                            <ChartBarIcon className="h-8 w-8 text-blue-600" />
                                            <div className="ml-4">
                                                <p className="text-sm font-medium text-gray-500">API Requests</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {settings.billing.usage.requests.toLocaleString()}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    of {settings.billing.limits.requests.toLocaleString()} limit
                                                </p>
                                            </div>
                                        </div>
                                        <div className="mt-4">
                                            <div className="bg-gray-200 rounded-full h-2">
                                                <div
                                                    className="bg-blue-600 h-2 rounded-full"
                                                    style={{
                                                        width: `${(settings.billing.usage.requests / settings.billing.limits.requests) * 100}%`
                                                    }}
                                                ></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-gray-50 p-4 rounded-lg">
                                        <div className="flex items-center">
                                            <DocumentTextIcon className="h-8 w-8 text-green-600" />
                                            <div className="ml-4">
                                                <p className="text-sm font-medium text-gray-500">Tokens Used</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {(settings.billing.usage.tokens / 1000000).toFixed(1)}M
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    of {(settings.billing.limits.tokens / 1000000).toFixed(0)}M limit
                                                </p>
                                            </div>
                                        </div>
                                        <div className="mt-4">
                                            <div className="bg-gray-200 rounded-full h-2">
                                                <div
                                                    className="bg-green-600 h-2 rounded-full"
                                                    style={{
                                                        width: `${(settings.billing.usage.tokens / settings.billing.limits.tokens) * 100}%`
                                                    }}
                                                ></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-gray-50 p-4 rounded-lg">
                                        <div className="flex items-center">
                                            <Cog6ToothIcon className="h-8 w-8 text-purple-600" />
                                            <div className="ml-4">
                                                <p className="text-sm font-medium text-gray-500">Storage Used</p>
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {formatBytes(settings.billing.usage.storage)}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    of {formatBytes(settings.billing.limits.storage)} limit
                                                </p>
                                            </div>
                                        </div>
                                        <div className="mt-4">
                                            <div className="bg-gray-200 rounded-full h-2">
                                                <div
                                                    className="bg-purple-600 h-2 rounded-full"
                                                    style={{
                                                        width: `${(settings.billing.usage.storage / settings.billing.limits.storage) * 100}%`
                                                    }}
                                                ></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {activeTab === 'integrations' && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Integrations</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Webhook URL</label>
                                    <input
                                        type="url"
                                        value={settings.integrations.webhookUrl}
                                        onChange={(e) => updateSettings('integrations', 'webhookUrl', e.target.value)}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        placeholder="https://your-app.com/webhooks"
                                    />
                                </div>

                                <div className="space-y-4">
                                    {[
                                        { key: 'slackEnabled', name: 'Slack', description: 'Send notifications to Slack channels' },
                                        { key: 'teamsEnabled', name: 'Microsoft Teams', description: 'Send notifications to Teams channels' },
                                        { key: 'discordEnabled', name: 'Discord', description: 'Send notifications to Discord servers' },
                                    ].map((integration) => (
                                        <div key={integration.key} className="flex items-center justify-between">
                                            <div>
                                                <h4 className="text-sm font-medium text-gray-900">{integration.name}</h4>
                                                <p className="text-sm text-gray-500">{integration.description}</p>
                                            </div>
                                            <button
                                                type="button"
                                                onClick={() => updateSettings('integrations', integration.key, !settings.integrations[integration.key as keyof typeof settings.integrations])}
                                                className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                                                    settings.integrations[integration.key as keyof typeof settings.integrations] ? 'bg-blue-600' : 'bg-gray-200'
                                                }`}
                                            >
                                                <span
                                                    className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                                                        settings.integrations[integration.key as keyof typeof settings.integrations] ? 'translate-x-5' : 'translate-x-0'
                                                    }`}
                                                />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </div>
    );
}
