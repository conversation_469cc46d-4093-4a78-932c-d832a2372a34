// Axient MCP++ Frontend Configuration

export const config = {
    // API Configuration
    api: {
        baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
        timeout: 30000,
        retries: 3,
    },

    // WebSocket Configuration
    websocket: {
        url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:6001',
        reconnectAttempts: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_ATTEMPTS || '5'),
        reconnectDelay: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_DELAY || '1000'),
        heartbeatInterval: 30000,
    },

    // Application Configuration
    app: {
        name: process.env.NEXT_PUBLIC_APP_NAME || 'Axient MCP++',
        version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
        description: 'Universal AI Orchestration Platform',
        url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    },

    // Authentication Configuration
    auth: {
        cookieName: process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME || 'axient_token',
        sessionTimeout: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT || '3600000'), // 1 hour
        refreshThreshold: 300000, // 5 minutes before expiry
    },

    // AI Provider Configuration
    ai: {
        defaultProvider: process.env.NEXT_PUBLIC_DEFAULT_AI_PROVIDER || 'openai',
        defaultMaxTokens: parseInt(process.env.NEXT_PUBLIC_MAX_TOKENS_DEFAULT || '1000'),
        defaultTemperature: parseFloat(process.env.NEXT_PUBLIC_TEMPERATURE_DEFAULT || '0.7'),
        streamingEnabled: true,
    },

    // Chat Configuration
    chat: {
        maxMessageLength: parseInt(process.env.NEXT_PUBLIC_MAX_MESSAGE_LENGTH || '4000'),
        historyLimit: parseInt(process.env.NEXT_PUBLIC_CHAT_HISTORY_LIMIT || '100'),
        typingIndicatorTimeout: parseInt(process.env.NEXT_PUBLIC_TYPING_INDICATOR_TIMEOUT || '3000'),
        autoSaveInterval: 30000, // 30 seconds
    },

    // File Upload Configuration
    upload: {
        maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'), // 10MB
        allowedTypes: (process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || '.pdf,.txt,.doc,.docx,.md').split(','),
        chunkSize: 1024 * 1024, // 1MB chunks
    },

    // UI Configuration
    ui: {
        theme: {
            primary: '#3B82F6', // Blue
            secondary: '#10B981', // Green
            accent: '#F59E0B', // Amber
            error: '#EF4444', // Red
            warning: '#F59E0B', // Amber
            success: '#10B981', // Green
            info: '#3B82F6', // Blue
        },
        animations: {
            duration: 200,
            easing: 'ease-in-out',
        },
        breakpoints: {
            sm: '640px',
            md: '768px',
            lg: '1024px',
            xl: '1280px',
            '2xl': '1536px',
        },
    },

    // Development Configuration
    dev: {
        debugMode: process.env.NEXT_PUBLIC_DEBUG_MODE === 'true',
        logLevel: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',
        showDevTools: process.env.NODE_ENV === 'development',
    },

    // Feature Flags
    features: {
        enableWebSocket: true,
        enableFileUpload: true,
        enableVoiceInput: false,
        enableDarkMode: true,
        enableNotifications: true,
        enableAnalytics: true,
        enableMultiTenant: true,
        enableWorkflows: true,
        enableTools: true,
    },

    // Performance Configuration
    performance: {
        enableServiceWorker: true,
        enableImageOptimization: true,
        enableCodeSplitting: true,
        enablePrefetching: true,
        cacheTimeout: 300000, // 5 minutes
    },

    // Monitoring Configuration
    monitoring: {
        enableErrorTracking: true,
        enablePerformanceTracking: true,
        enableUserTracking: false, // Privacy-first
        sampleRate: 0.1, // 10% sampling
    },

    // Security Configuration
    security: {
        enableCSP: true,
        enableXSSProtection: true,
        enableFrameGuard: true,
        enableHSTS: true,
        cookieSecure: process.env.NODE_ENV === 'production',
        cookieSameSite: 'strict' as const,
    },
} as const;

// Type-safe environment variable access
export const getEnvVar = (key: string, defaultValue?: string): string => {
    const value = process.env[key];
    if (!value && !defaultValue) {
        throw new Error(`Environment variable ${key} is required but not set`);
    }
    return value || defaultValue || '';
};

// Validate required environment variables
export const validateConfig = (): void => {
    const requiredVars = [
        'NEXT_PUBLIC_API_URL',
        'NEXT_PUBLIC_WS_URL',
    ];

    const missing = requiredVars.filter(key => !process.env[key]);

    if (missing.length > 0) {
        console.warn(`Missing environment variables: ${missing.join(', ')}`);
        console.warn('Using default values. This may cause issues in production.');
    }
};

// Initialize configuration validation
if (typeof window === 'undefined') {
    // Only validate on server-side to avoid hydration issues
    validateConfig();
}

export default config; 