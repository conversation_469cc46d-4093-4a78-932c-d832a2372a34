<?php

namespace App\Services\AI;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\AI\Factory\AIProviderFactory;
use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\ConfigurationService;
use App\Services\Core\TenantContextService;
use App\Models\AIProvider;
use Illuminate\Support\Facades\Cache;

class AIProviderService extends BaseService
{
    protected AIProviderFactory $factory;
    protected array $providerInstances = [];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService,
        AIProviderFactory $factory
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
        $this->factory = $factory;
    }

    /**
     * Get service name for logging
     */
    protected function getServiceName(): string
    {
        return 'AIProviderService';
    }

    /**
     * Get provider instance for tenant
     */
    public function getProvider(string $tenantId, string $providerName = null): AIProviderInterface
    {
        // Use default provider if none specified
        if (!$providerName) {
            $providerName = $this->getDefaultProvider($tenantId);
        }

        $cacheKey = "ai_provider_{$tenantId}_{$providerName}";

        // Check if already instantiated
        if (isset($this->providerInstances[$cacheKey])) {
            return $this->providerInstances[$cacheKey];
        }

        // Get provider configuration from database
        $aiProvider = AIProvider::where('tenant_id', $tenantId)
            ->where('provider_name', $providerName)
            ->where('status', 'active')
            ->first();

        if (!$aiProvider) {
            throw new \RuntimeException("AI provider '{$providerName}' not configured for tenant");
        }

        // Create provider instance
        $provider = $this->factory->createFromModel($aiProvider);

        // Cache the instance
        $this->providerInstances[$cacheKey] = $provider;

        return $provider;
    }

    /**
     * Get default provider for tenant
     */
    public function getDefaultProvider(string $tenantId): string
    {
        $defaultProvider = AIProvider::where('tenant_id', $tenantId)
            ->where('is_default', true)
            ->where('status', 'active')
            ->first();

        if ($defaultProvider) {
            return $defaultProvider->provider_name;
        }

        // Fallback to first active provider
        $firstProvider = AIProvider::where('tenant_id', $tenantId)
            ->where('status', 'active')
            ->first();

        if ($firstProvider) {
            return $firstProvider->provider_name;
        }

        throw new \RuntimeException("No active AI providers configured for tenant");
    }

    /**
     * Generate text completion
     */
    public function generateCompletion(
        string $tenantId,
        string $prompt,
        array $options = [],
        string $providerName = null,
        bool $stream = false
    ): array {
        $startTime = microtime(true);

        try {
            $provider = $this->getProvider($tenantId, $providerName);

            $this->loggingService->logAIProvider('completion_request_started', [
                'tenant_id' => $tenantId,
                'provider_name' => $provider->getName(),
                'prompt_length' => strlen($prompt),
                'options' => $options,
                'stream' => $stream,
            ]);

            $result = $provider->generateCompletion($prompt, $options, $stream);

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            $this->loggingService->logAIProvider('completion_request_completed', [
                'tenant_id' => $tenantId,
                'provider_name' => $provider->getName(),
                'processing_time_ms' => $processingTime,
                'tokens_used' => $result['usage']['total_tokens'] ?? 0,
                'cost' => $result['cost'] ?? 0,
                'success' => true,
            ]);

            return array_merge($result, [
                'provider_name' => $provider->getName(),
                'processing_time_ms' => $processingTime,
            ]);

        } catch (\Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            $this->loggingService->logAIProvider('completion_request_failed', [
                'tenant_id' => $tenantId,
                'provider_name' => $providerName ?? 'unknown',
                'processing_time_ms' => $processingTime,
                'error' => $e->getMessage(),
                'success' => false,
            ], 'error');

            // Try fallback provider if available
            if (!$providerName) {
                return $this->tryFallbackProvider($tenantId, 'generateCompletion', [$prompt, $options, $stream]);
            }

            throw $e;
        }
    }

    /**
     * Generate chat completion
     */
    public function generateChatCompletion(
        string $tenantId,
        array $messages,
        array $options = [],
        string $providerName = null,
        bool $stream = false
    ): array {
        $startTime = microtime(true);

        try {
            $provider = $this->getProvider($tenantId, $providerName);

            $this->loggingService->logAIProvider('chat_completion_request_started', [
                'tenant_id' => $tenantId,
                'provider_name' => $provider->getName(),
                'messages_count' => count($messages),
                'options' => $options,
                'stream' => $stream,
            ]);

            $result = $provider->generateChatCompletion($messages, $options, $stream);

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            $this->loggingService->logAIProvider('chat_completion_request_completed', [
                'tenant_id' => $tenantId,
                'provider_name' => $provider->getName(),
                'processing_time_ms' => $processingTime,
                'tokens_used' => $result['usage']['total_tokens'] ?? 0,
                'cost' => $result['cost'] ?? 0,
                'success' => true,
            ]);

            return array_merge($result, [
                'provider_name' => $provider->getName(),
                'processing_time_ms' => $processingTime,
            ]);

        } catch (\Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            $this->loggingService->logAIProvider('chat_completion_request_failed', [
                'tenant_id' => $tenantId,
                'provider_name' => $providerName ?? 'unknown',
                'processing_time_ms' => $processingTime,
                'error' => $e->getMessage(),
                'success' => false,
            ], 'error');

            // Try fallback provider if available
            if (!$providerName) {
                return $this->tryFallbackProvider($tenantId, 'generateChatCompletion', [$messages, $options, $stream]);
            }

            throw $e;
        }
    }

    /**
     * Generate embeddings
     */
    public function generateEmbeddings(
        string $tenantId,
        array $texts,
        array $options = [],
        string $providerName = null
    ): array {
        $startTime = microtime(true);

        try {
            // Find provider that supports embeddings
            if (!$providerName) {
                $provider = $this->factory->getBestProviderForCapability($tenantId, 'embeddings');
                if (!$provider) {
                    throw new \RuntimeException("No providers support embeddings for this tenant");
                }
            } else {
                $provider = $this->getProvider($tenantId, $providerName);
            }

            $this->loggingService->logAIProvider('embeddings_request_started', [
                'tenant_id' => $tenantId,
                'provider_name' => $provider->getName(),
                'texts_count' => count($texts),
                'options' => $options,
            ]);

            $result = $provider->generateEmbeddings($texts, $options);

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            $this->loggingService->logAIProvider('embeddings_request_completed', [
                'tenant_id' => $tenantId,
                'provider_name' => $provider->getName(),
                'processing_time_ms' => $processingTime,
                'tokens_used' => $result['usage']['total_tokens'] ?? 0,
                'cost' => $result['cost'] ?? 0,
                'success' => true,
            ]);

            return array_merge($result, [
                'provider_name' => $provider->getName(),
                'processing_time_ms' => $processingTime,
            ]);

        } catch (\Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            $this->loggingService->logAIProvider('embeddings_request_failed', [
                'tenant_id' => $tenantId,
                'provider_name' => $providerName ?? 'unknown',
                'processing_time_ms' => $processingTime,
                'error' => $e->getMessage(),
                'success' => false,
            ], 'error');

            throw $e;
        }
    }

    /**
     * Test provider connection
     */
    public function testProvider(string $tenantId, string $providerName): array
    {
        try {
            $provider = $this->getProvider($tenantId, $providerName);
            $result = $provider->testConnection();

            $this->loggingService->logAIProvider('provider_connection_tested', [
                'tenant_id' => $tenantId,
                'provider_name' => $providerName,
                'test_result' => $result['status'] ?? 'unknown',
            ]);

            return $result;

        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('provider_connection_test_failed', [
                'tenant_id' => $tenantId,
                'provider_name' => $providerName,
                'error' => $e->getMessage(),
            ], 'error');

            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ];
        }
    }

    /**
     * Get available models for provider
     */
    public function getAvailableModels(string $tenantId, string $providerName): array
    {
        $cacheKey = "ai_models_{$tenantId}_{$providerName}";

        return Cache::remember($cacheKey, 3600, function () use ($tenantId, $providerName) {
            try {
                $provider = $this->getProvider($tenantId, $providerName);
                return $provider->getAvailableModels();
            } catch (\Exception $e) {
                $this->loggingService->logAIProvider('models_retrieval_failed', [
                    'tenant_id' => $tenantId,
                    'provider_name' => $providerName,
                    'error' => $e->getMessage(),
                ], 'error');
                return [];
            }
        });
    }

    /**
     * Get provider capabilities
     */
    public function getProviderCapabilities(string $tenantId, string $providerName): array
    {
        try {
            $provider = $this->getProvider($tenantId, $providerName);
            return $provider->getCapabilities();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get all configured providers for tenant
     */
    public function getConfiguredProviders(string $tenantId): array
    {
        return AIProvider::where('tenant_id', $tenantId)
            ->where('status', 'active')
            ->get()
            ->map(function ($aiProvider) {
                return [
                    'id' => $aiProvider->id,
                    'provider_name' => $aiProvider->provider_name,
                    'display_name' => $aiProvider->display_name,
                    'is_default' => $aiProvider->is_default,
                    'capabilities' => $aiProvider->capabilities,
                    'models' => $aiProvider->models,
                ];
            })
            ->toArray();
    }

    /**
     * Get provider health status
     */
    public function getProviderHealth(string $tenantId, string $providerName = null): array
    {
        if ($providerName) {
            return $this->testProvider($tenantId, $providerName);
        }

        // Test all providers
        return $this->factory->testAllProviders($tenantId);
    }

    /**
     * Try fallback provider
     */
    protected function tryFallbackProvider(string $tenantId, string $method, array $args): array
    {
        $providers = AIProvider::where('tenant_id', $tenantId)
            ->where('status', 'active')
            ->where('is_default', false)
            ->orderBy('created_at')
            ->get();

        foreach ($providers as $aiProvider) {
            try {
                $provider = $this->factory->createFromModel($aiProvider);

                $this->loggingService->logAIProvider('fallback_provider_attempt', [
                    'tenant_id' => $tenantId,
                    'fallback_provider' => $aiProvider->provider_name,
                    'method' => $method,
                ]);

                return call_user_func_array([$provider, $method], $args);

            } catch (\Exception $e) {
                $this->loggingService->logAIProvider('fallback_provider_failed', [
                    'tenant_id' => $tenantId,
                    'fallback_provider' => $aiProvider->provider_name,
                    'method' => $method,
                    'error' => $e->getMessage(),
                ], 'error');
                continue;
            }
        }

        throw new \RuntimeException("All AI providers failed for tenant");
    }

    /**
     * Calculate total cost for usage
     */
    public function calculateCost(string $tenantId, string $providerName, array $usage): float
    {
        try {
            $provider = $this->getProvider($tenantId, $providerName);
            return $provider->calculateCost($usage);
        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('cost_calculation_failed', [
                'tenant_id' => $tenantId,
                'provider_name' => $providerName,
                'error' => $e->getMessage(),
            ], 'error');
            return 0.0;
        }
    }

    /**
     * Get usage statistics for tenant
     */
    public function getUsageStatistics(string $tenantId, string $period = '24h'): array
    {
        // This would integrate with your logging/analytics system
        // For now, return basic structure
        return [
            'period' => $period,
            'total_requests' => 0,
            'total_tokens' => 0,
            'total_cost' => 0.0,
            'by_provider' => [],
            'by_model' => [],
        ];
    }
}
