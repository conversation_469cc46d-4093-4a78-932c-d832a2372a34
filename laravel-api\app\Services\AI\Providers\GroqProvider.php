<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class GroqProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://api.groq.com/openai/v1';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(10)->get($this->baseUrl . '/models');

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'Groq API connection successful',
                    'models_count' => count($response->json('data', [])),
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'Groq API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Groq API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/models');

            if ($response->successful()) {
                $models = $response->json('data', []);
                return array_map(function ($model) {
                    return [
                        'id' => $model['id'],
                        'name' => $model['id'],
                        'description' => $this->getModelDescription($model['id']),
                        'capabilities' => $this->getModelCapabilities($model['id']),
                        'context_length' => $this->getModelContextLength($model['id']),
                        'cost_per_token' => $this->getModelCostPerToken($model['id']),
                    ];
                }, $models);
            }

            return $this->getDefaultModels();

        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('groq_models_fetch_error', [
                'error' => $e->getMessage(),
            ], 'error');
            return $this->getDefaultModels();
        }
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        // Groq primarily uses chat completions, convert to chat format
        $messages = [['role' => 'user', 'content' => $prompt]];
        return $this->generateChatCompletion($messages, $options, $stream);
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'llama2-70b-4096';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'model' => $model,
                'messages' => $messages,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            if (isset($options['stop'])) {
                $payload['stop'] = $options['stop'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(30)->post($this->baseUrl . '/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['message']['content'] ?? '',
                    'message' => $data['choices'][0]['message'] ?? [],
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Groq API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        // Groq doesn't support embeddings directly
        return [
            'success' => false,
            'error' => 'Groq does not support embeddings',
            'provider' => 'groq',
        ];
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'streaming',
            'high_speed_inference',
        ];
    }

    public function getName(): string
    {
        return 'groq';
    }

    public function getDisplayName(): string
    {
        return 'Groq';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 30,
            'tokens_per_minute' => 6000,
            'requests_per_day' => 14400,
        ];
    }

    public function calculateCost(array $usage): float
    {
        $totalTokens = $usage['total_tokens'] ?? 0;

        // Groq pricing (example - very competitive pricing)
        return $totalTokens * 0.0000002; // $0.0002 per 1K tokens
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('groq_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'groq',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'llama2-70b-4096',
                'name' => 'Llama 2 70B',
                'description' => 'Meta Llama 2 70B model optimized for speed',
                'capabilities' => ['text_generation', 'chat'],
                'context_length' => 4096,
                'cost_per_token' => 0.0000002,
            ],
            [
                'id' => 'mixtral-8x7b-32768',
                'name' => 'Mixtral 8x7B',
                'description' => 'Mistral Mixtral 8x7B model with large context',
                'capabilities' => ['text_generation', 'chat'],
                'context_length' => 32768,
                'cost_per_token' => 0.0000002,
            ],
            [
                'id' => 'gemma-7b-it',
                'name' => 'Gemma 7B IT',
                'description' => 'Google Gemma 7B instruction-tuned model',
                'capabilities' => ['text_generation', 'chat'],
                'context_length' => 8192,
                'cost_per_token' => 0.0000001,
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $descriptions = [
            'llama2-70b-4096' => 'Meta Llama 2 70B model optimized for speed',
            'mixtral-8x7b-32768' => 'Mistral Mixtral 8x7B model with large context',
            'gemma-7b-it' => 'Google Gemma 7B instruction-tuned model',
        ];

        return $descriptions[$modelId] ?? 'Groq high-speed model';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        return ['text_generation', 'chat', 'high_speed_inference'];
    }

    protected function getModelContextLength(string $modelId): int
    {
        $contextLengths = [
            'llama2-70b-4096' => 4096,
            'mixtral-8x7b-32768' => 32768,
            'gemma-7b-it' => 8192,
        ];

        return $contextLengths[$modelId] ?? 4096;
    }

    protected function getModelCostPerToken(string $modelId): float
    {
        return 0.0000002; // Very competitive pricing
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'streaming',
            'high_speed_inference',
        ];
    }
}
