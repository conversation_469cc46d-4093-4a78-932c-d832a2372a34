<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class MistralProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://api.mistral.ai/v1';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(10)->get($this->baseUrl . '/models');

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'Mistral API connection successful',
                    'models_count' => count($response->json('data', [])),
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'Mistral API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Mistral API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/models');

            if ($response->successful()) {
                $models = $response->json('data', []);
                return array_map(function ($model) {
                    return [
                        'id' => $model['id'],
                        'name' => $model['id'],
                        'description' => $this->getModelDescription($model['id']),
                        'capabilities' => $this->getModelCapabilities($model['id']),
                        'context_length' => $this->getModelContextLength($model['id']),
                        'cost_per_token' => $this->getModelCostPerToken($model['id']),
                    ];
                }, $models);
            }

            return $this->getDefaultModels();

        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('mistral_models_fetch_error', [
                'error' => $e->getMessage(),
            ], 'error');
            return $this->getDefaultModels();
        }
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        // Mistral uses chat completions, convert to chat format
        $messages = [['role' => 'user', 'content' => $prompt]];
        return $this->generateChatCompletion($messages, $options, $stream);
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'mistral-medium';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'model' => $model,
                'messages' => $messages,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            if (isset($options['tools'])) {
                $payload['tools'] = $options['tools'];
            }

            if (isset($options['tool_choice'])) {
                $payload['tool_choice'] = $options['tool_choice'];
            }

            if (isset($options['safe_prompt'])) {
                $payload['safe_prompt'] = $options['safe_prompt'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['message']['content'] ?? '',
                    'message' => $data['choices'][0]['message'] ?? [],
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Mistral API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        $model = $options['model'] ?? 'mistral-embed';

        try {
            $payload = [
                'model' => $model,
                'input' => $texts,
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/embeddings', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'embeddings' => array_map(fn($item) => $item['embedding'], $data['data']),
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Mistral embedding error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'function_calling',
            'streaming',
            'european_compliance',
            'multilingual',
        ];
    }

    public function getName(): string
    {
        return 'mistral';
    }

    public function getDisplayName(): string
    {
        return 'Mistral AI';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 100,
            'tokens_per_minute' => 20000,
            'requests_per_day' => 1000,
        ];
    }

    public function calculateCost(array $usage): float
    {
        $promptTokens = $usage['prompt_tokens'] ?? 0;
        $completionTokens = $usage['completion_tokens'] ?? 0;

        // Mistral pricing (competitive European pricing)
        $promptCost = $promptTokens * 0.000002; // $2 per 1M tokens
        $completionCost = $completionTokens * 0.000006; // $6 per 1M tokens

        return $promptCost + $completionCost;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('mistral_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'mistral',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'mistral-large-latest',
                'name' => 'Mistral Large',
                'description' => 'Most capable Mistral model',
                'capabilities' => ['text_generation', 'chat', 'function_calling'],
                'context_length' => 32768,
                'cost_per_token' => 0.000008,
            ],
            [
                'id' => 'mistral-medium-latest',
                'name' => 'Mistral Medium',
                'description' => 'Balanced performance model',
                'capabilities' => ['text_generation', 'chat'],
                'context_length' => 32768,
                'cost_per_token' => 0.0000027,
            ],
            [
                'id' => 'mistral-small-latest',
                'name' => 'Mistral Small',
                'description' => 'Fast and efficient model',
                'capabilities' => ['text_generation', 'chat'],
                'context_length' => 32768,
                'cost_per_token' => 0.000002,
            ],
            [
                'id' => 'mistral-embed',
                'name' => 'Mistral Embed',
                'description' => 'Text embedding model',
                'capabilities' => ['embeddings'],
                'context_length' => 8192,
                'cost_per_token' => 0.0000001,
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $descriptions = [
            'mistral-large-latest' => 'Most capable Mistral model',
            'mistral-medium-latest' => 'Balanced performance model',
            'mistral-small-latest' => 'Fast and efficient model',
            'mistral-embed' => 'Text embedding model',
        ];

        return $descriptions[$modelId] ?? 'Mistral model';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        if (str_contains($modelId, 'embed')) {
            return ['embeddings'];
        }

        if (str_contains($modelId, 'large')) {
            return ['text_generation', 'chat', 'function_calling'];
        }

        return ['text_generation', 'chat'];
    }

    protected function getModelContextLength(string $modelId): int
    {
        $contextLengths = [
            'mistral-large-latest' => 32768,
            'mistral-medium-latest' => 32768,
            'mistral-small-latest' => 32768,
            'mistral-embed' => 8192,
        ];

        return $contextLengths[$modelId] ?? 32768;
    }

    protected function getModelCostPerToken(string $modelId): float
    {
        $costs = [
            'mistral-large-latest' => 0.000008,
            'mistral-medium-latest' => 0.0000027,
            'mistral-small-latest' => 0.000002,
            'mistral-embed' => 0.0000001,
        ];

        return $costs[$modelId] ?? 0.000002;
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'function_calling',
            'streaming',
            'european_compliance',
            'multilingual',
        ];
    }
}
