<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class OpenAIProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://api.openai.com/v1';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(10)->get($this->baseUrl . '/models');

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'OpenAI API connection successful',
                    'models_count' => count($response->json('data', [])),
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'OpenAI API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'OpenAI API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/models');

            if ($response->successful()) {
                $models = $response->json('data', []);
                return array_map(function ($model) {
                    return [
                        'id' => $model['id'],
                        'name' => $model['id'],
                        'description' => $this->getModelDescription($model['id']),
                        'capabilities' => $this->getModelCapabilities($model['id']),
                        'context_length' => $this->getModelContextLength($model['id']),
                        'cost_per_token' => $this->getModelCostPerToken($model['id']),
                    ];
                }, $models);
            }

            return $this->getDefaultModels();

        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('openai_models_fetch_error', [
                'error' => $e->getMessage(),
            ], 'error');
            return $this->getDefaultModels();
        }
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'gpt-3.5-turbo-instruct';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'model' => $model,
                'prompt' => $prompt,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            if (isset($options['stop'])) {
                $payload['stop'] = $options['stop'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['text'] ?? '',
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('OpenAI API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'gpt-3.5-turbo';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'model' => $model,
                'messages' => $messages,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            if (isset($options['functions'])) {
                $payload['functions'] = $options['functions'];
            }

            if (isset($options['function_call'])) {
                $payload['function_call'] = $options['function_call'];
            }

            if (isset($options['tools'])) {
                $payload['tools'] = $options['tools'];
            }

            if (isset($options['tool_choice'])) {
                $payload['tool_choice'] = $options['tool_choice'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['message']['content'] ?? '',
                    'message' => $data['choices'][0]['message'] ?? [],
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('OpenAI API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        $model = $options['model'] ?? 'text-embedding-ada-002';

        try {
            $payload = [
                'model' => $model,
                'input' => $texts,
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/embeddings', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'embeddings' => array_map(fn($item) => $item['embedding'], $data['data']),
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('OpenAI API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'function_calling',
            'streaming',
            'vision', // GPT-4V
            'code_generation',
        ];
    }

    public function getName(): string
    {
        return 'openai';
    }

    public function getDisplayName(): string
    {
        return 'OpenAI';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 3500,
            'tokens_per_minute' => 90000,
            'requests_per_day' => 10000,
        ];
    }

    public function calculateCost(array $usage): float
    {
        $promptTokens = $usage['prompt_tokens'] ?? 0;
        $completionTokens = $usage['completion_tokens'] ?? 0;

        // GPT-4 pricing (example - adjust based on actual model)
        $promptCost = $promptTokens * 0.00003; // $0.03 per 1K tokens
        $completionCost = $completionTokens * 0.00006; // $0.06 per 1K tokens

        return $promptCost + $completionCost;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('openai_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'openai',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'gpt-4',
                'name' => 'GPT-4',
                'description' => 'Most capable GPT-4 model',
                'capabilities' => ['text_generation', 'chat', 'function_calling'],
                'context_length' => 8192,
                'cost_per_token' => 0.00003,
            ],
            [
                'id' => 'gpt-4-turbo-preview',
                'name' => 'GPT-4 Turbo',
                'description' => 'Latest GPT-4 Turbo model',
                'capabilities' => ['text_generation', 'chat', 'function_calling'],
                'context_length' => 128000,
                'cost_per_token' => 0.00001,
            ],
            [
                'id' => 'gpt-3.5-turbo',
                'name' => 'GPT-3.5 Turbo',
                'description' => 'Fast and efficient model',
                'capabilities' => ['text_generation', 'chat', 'function_calling'],
                'context_length' => 4096,
                'cost_per_token' => 0.0000015,
            ],
            [
                'id' => 'text-embedding-ada-002',
                'name' => 'Text Embedding Ada 002',
                'description' => 'Text embedding model',
                'capabilities' => ['embeddings'],
                'context_length' => 8191,
                'cost_per_token' => 0.0000001,
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $descriptions = [
            'gpt-4' => 'Most capable GPT-4 model',
            'gpt-4-turbo-preview' => 'Latest GPT-4 Turbo model',
            'gpt-3.5-turbo' => 'Fast and efficient model',
            'text-embedding-ada-002' => 'Text embedding model',
        ];

        return $descriptions[$modelId] ?? 'OpenAI model';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        if (str_contains($modelId, 'embedding')) {
            return ['embeddings'];
        }

        if (str_contains($modelId, 'gpt')) {
            return ['text_generation', 'chat', 'function_calling'];
        }

        return ['text_generation'];
    }

    protected function getModelContextLength(string $modelId): int
    {
        $contextLengths = [
            'gpt-4' => 8192,
            'gpt-4-turbo-preview' => 128000,
            'gpt-3.5-turbo' => 4096,
            'text-embedding-ada-002' => 8191,
        ];

        return $contextLengths[$modelId] ?? 4096;
    }

    protected function getModelCostPerToken(string $modelId): float
    {
        $costs = [
            'gpt-4' => 0.00003,
            'gpt-4-turbo-preview' => 0.00001,
            'gpt-3.5-turbo' => 0.0000015,
            'text-embedding-ada-002' => 0.0000001,
        ];

        return $costs[$modelId] ?? 0.00001;
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'function_calling',
            'streaming',
            'vision',
            'code_generation',
        ];
    }
}
