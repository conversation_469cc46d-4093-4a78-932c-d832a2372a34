<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\WebSocket\WebSocketService;
use App\Services\WebSocket\ChannelManager;
use App\Services\WebSocket\SessionManager;
use App\Services\Core\LoggingService;
use App\Services\Core\TenantContextService;
use App\Events\WebSocket\NotificationEvent;
use App\Events\WebSocket\SystemAlertEvent;
use App\Models\WebSocketSession;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class WebSocketController extends Controller
{
    protected WebSocketService $webSocketService;
    protected ChannelManager $channelManager;
    protected SessionManager $sessionManager;
    protected LoggingService $loggingService;
    protected TenantContextService $tenantContext;

    public function __construct(
        WebSocketService $webSocketService,
        ChannelManager $channelManager,
        SessionManager $sessionManager,
        LoggingService $loggingService,
        TenantContextService $tenantContext
    ) {
        $this->webSocketService = $webSocketService;
        $this->channelManager = $channelManager;
        $this->sessionManager = $sessionManager;
        $this->loggingService = $loggingService;
        $this->tenantContext = $tenantContext;
    }

    /**
     * Authenticate WebSocket connection
     */
    public function authenticate(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'connection_id' => 'required|string|max:255',
                'channel_name' => 'nullable|string|max:255',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Authenticate WebSocket connection
            $authResult = $this->webSocketService->authenticateConnection(
                $request->input('connection_id'),
                $tenant->id,
                $user->id,
                $request->bearerToken(),
                $request->input('metadata', [])
            );

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $authResult['message'] ?? 'WebSocket authentication failed'
                ], 401);
            }

            // Create or update WebSocket session
            $session = $this->sessionManager->createSession(
                $tenant->id,
                $user->id,
                $request->input('connection_id'),
                $request->input('channel_name'),
                'websocket',
                $request->input('metadata', [])
            );

            $this->loggingService->logWebSocketEvent('websocket_authenticated', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'connection_id' => $request->input('connection_id'),
                'session_id' => $session['session_id'],
                'channel_name' => $request->input('channel_name'),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'session_id' => $session['session_id'],
                    'channel_name' => $session['channel_name'],
                    'connection_id' => $request->input('connection_id'),
                    'auth_token' => $authResult['auth_token'] ?? null,
                    'expires_at' => $authResult['expires_at'] ?? null,
                ],
                'message' => 'WebSocket authenticated successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_auth_error', [
                'connection_id' => $request->input('connection_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'WebSocket authentication failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get WebSocket sessions for the authenticated user
     */
    public function getSessions(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $sessions = WebSocketSession::where('tenant_id', $tenant->id)
                ->where('user_id', $user->id)
                ->where('status', 'active')
                ->orderBy('connected_at', 'desc')
                ->paginate(20);

            $this->loggingService->logWebSocketEvent('websocket_sessions_retrieved', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'sessions_count' => $sessions->count(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $sessions,
                'message' => 'WebSocket sessions retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_sessions_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve WebSocket sessions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Disconnect a specific WebSocket session
     */
    public function disconnectSession(Request $request, string $sessionId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $session = WebSocketSession::where('id', $sessionId)
                ->where('tenant_id', $tenant->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'WebSocket session not found'
                ], 404);
            }

            // Disconnect the session
            $disconnectResult = $this->sessionManager->disconnectSession(
                $sessionId,
                $tenant->id,
                'user_requested'
            );

            if (!$disconnectResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $disconnectResult['message'] ?? 'Failed to disconnect session'
                ], 500);
            }

            $this->loggingService->logWebSocketEvent('websocket_session_disconnected', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'session_id' => $sessionId,
                'reason' => 'user_requested',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'WebSocket session disconnected successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_disconnect_error', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to disconnect WebSocket session',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Broadcast a message to WebSocket channels (admin only)
     */
    public function broadcast(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'event_type' => 'required|string|in:notification,system_alert,announcement',
                'message' => 'required|string|max:1000',
                'channels' => 'nullable|array',
                'channels.*' => 'string|max:255',
                'target_users' => 'nullable|array',
                'target_users.*' => 'string|uuid',
                'metadata' => 'nullable|array',
                'priority' => 'nullable|string|in:low,normal,high,critical',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $eventType = $request->input('event_type');
            $message = $request->input('message');
            $channels = $request->input('channels', []);
            $targetUsers = $request->input('target_users', []);
            $metadata = $request->input('metadata', []);
            $priority = $request->input('priority', 'normal');

            // If no specific channels provided, use tenant-wide channel
            if (empty($channels) && empty($targetUsers)) {
                $channels = ["private-tenant.{$tenant->id}"];
            }

            // Add target user channels if specified
            foreach ($targetUsers as $targetUserId) {
                $channels[] = "private-user.{$tenant->id}.{$targetUserId}";
            }

            $broadcastId = Str::uuid();
            $broadcastCount = 0;

            // Broadcast based on event type
            switch ($eventType) {
                case 'notification':
                    foreach ($channels as $channel) {
                        broadcast(new NotificationEvent(
                            $tenant->id,
                            $broadcastId,
                            $user->id,
                            $user->name,
                            $message,
                            'admin_broadcast',
                            $priority,
                            array_merge($metadata, [
                                'channel' => $channel,
                                'broadcast_id' => $broadcastId,
                            ])
                        ));
                        $broadcastCount++;
                    }
                    break;

                case 'system_alert':
                case 'announcement':
                    foreach ($channels as $channel) {
                        broadcast(new SystemAlertEvent(
                            $tenant->id,
                            $broadcastId,
                            $eventType,
                            $message,
                            $priority,
                            array_merge($metadata, [
                                'channel' => $channel,
                                'broadcast_id' => $broadcastId,
                                'sender_id' => $user->id,
                                'sender_name' => $user->name,
                            ])
                        ));
                        $broadcastCount++;
                    }
                    break;
            }

            $this->loggingService->logWebSocketEvent('websocket_broadcast_sent', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'broadcast_id' => $broadcastId,
                'event_type' => $eventType,
                'channels_count' => count($channels),
                'target_users_count' => count($targetUsers),
                'broadcast_count' => $broadcastCount,
                'priority' => $priority,
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'broadcast_id' => $broadcastId,
                    'event_type' => $eventType,
                    'channels_count' => count($channels),
                    'broadcast_count' => $broadcastCount,
                    'priority' => $priority,
                ],
                'message' => 'Message broadcasted successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_broadcast_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to broadcast message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get WebSocket channel information
     */
    public function getChannelInfo(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'channel_name' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $channelName = $request->input('channel_name');

            // Get channel information
            $channelInfo = $this->channelManager->getChannelInfo($tenant->id, $channelName);

            if (!$channelInfo) {
                return response()->json([
                    'success' => false,
                    'message' => 'Channel not found or access denied'
                ], 404);
            }

            $this->loggingService->logWebSocketEvent('websocket_channel_info_retrieved', [
                'tenant_id' => $tenant->id,
                'channel_name' => $channelName,
                'active_connections' => $channelInfo['active_connections'] ?? 0,
            ]);

            return response()->json([
                'success' => true,
                'data' => $channelInfo,
                'message' => 'Channel information retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_channel_info_error', [
                'channel_name' => $request->input('channel_name'),
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve channel information',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get WebSocket connection statistics
     */
    public function getConnectionStats(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            // Get connection statistics
            $stats = $this->webSocketService->getConnectionStatistics($tenant->id);

            $this->loggingService->logWebSocketEvent('websocket_stats_retrieved', [
                'tenant_id' => $tenant->id,
                'total_connections' => $stats['total_connections'] ?? 0,
                'active_sessions' => $stats['active_sessions'] ?? 0,
            ]);

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Connection statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_stats_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve connection statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test WebSocket connectivity
     */
    public function testConnection(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'connection_id' => 'nullable|string|max:255',
                'test_message' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $testMessage = $request->input('test_message', 'WebSocket connection test');
            $connectionId = $request->input('connection_id');

            // Test WebSocket connectivity
            $testResult = $this->webSocketService->testConnection(
                $tenant->id,
                $user->id,
                $connectionId,
                $testMessage
            );

            $this->loggingService->logWebSocketEvent('websocket_connection_tested', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'connection_id' => $connectionId,
                'test_success' => $testResult['success'] ?? false,
                'response_time' => $testResult['response_time'] ?? 0,
            ]);

            return response()->json([
                'success' => true,
                'data' => $testResult,
                'message' => 'WebSocket connection test completed'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('websocket_test_error', [
                'connection_id' => $request->input('connection_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'WebSocket connection test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
