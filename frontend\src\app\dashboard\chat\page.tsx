'use client';

import { useState, useEffect } from 'react';
import {
    ChatBubbleLeftRightIcon,
    PlusIcon,
    PaperAirplaneIcon,
    CpuChipIcon,
    UserIcon,
    ClockIcon,
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

interface ChatSession {
    id: string;
    title: string;
    lastMessage: string;
    timestamp: string;
    messageCount: number;
    provider: string;
}

interface ChatMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
    provider?: string;
}

export default function ChatPage() {
    const [sessions, setSessions] = useState<ChatSession[]>([]);
    const [activeSession, setActiveSession] = useState<string | null>(null);
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [newMessage, setNewMessage] = useState('');
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        // Load chat sessions
        const loadSessions = async () => {
            // TODO: Replace with actual API call
            setSessions([
                {
                    id: '1',
                    title: 'General Discussion',
                    lastMessage: 'How can I help you today?',
                    timestamp: '2 minutes ago',
                    messageCount: 5,
                    provider: 'GPT-4',
                },
                {
                    id: '2',
                    title: 'Code Review',
                    lastMessage: 'The function looks good overall...',
                    timestamp: '1 hour ago',
                    messageCount: 12,
                    provider: 'Claude-3',
                },
                {
                    id: '3',
                    title: 'Data Analysis',
                    lastMessage: 'Based on the dataset...',
                    timestamp: '3 hours ago',
                    messageCount: 8,
                    provider: 'Gemini Pro',
                },
            ]);
        };

        loadSessions();
    }, []);

    const handleNewChat = () => {
        const newSession: ChatSession = {
            id: Date.now().toString(),
            title: 'New Chat',
            lastMessage: '',
            timestamp: 'Just now',
            messageCount: 0,
            provider: 'GPT-4',
        };
        setSessions([newSession, ...sessions]);
        setActiveSession(newSession.id);
        setMessages([]);
    };

    const handleSendMessage = async () => {
        if (!newMessage.trim() || loading) return;

        const userMessage: ChatMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: newMessage,
            timestamp: new Date().toLocaleTimeString(),
        };

        setMessages([...messages, userMessage]);
        setNewMessage('');
        setLoading(true);

        // TODO: Replace with actual AI provider API call
        setTimeout(() => {
            const assistantMessage: ChatMessage = {
                id: (Date.now() + 1).toString(),
                role: 'assistant',
                content: 'This is a simulated response. In the actual implementation, this would be a response from the selected AI provider.',
                timestamp: new Date().toLocaleTimeString(),
                provider: 'GPT-4',
            };
            setMessages(prev => [...prev, assistantMessage]);
            setLoading(false);
        }, 1500);
    };

    return (
        <div className="h-full flex">
            {/* Chat Sessions Sidebar */}
            <div className="w-80 border-r border-gray-200 bg-white flex flex-col">
                <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                        <h2 className="text-lg font-semibold text-gray-900">Chat Sessions</h2>
                        <button
                            onClick={handleNewChat}
                            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <PlusIcon className="h-4 w-4 mr-1" />
                            New Chat
                        </button>
                    </div>
                </div>

                <div className="flex-1 overflow-y-auto">
                    {sessions.map((session) => (
                        <div
                            key={session.id}
                            onClick={() => setActiveSession(session.id)}
                            className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                                activeSession === session.id ? 'bg-blue-50 border-blue-200' : ''
                            }`}
                        >
                            <div className="flex items-start justify-between">
                                <div className="flex-1 min-w-0">
                                    <h3 className="text-sm font-medium text-gray-900 truncate">
                                        {session.title}
                                    </h3>
                                    <p className="text-sm text-gray-500 truncate mt-1">
                                        {session.lastMessage}
                                    </p>
                                    <div className="flex items-center mt-2 text-xs text-gray-400">
                                        <ClockIcon className="h-3 w-3 mr-1" />
                                        {session.timestamp}
                                        <span className="mx-2">•</span>
                                        {session.messageCount} messages
                                        <span className="mx-2">•</span>
                                        <CpuChipIcon className="h-3 w-3 mr-1" />
                                        {session.provider}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Chat Interface */}
            <div className="flex-1 flex flex-col">
                {activeSession ? (
                    <>
                        {/* Chat Header */}
                        <div className="p-4 border-b border-gray-200 bg-white">
                            <div className="flex items-center">
                                <ChatBubbleLeftRightIcon className="h-6 w-6 text-gray-400 mr-3" />
                                <div>
                                    <h1 className="text-lg font-semibold text-gray-900">
                                        {sessions.find(s => s.id === activeSession)?.title}
                                    </h1>
                                    <p className="text-sm text-gray-500">
                                        Powered by {sessions.find(s => s.id === activeSession)?.provider}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Messages */}
                        <div className="flex-1 overflow-y-auto p-4 space-y-4">
                            {messages.length === 0 ? (
                                <div className="text-center py-12">
                                    <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">No messages yet</h3>
                                    <p className="mt-1 text-sm text-gray-500">Start a conversation with your AI assistant.</p>
                                </div>
                            ) : (
                                messages.map((message) => (
                                    <div
                                        key={message.id}
                                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                                    >
                                        <div
                                            className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                                message.role === 'user'
                                                    ? 'bg-blue-600 text-white'
                                                    : 'bg-gray-100 text-gray-900'
                                            }`}
                                        >
                                            <div className="flex items-center mb-1">
                                                {message.role === 'user' ? (
                                                    <UserIcon className="h-4 w-4 mr-2" />
                                                ) : (
                                                    <CpuChipIcon className="h-4 w-4 mr-2" />
                                                )}
                                                <span className="text-xs opacity-75">
                                                    {message.role === 'user' ? 'You' : message.provider || 'Assistant'}
                                                </span>
                                                <span className="text-xs opacity-50 ml-2">{message.timestamp}</span>
                                            </div>
                                            <p className="text-sm">{message.content}</p>
                                        </div>
                                    </div>
                                ))
                            )}
                            {loading && (
                                <div className="flex justify-start">
                                    <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100 text-gray-900">
                                        <div className="flex items-center">
                                            <CpuChipIcon className="h-4 w-4 mr-2" />
                                            <span className="text-xs opacity-75">Assistant is typing...</span>
                                        </div>
                                        <div className="flex space-x-1 mt-2">
                                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Message Input */}
                        <div className="p-4 border-t border-gray-200 bg-white">
                            <div className="flex space-x-4">
                                <div className="flex-1">
                                    <input
                                        type="text"
                                        value={newMessage}
                                        onChange={(e) => setNewMessage(e.target.value)}
                                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                                        placeholder="Type your message..."
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        disabled={loading}
                                    />
                                </div>
                                <button
                                    onClick={handleSendMessage}
                                    disabled={!newMessage.trim() || loading}
                                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <PaperAirplaneIcon className="h-4 w-4" />
                                </button>
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="flex-1 flex items-center justify-center">
                        <div className="text-center">
                            <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">Select a chat session</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Choose an existing conversation or start a new one.
                            </p>
                            <div className="mt-6">
                                <button
                                    onClick={handleNewChat}
                                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    <PlusIcon className="h-4 w-4 mr-2" />
                                    Start New Chat
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
