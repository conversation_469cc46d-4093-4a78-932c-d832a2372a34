<?php

namespace App\Services\Core;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

abstract class BaseService
{
    protected LoggingService $loggingService;
    protected ErrorHandlingService $errorHandlingService;
    protected TenantContextService $tenantContextService;
    protected ConfigurationService $configurationService;

    /**
     * Service configuration
     */
    protected array $config = [];

    /**
     * Cache configuration
     */
    protected array $cacheConfig = [
        'enabled' => true,
        'ttl' => 3600, // 1 hour default
        'prefix' => null,
    ];

    /**
     * Performance metrics
     */
    protected array $metrics = [
        'start_time' => null,
        'memory_start' => null,
        'queries_start' => null,
    ];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        $this->loggingService = $loggingService;
        $this->errorHandlingService = $errorHandlingService;
        $this->tenantContextService = $tenantContextService;
        $this->configurationService = $configurationService;

        $this->initializeService();
    }

    /**
     * Initialize service-specific configuration
     */
    protected function initializeService(): void
    {
        $this->config = $this->getServiceConfig();
        $this->cacheConfig = array_merge($this->cacheConfig, $this->getCacheConfig());

        // Set cache prefix if not set
        if (!$this->cacheConfig['prefix']) {
            $this->cacheConfig['prefix'] = $this->getServiceName() . ':';
        }
    }

    /**
     * Get service name (used for logging, caching, etc.)
     */
    abstract protected function getServiceName(): string;

    /**
     * Get service-specific configuration
     */
    protected function getServiceConfig(): array
    {
        return $this->configurationService->getServiceConfig($this->getServiceName());
    }

    /**
     * Get cache configuration for this service
     */
    protected function getCacheConfig(): array
    {
        return $this->configurationService->getCacheConfig($this->getServiceName());
    }

    /**
     * Start performance tracking
     */
    protected function startPerformanceTracking(): void
    {
        $this->metrics['start_time'] = microtime(true);
        $this->metrics['memory_start'] = memory_get_usage(true);
        $this->metrics['queries_start'] = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;
    }

    /**
     * End performance tracking and log metrics
     */
    protected function endPerformanceTracking(string $operation, array $context = []): void
    {
        if (!$this->metrics['start_time']) {
            return;
        }

        $executionTime = microtime(true) - $this->metrics['start_time'];
        $memoryUsed = memory_get_usage(true) - $this->metrics['memory_start'];
        $queriesExecuted = DB::getQueryLog() ? count(DB::getQueryLog()) - $this->metrics['queries_start'] : 0;

        $this->loggingService->logPerformance([
            'service' => $this->getServiceName(),
            'operation' => $operation,
            'execution_time_ms' => round($executionTime * 1000, 2),
            'memory_used_bytes' => $memoryUsed,
            'queries_executed' => $queriesExecuted,
            'context' => $context,
            'tenant_id' => $this->getCurrentTenantId(),
            'user_id' => $this->getCurrentUserId(),
        ]);

        // Reset metrics
        $this->metrics = [
            'start_time' => null,
            'memory_start' => null,
            'queries_start' => null,
        ];
    }

    /**
     * Execute operation with performance tracking and error handling
     */
    protected function executeWithTracking(string $operation, callable $callback, array $context = [])
    {
        $this->startPerformanceTracking();

        try {
            $result = $callback();
            $this->endPerformanceTracking($operation, $context);
            return $result;
        } catch (\Exception $e) {
            $this->endPerformanceTracking($operation . '_failed', $context);
            throw $this->errorHandlingService->handleException($e, [
                'service' => $this->getServiceName(),
                'operation' => $operation,
                'context' => $context,
            ]);
        }
    }

    /**
     * Get current tenant
     */
    protected function getCurrentTenant(): ?Tenant
    {
        return $this->tenantContextService->getCurrentTenant();
    }

    /**
     * Get current tenant ID
     */
    protected function getCurrentTenantId(): ?string
    {
        $tenant = $this->getCurrentTenant();
        return $tenant?->id;
    }

    /**
     * Get current user
     */
    protected function getCurrentUser(): ?User
    {
        return Auth::user();
    }

    /**
     * Get current user ID
     */
    protected function getCurrentUserId(): ?string
    {
        $user = $this->getCurrentUser();
        return $user?->id;
    }

    /**
     * Check if current user has permission
     */
    protected function hasPermission(string $permission): bool
    {
        $user = $this->getCurrentUser();
        return $user?->hasPermission($permission) ?? false;
    }

    /**
     * Ensure user has permission or throw exception
     */
    protected function requirePermission(string $permission): void
    {
        if (!$this->hasPermission($permission)) {
            throw $this->errorHandlingService->createPermissionException($permission);
        }
    }

    /**
     * Get cache key with tenant and service prefix
     */
    protected function getCacheKey(string $key): string
    {
        $tenantId = $this->getCurrentTenantId();
        $prefix = $this->cacheConfig['prefix'];

        return $tenantId ? "{$prefix}tenant:{$tenantId}:{$key}" : "{$prefix}global:{$key}";
    }

    /**
     * Get from cache
     */
    protected function getFromCache(string $key, $default = null)
    {
        if (!$this->cacheConfig['enabled']) {
            return $default;
        }

        return Cache::get($this->getCacheKey($key), $default);
    }

    /**
     * Put to cache
     */
    protected function putToCache(string $key, $value, ?int $ttl = null): void
    {
        if (!$this->cacheConfig['enabled']) {
            return;
        }

        $ttl = $ttl ?? $this->cacheConfig['ttl'];
        Cache::put($this->getCacheKey($key), $value, $ttl);
    }

    /**
     * Remember in cache (get or put)
     */
    protected function remember(string $key, callable $callback, ?int $ttl = null)
    {
        if (!$this->cacheConfig['enabled']) {
            return $callback();
        }

        $ttl = $ttl ?? $this->cacheConfig['ttl'];
        return Cache::remember($this->getCacheKey($key), $ttl, $callback);
    }

    /**
     * Forget from cache
     */
    protected function forgetFromCache(string $key): void
    {
        Cache::forget($this->getCacheKey($key));
    }

    /**
     * Clear all cache for this service
     */
    protected function clearServiceCache(): void
    {
        $pattern = $this->cacheConfig['prefix'] . '*';

        // This would need to be implemented based on cache driver
        // For Redis: Cache::getRedis()->del(Cache::getRedis()->keys($pattern))
        // For now, we'll log the cache clear request
        $this->loggingService->log([
            'log_type' => 'cache',
            'log_level' => 'info',
            'message' => 'Service cache clear requested',
            'service' => $this->getServiceName(),
            'pattern' => $pattern,
            'tenant_id' => $this->getCurrentTenantId(),
        ]);
    }

    /**
     * Log service activity
     */
    protected function logActivity(string $message, array $context = [], string $level = 'info'): void
    {
        $this->loggingService->log([
            'log_type' => 'service',
            'log_level' => $level,
            'message' => $message,
            'service' => $this->getServiceName(),
            'context' => $context,
            'tenant_id' => $this->getCurrentTenantId(),
            'user_id' => $this->getCurrentUserId(),
        ]);
    }

    /**
     * Validate required configuration
     */
    protected function validateConfig(array $requiredKeys): void
    {
        foreach ($requiredKeys as $key) {
            if (!isset($this->config[$key])) {
                throw $this->errorHandlingService->createConfigurationException(
                    "Missing required configuration key: {$key} for service: {$this->getServiceName()}"
                );
            }
        }
    }

    /**
     * Get configuration value with default
     */
    protected function getConfig(string $key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * Check if service is enabled
     */
    protected function isEnabled(): bool
    {
        return $this->getConfig('enabled', true);
    }

    /**
     * Ensure service is enabled or throw exception
     */
    protected function requireEnabled(): void
    {
        if (!$this->isEnabled()) {
            throw $this->errorHandlingService->createServiceDisabledException($this->getServiceName());
        }
    }

    /**
     * Execute database transaction with error handling
     */
    protected function executeInTransaction(callable $callback)
    {
        return DB::transaction(function () use ($callback) {
            return $callback();
        });
    }

    /**
     * Validate tenant context
     */
    protected function validateTenantContext(): void
    {
        if (!$this->getCurrentTenant()) {
            throw $this->errorHandlingService->createTenantContextException();
        }
    }

    /**
     * Get service health status
     */
    public function getHealthStatus(): array
    {
        return [
            'service' => $this->getServiceName(),
            'status' => $this->isEnabled() ? 'enabled' : 'disabled',
            'tenant_id' => $this->getCurrentTenantId(),
            'cache_enabled' => $this->cacheConfig['enabled'],
            'config_loaded' => !empty($this->config),
            'timestamp' => now()->toISOString(),
        ];
    }
}
