<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Tenant;
use Symfony\Component\HttpFoundation\Response;

class TenantAwareAuth
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Ensure user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'error' => 'Unauthenticated',
                'message' => 'Authentication required'
            ], 401);
        }

        $user = Auth::user();
        $tenant = $request->get('tenant');

        // Ensure tenant context is set
        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant context missing',
                'message' => 'Tenant context must be established before authentication'
            ], 400);
        }

        // Ensure user belongs to the current tenant
        if ($user->tenant_id !== $tenant->id) {
            return response()->json([
                'error' => 'Tenant mismatch',
                'message' => 'User does not belong to the current tenant'
            ], 403);
        }

        // Check if user is active
        if (!$user->is_active) {
            return response()->json([
                'error' => 'User inactive',
                'message' => 'This user account is currently inactive'
            ], 403);
        }

        // Check if user email is verified (if required)
        if ($tenant->require_email_verification && !$user->hasVerifiedEmail()) {
            return response()->json([
                'error' => 'Email not verified',
                'message' => 'Email verification is required for this tenant'
            ], 403);
        }

        // Check if 2FA is required and enabled
        if ($tenant->require_2fa && !$user->two_factor_enabled) {
            return response()->json([
                'error' => '2FA required',
                'message' => 'Two-factor authentication is required for this tenant'
            ], 403);
        }

        // Update last activity
        $user->update([
            'last_activity_at' => now(),
            'last_ip_address' => $request->ip(),
            'last_user_agent' => $request->userAgent()
        ]);

        // Add user to request for easy access
        $request->merge(['auth_user' => $user]);

        return $next($request);
    }
}
