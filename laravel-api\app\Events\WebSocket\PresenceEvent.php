<?php

namespace App\Events\WebSocket;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PresenceEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $tenantId;
    public string $userId;
    public string $presenceType;
    public string $status;
    public array $userInfo;
    public array $metadata;
    public string $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $tenantId,
        string $userId,
        string $presenceType,
        string $status,
        array $userInfo = [],
        array $metadata = []
    ) {
        $this->tenantId = $tenantId;
        $this->userId = $userId;
        $this->presenceType = $presenceType;
        $this->status = $status;
        $this->userInfo = $userInfo;
        $this->metadata = $metadata;
        $this->timestamp = now()->toISOString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PresenceChannel("presence.{$this->tenantId}"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event_type' => 'presence',
            'presence_type' => $this->presenceType,
            'user_id' => $this->userId,
            'status' => $this->status,
            'user_info' => $this->userInfo,
            'metadata' => $this->metadata,
            'timestamp' => $this->timestamp,
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return "presence.{$this->presenceType}";
    }

    /**
     * Determine if this event should be queued.
     */
    public function shouldQueue(): bool
    {
        return config('broadcasting.queue.enabled', true);
    }

    /**
     * Get the queue connection for the event.
     */
    public function broadcastQueue(): string
    {
        return config('broadcasting.queue.queue_name', 'websocket');
    }
}
