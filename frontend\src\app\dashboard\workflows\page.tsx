'use client';

import { useState, useEffect } from 'react';
import {
    ChartBarIcon,
    PlusIcon,
    PlayIcon,
    PauseIcon,
    StopIcon,
    Cog6ToothIcon,
    ClockIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

interface Workflow {
    id: string;
    name: string;
    description: string;
    status: 'running' | 'paused' | 'stopped' | 'completed' | 'error';
    steps: WorkflowStep[];
    createdAt: string;
    lastRun: string;
    nextRun?: string;
    totalRuns: number;
    successRate: number;
    avgDuration: number;
    isScheduled: boolean;
    schedule?: string;
}

interface WorkflowStep {
    id: string;
    name: string;
    type: 'ai_provider' | 'document_processing' | 'data_transformation' | 'webhook' | 'condition';
    status: 'pending' | 'running' | 'completed' | 'error' | 'skipped';
    duration?: number;
    config: Record<string, any>;
}

export default function WorkflowsPage() {
    const [workflows, setWorkflows] = useState<Workflow[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Load workflows
        const loadWorkflows = async () => {
            try {
                // TODO: Replace with actual API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                setWorkflows([
                    {
                        id: '1',
                        name: 'Document Analysis Pipeline',
                        description: 'Automatically analyze uploaded documents using multiple AI providers',
                        status: 'running',
                        steps: [
                            { id: '1', name: 'Extract Text', type: 'document_processing', status: 'completed', duration: 2.5, config: {} },
                            { id: '2', name: 'Analyze Content', type: 'ai_provider', status: 'running', config: {} },
                            { id: '3', name: 'Generate Summary', type: 'ai_provider', status: 'pending', config: {} },
                            { id: '4', name: 'Send Notification', type: 'webhook', status: 'pending', config: {} },
                        ],
                        createdAt: '2024-01-15T10:30:00Z',
                        lastRun: '2024-01-15T14:20:00Z',
                        nextRun: '2024-01-15T15:00:00Z',
                        totalRuns: 45,
                        successRate: 95.6,
                        avgDuration: 12.3,
                        isScheduled: true,
                        schedule: 'Every 30 minutes',
                    },
                    {
                        id: '2',
                        name: 'Customer Support Automation',
                        description: 'Route and respond to customer inquiries automatically',
                        status: 'completed',
                        steps: [
                            { id: '1', name: 'Classify Inquiry', type: 'ai_provider', status: 'completed', duration: 1.2, config: {} },
                            { id: '2', name: 'Check Knowledge Base', type: 'data_transformation', status: 'completed', duration: 0.8, config: {} },
                            { id: '3', name: 'Generate Response', type: 'ai_provider', status: 'completed', duration: 2.1, config: {} },
                            { id: '4', name: 'Send Response', type: 'webhook', status: 'completed', duration: 0.5, config: {} },
                        ],
                        createdAt: '2024-01-14T09:15:00Z',
                        lastRun: '2024-01-15T13:45:00Z',
                        totalRuns: 128,
                        successRate: 98.4,
                        avgDuration: 4.6,
                        isScheduled: false,
                    },
                    {
                        id: '3',
                        name: 'Content Generation Workflow',
                        description: 'Generate marketing content based on product data',
                        status: 'paused',
                        steps: [
                            { id: '1', name: 'Load Product Data', type: 'data_transformation', status: 'completed', duration: 1.5, config: {} },
                            { id: '2', name: 'Generate Headlines', type: 'ai_provider', status: 'error', config: {} },
                            { id: '3', name: 'Create Descriptions', type: 'ai_provider', status: 'pending', config: {} },
                            { id: '4', name: 'Format Output', type: 'data_transformation', status: 'pending', config: {} },
                        ],
                        createdAt: '2024-01-13T16:45:00Z',
                        lastRun: '2024-01-15T11:30:00Z',
                        totalRuns: 23,
                        successRate: 87.0,
                        avgDuration: 8.7,
                        isScheduled: true,
                        schedule: 'Daily at 9:00 AM',
                    },
                    {
                        id: '4',
                        name: 'Data Quality Check',
                        description: 'Validate and clean incoming data using AI',
                        status: 'error',
                        steps: [
                            { id: '1', name: 'Load Data', type: 'data_transformation', status: 'completed', duration: 0.8, config: {} },
                            { id: '2', name: 'Validate Schema', type: 'condition', status: 'completed', duration: 0.3, config: {} },
                            { id: '3', name: 'AI Quality Check', type: 'ai_provider', status: 'error', config: {} },
                            { id: '4', name: 'Clean Data', type: 'data_transformation', status: 'skipped', config: {} },
                        ],
                        createdAt: '2024-01-12T11:20:00Z',
                        lastRun: '2024-01-15T12:15:00Z',
                        totalRuns: 67,
                        successRate: 92.5,
                        avgDuration: 3.2,
                        isScheduled: true,
                        schedule: 'Every 2 hours',
                    },
                ]);
            } catch (error) {
                console.error('Error loading workflows:', error);
            } finally {
                setLoading(false);
            }
        };

        loadWorkflows();
    }, []);

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'running':
                return <PlayIcon className="h-5 w-5 text-green-500" />;
            case 'paused':
                return <PauseIcon className="h-5 w-5 text-yellow-500" />;
            case 'stopped':
                return <StopIcon className="h-5 w-5 text-gray-500" />;
            case 'completed':
                return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
            case 'error':
                return <XCircleIcon className="h-5 w-5 text-red-500" />;
            default:
                return <ClockIcon className="h-5 w-5 text-gray-400" />;
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'running':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Running</span>;
            case 'paused':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Paused</span>;
            case 'stopped':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Stopped</span>;
            case 'completed':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Completed</span>;
            case 'error':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Error</span>;
            default:
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>;
        }
    };

    const getStepStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
            case 'running':
                return <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
            case 'error':
                return <XCircleIcon className="h-4 w-4 text-red-500" />;
            case 'skipped':
                return <div className="h-4 w-4 bg-gray-300 rounded-full" />;
            default:
                return <div className="h-4 w-4 bg-gray-200 rounded-full" />;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="space-y-4">
                        {[1, 2, 3].map(i => (
                            <div key={i} className="h-32 bg-gray-200 rounded"></div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Workflows</h1>
                    <p className="mt-1 text-sm text-gray-500">
                        Automate your AI processes with custom workflows.
                    </p>
                </div>
                <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Create Workflow
                </button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ChartBarIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Workflows</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">{workflows.length}</dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <PlayIcon className="h-8 w-8 text-green-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Running</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {workflows.filter(w => w.status === 'running').length}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ClockIcon className="h-8 w-8 text-purple-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Scheduled</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {workflows.filter(w => w.isScheduled).length}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <span className="text-green-600 font-semibold text-sm">%</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Avg Success Rate</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {(workflows.reduce((sum, w) => sum + w.successRate, 0) / workflows.length).toFixed(1)}%
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Workflows List */}
            <div className="space-y-4">
                {workflows.map((workflow) => (
                    <Card key={workflow.id}>
                        <CardContent className="p-6">
                            <div className="flex items-start justify-between">
                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0 mt-1">
                                        {getStatusIcon(workflow.status)}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center space-x-2">
                                            <h3 className="text-lg font-medium text-gray-900">{workflow.name}</h3>
                                            {getStatusBadge(workflow.status)}
                                            {workflow.isScheduled && (
                                                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800">
                                                    Scheduled
                                                </span>
                                            )}
                                        </div>
                                        <p className="mt-1 text-sm text-gray-600">{workflow.description}</p>
                                        
                                        {/* Workflow Steps */}
                                        <div className="mt-4">
                                            <div className="flex items-center space-x-2 overflow-x-auto pb-2">
                                                {workflow.steps.map((step, index) => (
                                                    <div key={step.id} className="flex items-center space-x-2 flex-shrink-0">
                                                        <div className="flex items-center space-x-1">
                                                            {getStepStatusIcon(step.status)}
                                                            <span className="text-xs text-gray-600">{step.name}</span>
                                                        </div>
                                                        {index < workflow.steps.length - 1 && (
                                                            <div className="w-4 h-px bg-gray-300"></div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>

                                        <div className="mt-4 grid grid-cols-2 gap-4 sm:grid-cols-4">
                                            <div>
                                                <dt className="text-xs font-medium text-gray-500">Total Runs</dt>
                                                <dd className="mt-1 text-sm text-gray-900">{workflow.totalRuns}</dd>
                                            </div>
                                            <div>
                                                <dt className="text-xs font-medium text-gray-500">Success Rate</dt>
                                                <dd className="mt-1 text-sm text-gray-900">{workflow.successRate}%</dd>
                                            </div>
                                            <div>
                                                <dt className="text-xs font-medium text-gray-500">Avg Duration</dt>
                                                <dd className="mt-1 text-sm text-gray-900">{workflow.avgDuration}s</dd>
                                            </div>
                                            <div>
                                                <dt className="text-xs font-medium text-gray-500">Last Run</dt>
                                                <dd className="mt-1 text-sm text-gray-900">{formatDate(workflow.lastRun)}</dd>
                                            </div>
                                        </div>

                                        {workflow.schedule && (
                                            <div className="mt-2">
                                                <dt className="text-xs font-medium text-gray-500">Schedule</dt>
                                                <dd className="mt-1 text-sm text-gray-900">{workflow.schedule}</dd>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                    {workflow.status === 'running' ? (
                                        <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <PauseIcon className="h-4 w-4 mr-1" />
                                            Pause
                                        </button>
                                    ) : (
                                        <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <PlayIcon className="h-4 w-4 mr-1" />
                                            Run
                                        </button>
                                    )}
                                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <Cog6ToothIcon className="h-4 w-4 mr-1" />
                                        Configure
                                    </button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
