/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDbGFyYWdvbiU1QyU1Q3d3dyU1QyU1Q01DUCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2xhcmFnb24lNUMlNUN3d3clNUMlNUNNQ1AlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBa0k7QUFDbEk7QUFDQSwwT0FBcUk7QUFDckk7QUFDQSwwT0FBcUk7QUFDckk7QUFDQSxvUkFBMko7QUFDM0o7QUFDQSx3T0FBb0k7QUFDcEk7QUFDQSw0UEFBK0k7QUFDL0k7QUFDQSxrUUFBa0o7QUFDbEo7QUFDQSxzUUFBbUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXE1DUFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcbGFyYWdvblxcXFx3d3dcXFxcTUNQXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXE1DUFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcbGFyYWdvblxcXFx3d3dcXFxcTUNQXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXE1DUFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXdGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXE1DUFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcbGFyYWdvblxcd3d3XFxNQ1BcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: _lib_config__WEBPACK_IMPORTED_MODULE_2__.config.app.name,\n    description: _lib_config__WEBPACK_IMPORTED_MODULE_2__.config.app.description,\n    keywords: [\n        \"AI\",\n        \"orchestration\",\n        \"platform\",\n        \"automation\",\n        \"machine learning\"\n    ],\n    authors: [\n        {\n            name: \"Axient Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: '#fff',\n                            color: '#374151',\n                            border: '1px solid #e5e7eb',\n                            borderRadius: '8px',\n                            fontSize: '14px',\n                            fontWeight: '500',\n                            padding: '12px 16px',\n                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\MCP\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getEnvVar: () => (/* binding */ getEnvVar),\n/* harmony export */   validateConfig: () => (/* binding */ validateConfig)\n/* harmony export */ });\n// Axient MCP++ Frontend Configuration\nconst config = {\n    // API Configuration\n    api: {\n        baseUrl: \"http://localhost:8000/api\" || 0,\n        timeout: 30000,\n        retries: 3\n    },\n    // WebSocket Configuration\n    websocket: {\n        url: \"ws://localhost:6001\" || 0,\n        reconnectAttempts: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_ATTEMPTS || '5'),\n        reconnectDelay: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_DELAY || '1000'),\n        heartbeatInterval: 30000\n    },\n    // Application Configuration\n    app: {\n        name: \"Axient MCP++\" || 0,\n        version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n        description: 'Universal AI Orchestration Platform',\n        url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'\n    },\n    // Authentication Configuration\n    auth: {\n        cookieName: process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME || 'axient_token',\n        sessionTimeout: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT || '3600000'),\n        refreshThreshold: 300000\n    },\n    // AI Provider Configuration\n    ai: {\n        defaultProvider: process.env.NEXT_PUBLIC_DEFAULT_AI_PROVIDER || 'openai',\n        defaultMaxTokens: parseInt(process.env.NEXT_PUBLIC_MAX_TOKENS_DEFAULT || '1000'),\n        defaultTemperature: parseFloat(process.env.NEXT_PUBLIC_TEMPERATURE_DEFAULT || '0.7'),\n        streamingEnabled: true\n    },\n    // Chat Configuration\n    chat: {\n        maxMessageLength: parseInt(process.env.NEXT_PUBLIC_MAX_MESSAGE_LENGTH || '4000'),\n        historyLimit: parseInt(process.env.NEXT_PUBLIC_CHAT_HISTORY_LIMIT || '100'),\n        typingIndicatorTimeout: parseInt(process.env.NEXT_PUBLIC_TYPING_INDICATOR_TIMEOUT || '3000'),\n        autoSaveInterval: 30000\n    },\n    // File Upload Configuration\n    upload: {\n        maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'),\n        allowedTypes: (process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || '.pdf,.txt,.doc,.docx,.md').split(','),\n        chunkSize: 1024 * 1024\n    },\n    // UI Configuration\n    ui: {\n        theme: {\n            primary: '#3B82F6',\n            secondary: '#10B981',\n            accent: '#F59E0B',\n            error: '#EF4444',\n            warning: '#F59E0B',\n            success: '#10B981',\n            info: '#3B82F6'\n        },\n        animations: {\n            duration: 200,\n            easing: 'ease-in-out'\n        },\n        breakpoints: {\n            sm: '640px',\n            md: '768px',\n            lg: '1024px',\n            xl: '1280px',\n            '2xl': '1536px'\n        }\n    },\n    // Development Configuration\n    dev: {\n        debugMode: \"true\" === 'true',\n        logLevel: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',\n        showDevTools: \"development\" === 'development'\n    },\n    // Feature Flags\n    features: {\n        enableWebSocket: true,\n        enableFileUpload: true,\n        enableVoiceInput: false,\n        enableDarkMode: true,\n        enableNotifications: true,\n        enableAnalytics: true,\n        enableMultiTenant: true,\n        enableWorkflows: true,\n        enableTools: true\n    },\n    // Performance Configuration\n    performance: {\n        enableServiceWorker: true,\n        enableImageOptimization: true,\n        enableCodeSplitting: true,\n        enablePrefetching: true,\n        cacheTimeout: 300000\n    },\n    // Monitoring Configuration\n    monitoring: {\n        enableErrorTracking: true,\n        enablePerformanceTracking: true,\n        enableUserTracking: false,\n        sampleRate: 0.1\n    },\n    // Security Configuration\n    security: {\n        enableCSP: true,\n        enableXSSProtection: true,\n        enableFrameGuard: true,\n        enableHSTS: true,\n        cookieSecure: \"development\" === 'production',\n        cookieSameSite: 'strict'\n    }\n};\n// Type-safe environment variable access\nconst getEnvVar = (key, defaultValue)=>{\n    const value = process.env[key];\n    if (!value && !defaultValue) {\n        throw new Error(`Environment variable ${key} is required but not set`);\n    }\n    return value || defaultValue || '';\n};\n// Validate required environment variables\nconst validateConfig = ()=>{\n    const requiredVars = [\n        'NEXT_PUBLIC_API_URL',\n        'NEXT_PUBLIC_WS_URL'\n    ];\n    const missing = requiredVars.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        console.warn(`Missing environment variables: ${missing.join(', ')}`);\n        console.warn('Using default values. This may cause issues in production.');\n    }\n};\n// Initialize configuration validation\nif (true) {\n    // Only validate on server-side to avoid hydration issues\n    validateConfig();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXdGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChatBubbleLeftRightIcon,ChevronRightIcon,CloudIcon,CpuChipIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChatBubbleLeftRightIcon,ChevronRightIcon,CloudIcon,CpuChipIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChatBubbleLeftRightIcon,ChevronRightIcon,CloudIcon,CpuChipIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChatBubbleLeftRightIcon,ChevronRightIcon,CloudIcon,CpuChipIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChatBubbleLeftRightIcon,ChevronRightIcon,CloudIcon,CpuChipIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChatBubbleLeftRightIcon,ChevronRightIcon,CloudIcon,CpuChipIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChatBubbleLeftRightIcon,ChevronRightIcon,CloudIcon,CpuChipIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Home.useEffect\"], []);\n    if (!mounted) {\n        return null; // Prevent hydration mismatch\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    \"aria-label\": \"Global\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-16 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex lg:flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"-m-1.5 p-1.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                        children: _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.app.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex lg:gap-x-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/features\",\n                                        className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/providers\",\n                                        className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600\",\n                                        children: \"AI Providers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/docs\",\n                                        className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600\",\n                                        children: \"Documentation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/pricing\",\n                                        className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600\",\n                                        children: \"Log in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/register\",\n                                        className: \"rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative isolate px-6 pt-14 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-blue-400 to-indigo-400 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-4xl py-32 sm:py-48 lg:py-56\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\",\n                                        children: [\n                                            \"Universal AI\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                children: \"Orchestration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            ' ',\n                                            \"Platform\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto\",\n                                        children: \"Connect, orchestrate, and scale AI providers seamlessly. Build intelligent workflows with real-time collaboration, comprehensive monitoring, and enterprise-grade security.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/register\",\n                                                className: \"rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors\",\n                                                children: \"Start Free Trial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/demo\",\n                                                className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 flex items-center gap-1 transition-colors\",\n                                                children: [\n                                                    \"Watch Demo \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-7xl px-6 lg:px-8 pb-24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto max-w-2xl text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"Everything you need to orchestrate AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg leading-8 text-gray-600\",\n                                            children: \"Powerful features designed for modern AI applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                        className: \"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"h-5 w-5 flex-none text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"10+ AI Providers\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"flex-auto\",\n                                                            children: \"Connect to OpenAI, Anthropic, Google Gemini, Groq, Mistral, and more. Switch providers seamlessly with unified APIs.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-5 w-5 flex-none text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Real-time Collaboration\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"flex-auto\",\n                                                            children: \"WebSocket-powered real-time chat, typing indicators, and presence management for seamless team collaboration.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-5 w-5 flex-none text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"MCP Agent System\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"flex-auto\",\n                                                            children: \"Intelligent agent orchestration with intent classification, RAG retrieval, and workflow automation.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 flex-none text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Enterprise Scale\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"flex-auto\",\n                                                            children: \"Multi-tenant architecture with comprehensive monitoring, logging, and performance analytics.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 flex-none text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Security First\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"flex-auto\",\n                                                            children: \"End-to-end encryption, secure API key management, and comprehensive audit logging for enterprise compliance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChatBubbleLeftRightIcon_ChevronRightIcon_CloudIcon_CpuChipIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-5 w-5 flex-none text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"High Performance\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"flex-auto\",\n                                                            children: \"Optimized for speed with intelligent caching, load balancing, and automatic failover mechanisms.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-7xl px-6 lg:px-8 pb-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto max-w-2xl text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                        children: \"Ready to get started?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-4 text-lg leading-8 text-gray-600\",\n                                        children: \"Join thousands of developers building the future of AI applications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 flex items-center justify-center gap-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/register\",\n                                                className: \"rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors\",\n                                                children: \"Start Building Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-base font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors\",\n                                                children: \"Contact Sales\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-400 to-indigo-400 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 py-12 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"xl:grid xl:grid-cols-3 xl:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.app.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm leading-6 text-gray-300\",\n                                            children: \"The Universal AI Orchestration Platform for modern applications.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-2 md:gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Product\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/features\",\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: \"Features\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/providers\",\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: \"AI Providers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/pricing\",\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: \"Pricing\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-10 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/docs\",\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: \"Documentation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/contact\",\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: \"Contact\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/status\",\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 border-t border-gray-900/10 pt-8 sm:mt-20 lg:mt-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs leading-5 text-gray-400\",\n                                children: [\n                                    \"\\xa9 2024 \",\n                                    _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.app.name,\n                                    \". All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getEnvVar: () => (/* binding */ getEnvVar),\n/* harmony export */   validateConfig: () => (/* binding */ validateConfig)\n/* harmony export */ });\n// Axient MCP++ Frontend Configuration\nconst config = {\n    // API Configuration\n    api: {\n        baseUrl: \"http://localhost:8000/api\" || 0,\n        timeout: 30000,\n        retries: 3\n    },\n    // WebSocket Configuration\n    websocket: {\n        url: \"ws://localhost:6001\" || 0,\n        reconnectAttempts: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_ATTEMPTS || '5'),\n        reconnectDelay: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_DELAY || '1000'),\n        heartbeatInterval: 30000\n    },\n    // Application Configuration\n    app: {\n        name: \"Axient MCP++\" || 0,\n        version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n        description: 'Universal AI Orchestration Platform',\n        url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'\n    },\n    // Authentication Configuration\n    auth: {\n        cookieName: process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME || 'axient_token',\n        sessionTimeout: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT || '3600000'),\n        refreshThreshold: 300000\n    },\n    // AI Provider Configuration\n    ai: {\n        defaultProvider: process.env.NEXT_PUBLIC_DEFAULT_AI_PROVIDER || 'openai',\n        defaultMaxTokens: parseInt(process.env.NEXT_PUBLIC_MAX_TOKENS_DEFAULT || '1000'),\n        defaultTemperature: parseFloat(process.env.NEXT_PUBLIC_TEMPERATURE_DEFAULT || '0.7'),\n        streamingEnabled: true\n    },\n    // Chat Configuration\n    chat: {\n        maxMessageLength: parseInt(process.env.NEXT_PUBLIC_MAX_MESSAGE_LENGTH || '4000'),\n        historyLimit: parseInt(process.env.NEXT_PUBLIC_CHAT_HISTORY_LIMIT || '100'),\n        typingIndicatorTimeout: parseInt(process.env.NEXT_PUBLIC_TYPING_INDICATOR_TIMEOUT || '3000'),\n        autoSaveInterval: 30000\n    },\n    // File Upload Configuration\n    upload: {\n        maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'),\n        allowedTypes: (process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || '.pdf,.txt,.doc,.docx,.md').split(','),\n        chunkSize: 1024 * 1024\n    },\n    // UI Configuration\n    ui: {\n        theme: {\n            primary: '#3B82F6',\n            secondary: '#10B981',\n            accent: '#F59E0B',\n            error: '#EF4444',\n            warning: '#F59E0B',\n            success: '#10B981',\n            info: '#3B82F6'\n        },\n        animations: {\n            duration: 200,\n            easing: 'ease-in-out'\n        },\n        breakpoints: {\n            sm: '640px',\n            md: '768px',\n            lg: '1024px',\n            xl: '1280px',\n            '2xl': '1536px'\n        }\n    },\n    // Development Configuration\n    dev: {\n        debugMode: \"true\" === 'true',\n        logLevel: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',\n        showDevTools: \"development\" === 'development'\n    },\n    // Feature Flags\n    features: {\n        enableWebSocket: true,\n        enableFileUpload: true,\n        enableVoiceInput: false,\n        enableDarkMode: true,\n        enableNotifications: true,\n        enableAnalytics: true,\n        enableMultiTenant: true,\n        enableWorkflows: true,\n        enableTools: true\n    },\n    // Performance Configuration\n    performance: {\n        enableServiceWorker: true,\n        enableImageOptimization: true,\n        enableCodeSplitting: true,\n        enablePrefetching: true,\n        cacheTimeout: 300000\n    },\n    // Monitoring Configuration\n    monitoring: {\n        enableErrorTracking: true,\n        enablePerformanceTracking: true,\n        enableUserTracking: false,\n        sampleRate: 0.1\n    },\n    // Security Configuration\n    security: {\n        enableCSP: true,\n        enableXSSProtection: true,\n        enableFrameGuard: true,\n        enableHSTS: true,\n        cookieSecure: \"development\" === 'production',\n        cookieSameSite: 'strict'\n    }\n};\n// Type-safe environment variable access\nconst getEnvVar = (key, defaultValue)=>{\n    const value = process.env[key];\n    if (!value && !defaultValue) {\n        throw new Error(`Environment variable ${key} is required but not set`);\n    }\n    return value || defaultValue || '';\n};\n// Validate required environment variables\nconst validateConfig = ()=>{\n    const requiredVars = [\n        'NEXT_PUBLIC_API_URL',\n        'NEXT_PUBLIC_WS_URL'\n    ];\n    const missing = requiredVars.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        console.warn(`Missing environment variables: ${missing.join(', ')}`);\n        console.warn('Using default values. This may cause issues in production.');\n    }\n};\n// Initialize configuration validation\nif (true) {\n    // Only validate on server-side to avoid hydration issues\n    validateConfig();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();