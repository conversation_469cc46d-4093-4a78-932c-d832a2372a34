'use client';

import { useState, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
    EyeIcon,
    EyeSlashIcon,
    EnvelopeIcon,
    LockClosedIcon,
    UserIcon,
    BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui';
import AuthLayout from '@/components/auth/AuthLayout';
import ValidatedInput from '@/components/auth/ValidatedInput';
import { apiClient } from '@/services/api';
import { validateEmail, validatePassword, validateName, validatePasswordConfirmation } from '@/utils';
import { showSuccess, showError, showLoading, dismiss, TOAST_MESSAGES } from '@/lib/toast';

interface RegisterForm {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
    tenantName: string;
    agreeToTerms: boolean;
}

interface ValidationState {
    firstName: boolean;
    lastName: boolean;
    email: boolean;
    password: boolean;
    confirmPassword: boolean;
    tenantName: boolean;
}

export default function RegisterPage() {
    const router = useRouter();
    const [form, setForm] = useState<RegisterForm>({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: '',
        tenantName: '',
        agreeToTerms: false,
    });
    const [validationState, setValidationState] = useState<ValidationState>({
        firstName: false,
        lastName: false,
        email: false,
        password: false,
        confirmPassword: false,
        tenantName: false,
    });
    const [loading, setLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [termsError, setTermsError] = useState<string>('');

    // Memoized input change handlers to prevent re-creation
    const handleInputChange = useCallback((field: keyof RegisterForm) => (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const value = field === 'agreeToTerms' ? e.target.checked : e.target.value;
        setForm(prev => ({ ...prev, [field]: value }));

        // Clear terms error when user checks the box
        if (field === 'agreeToTerms' && termsError) {
            setTermsError('');
        }
    }, [termsError]);

    // Stable validation callback that doesn't change on every render
    const handleValidation = useCallback((field: keyof ValidationState) => {
        return (result: { isValid: boolean }) => {
            setValidationState(prev => ({ ...prev, [field]: result.isValid }));
        };
    }, []);

    // Memoized validator functions to prevent re-creation
    const validators = useMemo(() => ({
        firstName: (value: string) => validateName(value, 'First name'),
        lastName: (value: string) => validateName(value, 'Last name'),
        email: validateEmail,
        password: validatePassword,
        confirmPassword: (value: string) => validatePasswordConfirmation(form.password, value),
        tenantName: (value: string) => validateName(value, 'Organization name'),
    }), [form.password]); // Only depend on password for confirmPassword validation

    // Memoized validation callbacks to prevent re-creation
    const validationCallbacks = useMemo(() => ({
        firstName: handleValidation('firstName'),
        lastName: handleValidation('lastName'),
        email: handleValidation('email'),
        password: handleValidation('password'),
        confirmPassword: handleValidation('confirmPassword'),
        tenantName: handleValidation('tenantName'),
    }), [handleValidation]);

    // Memoized input change handlers
    const inputChangeHandlers = useMemo(() => ({
        firstName: handleInputChange('firstName'),
        lastName: handleInputChange('lastName'),
        email: handleInputChange('email'),
        password: handleInputChange('password'),
        confirmPassword: handleInputChange('confirmPassword'),
        tenantName: handleInputChange('tenantName'),
        agreeToTerms: handleInputChange('agreeToTerms'),
    }), [handleInputChange]);

    const isFormValid = Object.values(validationState).every(Boolean) &&
        form.firstName && form.lastName && form.email &&
        form.password && form.confirmPassword && form.tenantName &&
        form.agreeToTerms;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate terms agreement
        if (!form.agreeToTerms) {
            setTermsError('You must agree to the terms and conditions');
            showError(TOAST_MESSAGES.FORM_INVALID);
            return;
        }

        if (!isFormValid) {
            showError(TOAST_MESSAGES.FORM_INVALID);
            return;
        }

        setLoading(true);
        const loadingToast = showLoading('Creating your account...');

        try {
            // Prepare data in the format expected by Laravel API
            const registrationData = {
                name: `${form.firstName.trim()} ${form.lastName.trim()}`,
                email: form.email.trim(),
                password: form.password,
                password_confirmation: form.confirmPassword,
                tenant_name: form.tenantName.trim(),
            };

            const response = await apiClient.register(registrationData);

            if (response.success && response.data) {
                dismiss(loadingToast);

                // Store authentication data
                localStorage.setItem('auth_token', response.data.token);
                localStorage.setItem('user', JSON.stringify(response.data.user));

                showSuccess(TOAST_MESSAGES.REGISTER_SUCCESS);

                // Small delay for better UX
                setTimeout(() => {
                    router.push('/dashboard');
                }, 500);
            } else {
                dismiss(loadingToast);
                showError(response.message || TOAST_MESSAGES.REGISTER_ERROR);
            }
        } catch (error: any) {
            dismiss(loadingToast);
            console.error('Registration error:', error);

            // Handle specific error types
            if (error?.errors) {
                // Laravel validation errors
                const firstError = Object.values(error.errors)[0];
                const errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
                showError(errorMessage as string);
            } else if (error?.message) {
                showError(error.message);
            } else {
                // Check if it's a network error
                const errorMessage = error instanceof Error ? error.message : '';
                if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
                    showError(TOAST_MESSAGES.NETWORK_ERROR);
                } else {
                    showError(TOAST_MESSAGES.REGISTER_ERROR);
                }
            }
        } finally {
            setLoading(false);
        }
    };

    const handleSocialRegister = (provider: string) => {
        showError(`${provider} registration is not yet implemented. Please use the form below.`);
    };

    const togglePasswordVisibility = useCallback(() => {
        setShowPassword(prev => !prev);
    }, []);

    const toggleConfirmPasswordVisibility = useCallback(() => {
        setShowConfirmPassword(prev => !prev);
    }, []);

    return (
        <AuthLayout
            title="Create your account"
            subtitle="Join thousands of users orchestrating AI"
        >
            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Fields */}
                <div className="grid grid-cols-2 gap-4">
                    <ValidatedInput
                        label="First name"
                        type="text"
                        value={form.firstName}
                        onChange={inputChangeHandlers.firstName}
                        onValidation={validationCallbacks.firstName}
                        validator={validators.firstName}
                        leftIcon={<UserIcon />}
                        placeholder="John"
                        autoComplete="given-name"
                        required
                    />

                    <ValidatedInput
                        label="Last name"
                        type="text"
                        value={form.lastName}
                        onChange={inputChangeHandlers.lastName}
                        onValidation={validationCallbacks.lastName}
                        validator={validators.lastName}
                        leftIcon={<UserIcon />}
                        placeholder="Doe"
                        autoComplete="family-name"
                        required
                    />
                </div>

                <ValidatedInput
                    label="Email address"
                    type="email"
                    value={form.email}
                    onChange={inputChangeHandlers.email}
                    onValidation={validationCallbacks.email}
                    validator={validators.email}
                    leftIcon={<EnvelopeIcon />}
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    required
                />

                <ValidatedInput
                    label="Organization name"
                    type="text"
                    value={form.tenantName}
                    onChange={inputChangeHandlers.tenantName}
                    onValidation={validationCallbacks.tenantName}
                    validator={validators.tenantName}
                    leftIcon={<BuildingOfficeIcon />}
                    placeholder="Your Company"
                    helperText="This will be your workspace name"
                    required
                />

                <ValidatedInput
                    label="Password"
                    type={showPassword ? 'text' : 'password'}
                    value={form.password}
                    onChange={inputChangeHandlers.password}
                    onValidation={validationCallbacks.password}
                    validator={validators.password}
                    leftIcon={<LockClosedIcon />}
                    rightIcon={
                        <button
                            type="button"
                            onClick={togglePasswordVisibility}
                            className="cursor-pointer hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100"
                            tabIndex={-1}
                        >
                            {showPassword ? <EyeSlashIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                        </button>
                    }
                    placeholder="Create a strong password"
                    autoComplete="new-password"
                    helperText="Must be at least 8 characters with letters and numbers"
                    required
                />

                <ValidatedInput
                    label="Confirm password"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={form.confirmPassword}
                    onChange={inputChangeHandlers.confirmPassword}
                    onValidation={validationCallbacks.confirmPassword}
                    validator={validators.confirmPassword}
                    leftIcon={<LockClosedIcon />}
                    rightIcon={
                        <button
                            type="button"
                            onClick={toggleConfirmPasswordVisibility}
                            className="cursor-pointer hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100"
                            tabIndex={-1}
                        >
                            {showConfirmPassword ? <EyeSlashIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                        </button>
                    }
                    placeholder="Confirm your password"
                    autoComplete="new-password"
                    required
                />

                {/* Terms and Conditions */}
                <div className="space-y-2">
                    <div className="flex items-start">
                        <input
                            id="agree-terms"
                            name="agree-terms"
                            type="checkbox"
                            checked={form.agreeToTerms}
                            onChange={inputChangeHandlers.agreeToTerms}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1 transition-colors"
                        />
                        <label htmlFor="agree-terms" className="ml-2 block text-sm text-gray-900">
                            I agree to the{' '}
                            <Link href="/terms" className="text-blue-600 hover:text-blue-500 transition-colors">
                                Terms of Service
                            </Link>{' '}
                            and{' '}
                            <Link href="/privacy" className="text-blue-600 hover:text-blue-500 transition-colors">
                                Privacy Policy
                            </Link>
                        </label>
                    </div>
                    {termsError && (
                        <p className="text-sm text-red-600 flex items-center animate-in slide-in-from-top-1 duration-200">
                            <svg className="h-4 w-4 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            {termsError}
                        </p>
                    )}
                </div>

                <Button
                    type="submit"
                    loading={loading}
                    disabled={loading || !isFormValid}
                    className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:scale-[1.02] shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    size="lg"
                >
                    {loading ? 'Creating account...' : 'Create account'}
                </Button>
            </form>

            {/* Divider */}
            <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or sign up with</span>
                </div>
            </div>

            {/* Social Registration */}
            <div className="grid grid-cols-2 gap-3">
                <Button
                    variant="outline"
                    className="w-full border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200"
                    onClick={() => handleSocialRegister('Google')}
                    disabled={loading}
                >
                    <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                        <path
                            fill="currentColor"
                            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                        />
                        <path
                            fill="currentColor"
                            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                        />
                        <path
                            fill="currentColor"
                            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                        />
                        <path
                            fill="currentColor"
                            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                        />
                    </svg>
                    Google
                </Button>

                <Button
                    variant="outline"
                    className="w-full border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200"
                    onClick={() => handleSocialRegister('GitHub')}
                    disabled={loading}
                >
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                    </svg>
                    GitHub
                </Button>
            </div>

            {/* Sign in link */}
            <div className="text-center mt-6">
                <p className="text-sm text-gray-600">
                    Already have an account?{' '}
                    <Link
                        href="/login"
                        className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
                    >
                        Sign in here
                    </Link>
                </p>
            </div>
        </AuthLayout>
    );
} 