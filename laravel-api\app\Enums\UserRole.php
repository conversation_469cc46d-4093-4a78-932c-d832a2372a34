<?php

namespace App\Enums;

enum UserRole: string
{
    case OWNER = 'owner';
    case ADMIN = 'admin';
    case MANAGER = 'manager';
    case USER = 'user';
    case VIEWER = 'viewer';

    /**
     * Get all values as array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human readable label
     */
    public function label(): string
    {
        return match($this) {
            self::OWNER => 'Owner',
            self::ADMIN => 'Administrator',
            self::MANAGER => 'Manager',
            self::USER => 'User',
            self::VIEWER => 'Viewer',
        };
    }

    /**
     * Get role hierarchy level (higher number = more permissions)
     */
    public function level(): int
    {
        return match($this) {
            self::OWNER => 100,
            self::ADMIN => 80,
            self::MANAGER => 60,
            self::USER => 40,
            self::VIEWER => 20,
        };
    }

    /**
     * Check if role can manage users
     */
    public function canManageUsers(): bool
    {
        return in_array($this, [self::OWNER, self::ADMIN]);
    }

    /**
     * Check if role can manage tenant settings
     */
    public function canManageTenant(): bool
    {
        return $this === self::OWNER;
    }

    /**
     * Check if role has higher or equal permissions than given role
     */
    public function hasPermissionLevel(UserRole $role): bool
    {
        return $this->level() >= $role->level();
    }
}
