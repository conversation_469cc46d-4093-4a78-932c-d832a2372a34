<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Core\TenantContextService;
use App\Services\Core\LoggingService;
use App\Services\AI\AIProviderService;
use App\Models\AIProvider;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Crypt;

class AIProviderController extends Controller
{
    protected TenantContextService $tenantContext;
    protected LoggingService $loggingService;
    protected AIProviderService $aiProviderService;

    public function __construct(
        TenantContextService $tenantContext,
        LoggingService $loggingService,
        AIProviderService $aiProviderService
    ) {
        $this->tenantContext = $tenantContext;
        $this->loggingService = $loggingService;
        $this->aiProviderService = $aiProviderService;
    }

    /**
     * Get all AI providers for the tenant
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $providers = AIProvider::where('tenant_id', $tenant->id)
                ->orderBy('provider_name')
                ->get()
                ->map(function ($provider) {
                    return [
                        'id' => $provider->id,
                        'provider_name' => $provider->provider_name,
                        'display_name' => $provider->display_name,
                        'status' => $provider->status,
                        'is_default' => $provider->is_default,
                        'models' => $provider->models,
                        'capabilities' => $provider->capabilities,
                        'rate_limits' => $provider->rate_limits,
                        'created_at' => $provider->created_at,
                        'updated_at' => $provider->updated_at,
                        // Don't expose API keys
                    ];
                });

            $this->loggingService->logHttpRequest('ai_providers_retrieved', [
                'tenant_id' => $tenant->id,
                'providers_count' => $providers->count(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $providers,
                'message' => 'AI providers retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('ai_providers_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve AI providers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new AI provider configuration
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'provider_name' => 'required|string|in:openai,groq,huggingface,gemini,deepseek,mistral,codestral,anthropic,openrouter,grok',
                'display_name' => 'nullable|string|max:255',
                'api_key' => 'required|string|max:500',
                'api_endpoint' => 'nullable|url|max:500',
                'models' => 'nullable|array',
                'capabilities' => 'nullable|array',
                'rate_limits' => 'nullable|array',
                'is_default' => 'nullable|boolean',
                'status' => 'nullable|string|in:active,inactive',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Check if provider already exists for this tenant
            $existingProvider = AIProvider::where('tenant_id', $tenant->id)
                ->where('provider_name', $request->input('provider_name'))
                ->first();

            if ($existingProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'AI provider already configured for this tenant'
                ], 422);
            }

            // If this is set as default, unset other defaults
            if ($request->input('is_default', false)) {
                AIProvider::where('tenant_id', $tenant->id)
                    ->update(['is_default' => false]);
            }

            $provider = AIProvider::create([
                'id' => Str::uuid(),
                'tenant_id' => $tenant->id,
                'provider_name' => $request->input('provider_name'),
                'display_name' => $request->input('display_name') ?: ucfirst($request->input('provider_name')),
                'api_key' => Crypt::encryptString($request->input('api_key')),
                'api_endpoint' => $request->input('api_endpoint'),
                'models' => $request->input('models', []),
                'capabilities' => $request->input('capabilities', []),
                'rate_limits' => $request->input('rate_limits', []),
                'is_default' => $request->input('is_default', false),
                'status' => $request->input('status', 'active'),
            ]);

            $this->loggingService->logHttpRequest('ai_provider_created', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'provider_id' => $provider->id,
                'provider_name' => $provider->provider_name,
            ]);

            // Return provider without API key
            $responseData = $provider->toArray();
            unset($responseData['api_key']);

            return response()->json([
                'success' => true,
                'data' => $responseData,
                'message' => 'AI provider created successfully'
            ], 201);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('ai_provider_creation_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to create AI provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific AI provider
     */
    public function show(Request $request, string $providerId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $provider = AIProvider::where('id', $providerId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$provider) {
                return response()->json([
                    'success' => false,
                    'message' => 'AI provider not found'
                ], 404);
            }

            // Return provider without API key
            $responseData = $provider->toArray();
            unset($responseData['api_key']);

            $this->loggingService->logHttpRequest('ai_provider_retrieved', [
                'tenant_id' => $tenant->id,
                'provider_id' => $providerId,
            ]);

            return response()->json([
                'success' => true,
                'data' => $responseData,
                'message' => 'AI provider retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('ai_provider_retrieval_error', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve AI provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an AI provider configuration
     */
    public function update(Request $request, string $providerId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'display_name' => 'nullable|string|max:255',
                'api_key' => 'nullable|string|max:500',
                'api_endpoint' => 'nullable|url|max:500',
                'models' => 'nullable|array',
                'capabilities' => 'nullable|array',
                'rate_limits' => 'nullable|array',
                'is_default' => 'nullable|boolean',
                'status' => 'nullable|string|in:active,inactive',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $provider = AIProvider::where('id', $providerId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$provider) {
                return response()->json([
                    'success' => false,
                    'message' => 'AI provider not found'
                ], 404);
            }

            $updateData = [];

            if ($request->filled('display_name')) {
                $updateData['display_name'] = $request->input('display_name');
            }

            if ($request->filled('api_key')) {
                $updateData['api_key'] = Crypt::encryptString($request->input('api_key'));
            }

            if ($request->filled('api_endpoint')) {
                $updateData['api_endpoint'] = $request->input('api_endpoint');
            }

            if ($request->filled('models')) {
                $updateData['models'] = $request->input('models');
            }

            if ($request->filled('capabilities')) {
                $updateData['capabilities'] = $request->input('capabilities');
            }

            if ($request->filled('rate_limits')) {
                $updateData['rate_limits'] = $request->input('rate_limits');
            }

            if ($request->filled('status')) {
                $updateData['status'] = $request->input('status');
            }

            // Handle default provider setting
            if ($request->filled('is_default')) {
                $isDefault = $request->input('is_default');
                if ($isDefault) {
                    // Unset other defaults
                    AIProvider::where('tenant_id', $tenant->id)
                        ->where('id', '!=', $providerId)
                        ->update(['is_default' => false]);
                }
                $updateData['is_default'] = $isDefault;
            }

            $provider->update($updateData);

            $this->loggingService->logHttpRequest('ai_provider_updated', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'provider_id' => $providerId,
                'updated_fields' => array_keys($updateData),
            ]);

            // Return provider without API key
            $responseData = $provider->fresh()->toArray();
            unset($responseData['api_key']);

            return response()->json([
                'success' => true,
                'data' => $responseData,
                'message' => 'AI provider updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('ai_provider_update_error', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to update AI provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an AI provider configuration
     */
    public function destroy(Request $request, string $providerId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $provider = AIProvider::where('id', $providerId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$provider) {
                return response()->json([
                    'success' => false,
                    'message' => 'AI provider not found'
                ], 404);
            }

            $providerName = $provider->provider_name;
            $provider->delete();

            $this->loggingService->logHttpRequest('ai_provider_deleted', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'provider_id' => $providerId,
                'provider_name' => $providerName,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'AI provider deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('ai_provider_deletion_error', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete AI provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test connection to an AI provider
     */
    public function testConnection(Request $request, string $providerId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $provider = AIProvider::where('id', $providerId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$provider) {
                return response()->json([
                    'success' => false,
                    'message' => 'AI provider not found'
                ], 404);
            }

            // Test connection using the AI Provider Service
            $connectionResult = $this->aiProviderService->testProviderConnection($provider->provider_name);

            $this->loggingService->logHttpRequest('ai_provider_connection_tested', [
                'tenant_id' => $tenant->id,
                'provider_id' => $providerId,
                'provider_name' => $provider->provider_name,
                'connection_status' => $connectionResult['status'] ?? 'unknown',
            ]);

            return response()->json([
                'success' => true,
                'data' => $connectionResult,
                'message' => 'Connection test completed'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('ai_provider_connection_test_error', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to test connection',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available AI providers and their capabilities
     */
    public function getAvailableProviders(Request $request): JsonResponse
    {
        try {
            $availableProviders = $this->aiProviderService->getAvailableProviders();

            $this->loggingService->logHttpRequest('available_providers_retrieved', [
                'providers_count' => count($availableProviders),
            ]);

            return response()->json([
                'success' => true,
                'data' => $availableProviders,
                'message' => 'Available providers retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('available_providers_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve available providers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available models for a specific provider
     */
    public function getProviderModels(Request $request, string $providerName): JsonResponse
    {
        try {
            $models = $this->aiProviderService->getProviderModels($providerName);

            $this->loggingService->logHttpRequest('provider_models_retrieved', [
                'provider_name' => $providerName,
                'models_count' => count($models),
            ]);

            return response()->json([
                'success' => true,
                'data' => $models,
                'message' => 'Provider models retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('provider_models_error', [
                'provider_name' => $providerName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve provider models',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate text using a specific provider
     */
    public function generateText(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'provider_name' => 'required|string',
                'prompt' => 'required|string|max:10000',
                'model' => 'nullable|string',
                'max_tokens' => 'nullable|integer|min:1|max:4000',
                'temperature' => 'nullable|numeric|min:0|max:2',
                'stream' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $result = $this->aiProviderService->generateText(
                $request->input('prompt'),
                [
                    'provider' => $request->input('provider_name'),
                    'model' => $request->input('model'),
                    'max_tokens' => $request->input('max_tokens', 1000),
                    'temperature' => $request->input('temperature', 0.7),
                    'stream' => $request->input('stream', false),
                ]
            );

            $this->loggingService->logHttpRequest('text_generated', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'provider_name' => $request->input('provider_name'),
                'model' => $request->input('model'),
                'prompt_length' => strlen($request->input('prompt')),
                'success' => $result['success'] ?? false,
            ]);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Text generated successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('text_generation_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate text',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
