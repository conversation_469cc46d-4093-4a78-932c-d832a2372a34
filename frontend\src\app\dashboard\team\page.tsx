'use client';

import { useState, useEffect } from 'react';
import {
    UserGroupIcon,
    PlusIcon,
    UserCircleIcon,
    EnvelopeIcon,
    PhoneIcon,
    CalendarIcon,
    Cog6ToothIcon,
    TrashIcon,
    ShieldCheckIcon,
    UserIcon,
    EyeIcon,
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

interface TeamMember {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'editor' | 'viewer';
    status: 'active' | 'inactive' | 'pending';
    avatar?: string;
    phone?: string;
    joinedAt: string;
    lastActive: string;
    permissions: string[];
    department?: string;
    title?: string;
}

export default function TeamPage() {
    const [members, setMembers] = useState<TeamMember[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Load team members
        const loadMembers = async () => {
            try {
                // TODO: Replace with actual API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                setMembers([
                    {
                        id: '1',
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        role: 'admin',
                        status: 'active',
                        phone: '+****************',
                        joinedAt: '2024-01-01T00:00:00Z',
                        lastActive: '2024-01-15T14:30:00Z',
                        permissions: ['all'],
                        department: 'Engineering',
                        title: 'Senior Developer',
                    },
                    {
                        id: '2',
                        name: 'Sarah Johnson',
                        email: '<EMAIL>',
                        role: 'editor',
                        status: 'active',
                        phone: '+****************',
                        joinedAt: '2024-01-05T00:00:00Z',
                        lastActive: '2024-01-15T13:45:00Z',
                        permissions: ['chat', 'documents', 'workflows'],
                        department: 'Product',
                        title: 'Product Manager',
                    },
                    {
                        id: '3',
                        name: 'Mike Chen',
                        email: '<EMAIL>',
                        role: 'editor',
                        status: 'active',
                        joinedAt: '2024-01-08T00:00:00Z',
                        lastActive: '2024-01-15T12:20:00Z',
                        permissions: ['chat', 'providers', 'workflows'],
                        department: 'Engineering',
                        title: 'AI Engineer',
                    },
                    {
                        id: '4',
                        name: 'Emily Davis',
                        email: '<EMAIL>',
                        role: 'viewer',
                        status: 'active',
                        joinedAt: '2024-01-10T00:00:00Z',
                        lastActive: '2024-01-15T11:15:00Z',
                        permissions: ['chat', 'documents'],
                        department: 'Marketing',
                        title: 'Content Specialist',
                    },
                    {
                        id: '5',
                        name: 'Alex Rodriguez',
                        email: '<EMAIL>',
                        role: 'viewer',
                        status: 'pending',
                        joinedAt: '2024-01-14T00:00:00Z',
                        lastActive: '2024-01-14T16:00:00Z',
                        permissions: ['chat'],
                        department: 'Sales',
                        title: 'Sales Representative',
                    },
                    {
                        id: '6',
                        name: 'Lisa Wang',
                        email: '<EMAIL>',
                        role: 'editor',
                        status: 'inactive',
                        joinedAt: '2024-01-03T00:00:00Z',
                        lastActive: '2024-01-12T09:30:00Z',
                        permissions: ['chat', 'documents'],
                        department: 'Design',
                        title: 'UX Designer',
                    },
                ]);
            } catch (error) {
                console.error('Error loading team members:', error);
            } finally {
                setLoading(false);
            }
        };

        loadMembers();
    }, []);

    const getRoleBadge = (role: string) => {
        switch (role) {
            case 'admin':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Admin</span>;
            case 'editor':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Editor</span>;
            case 'viewer':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Viewer</span>;
            default:
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>;
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>;
            case 'inactive':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>;
            case 'pending':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>;
            default:
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>;
        }
    };

    const getRoleIcon = (role: string) => {
        switch (role) {
            case 'admin':
                return <ShieldCheckIcon className="h-5 w-5 text-red-500" />;
            case 'editor':
                return <Cog6ToothIcon className="h-5 w-5 text-blue-500" />;
            case 'viewer':
                return <EyeIcon className="h-5 w-5 text-green-500" />;
            default:
                return <UserIcon className="h-5 w-5 text-gray-500" />;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const formatLastActive = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
        
        if (diffInHours < 1) return 'Just now';
        if (diffInHours < 24) return `${diffInHours}h ago`;
        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays < 7) return `${diffInDays}d ago`;
        return formatDate(dateString);
    };

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="space-y-4">
                        {[1, 2, 3].map(i => (
                            <div key={i} className="h-24 bg-gray-200 rounded"></div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Team</h1>
                    <p className="mt-1 text-sm text-gray-500">
                        Manage your team members and their permissions.
                    </p>
                </div>
                <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Invite Member
                </button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <UserGroupIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Members</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">{members.length}</dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <span className="text-green-600 font-semibold text-sm">✓</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Active</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {members.filter(m => m.status === 'active').length}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ShieldCheckIcon className="h-8 w-8 text-red-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Admins</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {members.filter(m => m.role === 'admin').length}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <span className="text-yellow-600 font-semibold text-sm">⏳</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {members.filter(m => m.status === 'pending').length}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Team Members List */}
            <div className="space-y-4">
                {members.map((member) => (
                    <Card key={member.id}>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                    <div className="flex-shrink-0">
                                        {member.avatar ? (
                                            <img
                                                className="h-12 w-12 rounded-full"
                                                src={member.avatar}
                                                alt={member.name}
                                            />
                                        ) : (
                                            <UserCircleIcon className="h-12 w-12 text-gray-400" />
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center space-x-2">
                                            <h3 className="text-lg font-medium text-gray-900">{member.name}</h3>
                                            {getRoleBadge(member.role)}
                                            {getStatusBadge(member.status)}
                                        </div>
                                        <div className="flex items-center mt-1 space-x-4 text-sm text-gray-500">
                                            <div className="flex items-center">
                                                <EnvelopeIcon className="h-4 w-4 mr-1" />
                                                {member.email}
                                            </div>
                                            {member.phone && (
                                                <div className="flex items-center">
                                                    <PhoneIcon className="h-4 w-4 mr-1" />
                                                    {member.phone}
                                                </div>
                                            )}
                                        </div>
                                        {member.title && member.department && (
                                            <p className="mt-1 text-sm text-gray-600">
                                                {member.title} • {member.department}
                                            </p>
                                        )}
                                        <div className="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                                            <div className="flex items-center">
                                                <CalendarIcon className="h-3 w-3 mr-1" />
                                                Joined {formatDate(member.joinedAt)}
                                            </div>
                                            <div>
                                                Last active {formatLastActive(member.lastActive)}
                                            </div>
                                        </div>
                                        <div className="flex flex-wrap gap-1 mt-2">
                                            {member.permissions.map((permission) => (
                                                <span
                                                    key={permission}
                                                    className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                                                >
                                                    {permission}
                                                </span>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <div className="flex-shrink-0">
                                        {getRoleIcon(member.role)}
                                    </div>
                                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <Cog6ToothIcon className="h-4 w-4 mr-1" />
                                        Edit
                                    </button>
                                    {member.role !== 'admin' && (
                                        <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                            <TrashIcon className="h-4 w-4 mr-1" />
                                            Remove
                                        </button>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
