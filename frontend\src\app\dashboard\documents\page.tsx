'use client';

import { useState, useEffect } from 'react';
import {
    DocumentTextIcon,
    PlusIcon,
    CloudArrowUpIcon,
    EyeIcon,
    TrashIcon,
    MagnifyingGlassIcon,
    FunnelIcon,
    DocumentIcon,
    PhotoIcon,
    FilmIcon,
    MusicalNoteIcon,
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

interface Document {
    id: string;
    name: string;
    type: 'pdf' | 'docx' | 'txt' | 'image' | 'video' | 'audio';
    size: number;
    uploadedAt: string;
    status: 'processing' | 'ready' | 'error';
    tags: string[];
    description?: string;
    extractedText?: string;
    metadata: {
        pages?: number;
        duration?: number;
        dimensions?: string;
    };
}

export default function DocumentsPage() {
    const [documents, setDocuments] = useState<Document[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedType, setSelectedType] = useState<string>('all');
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Load documents
        const loadDocuments = async () => {
            try {
                // TODO: Replace with actual API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                setDocuments([
                    {
                        id: '1',
                        name: 'Project Requirements.pdf',
                        type: 'pdf',
                        size: 2048000,
                        uploadedAt: '2024-01-15T10:30:00Z',
                        status: 'ready',
                        tags: ['requirements', 'project'],
                        description: 'Detailed project requirements and specifications',
                        metadata: { pages: 15 },
                    },
                    {
                        id: '2',
                        name: 'Meeting Notes.docx',
                        type: 'docx',
                        size: 512000,
                        uploadedAt: '2024-01-14T14:20:00Z',
                        status: 'ready',
                        tags: ['meeting', 'notes'],
                        description: 'Weekly team meeting notes',
                        metadata: { pages: 3 },
                    },
                    {
                        id: '3',
                        name: 'Data Analysis.txt',
                        type: 'txt',
                        size: 128000,
                        uploadedAt: '2024-01-13T09:15:00Z',
                        status: 'processing',
                        tags: ['data', 'analysis'],
                        description: 'Raw data analysis results',
                    },
                    {
                        id: '4',
                        name: 'Architecture Diagram.png',
                        type: 'image',
                        size: 1024000,
                        uploadedAt: '2024-01-12T16:45:00Z',
                        status: 'ready',
                        tags: ['architecture', 'diagram'],
                        description: 'System architecture overview',
                        metadata: { dimensions: '1920x1080' },
                    },
                    {
                        id: '5',
                        name: 'Training Video.mp4',
                        type: 'video',
                        size: 52428800,
                        uploadedAt: '2024-01-11T11:30:00Z',
                        status: 'error',
                        tags: ['training', 'video'],
                        description: 'Employee training session recording',
                        metadata: { duration: 1800 },
                    },
                ]);
            } catch (error) {
                console.error('Error loading documents:', error);
            } finally {
                setLoading(false);
            }
        };

        loadDocuments();
    }, []);

    const getFileIcon = (type: string) => {
        switch (type) {
            case 'pdf':
            case 'docx':
            case 'txt':
                return <DocumentIcon className="h-8 w-8 text-red-500" />;
            case 'image':
                return <PhotoIcon className="h-8 w-8 text-green-500" />;
            case 'video':
                return <FilmIcon className="h-8 w-8 text-blue-500" />;
            case 'audio':
                return <MusicalNoteIcon className="h-8 w-8 text-purple-500" />;
            default:
                return <DocumentTextIcon className="h-8 w-8 text-gray-500" />;
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'ready':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Ready</span>;
            case 'processing':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Processing</span>;
            case 'error':
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Error</span>;
            default:
                return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>;
        }
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const filteredDocuments = documents.filter(doc => {
        const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                            doc.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                            doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
        const matchesType = selectedType === 'all' || doc.type === selectedType;
        return matchesSearch && matchesType;
    });

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="space-y-4">
                        {[1, 2, 3].map(i => (
                            <div key={i} className="h-24 bg-gray-200 rounded"></div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
                    <p className="mt-1 text-sm text-gray-500">
                        Manage your document library and knowledge base.
                    </p>
                </div>
                <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                    Upload Document
                </button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <DocumentTextIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Documents</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">{documents.length}</dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <span className="text-green-600 font-semibold text-sm">✓</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Ready</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {documents.filter(d => d.status === 'ready').length}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <span className="text-yellow-600 font-semibold text-sm">⟳</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Processing</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {documents.filter(d => d.status === 'processing').length}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span className="text-purple-600 font-semibold text-sm">∑</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Size</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {formatFileSize(documents.reduce((sum, d) => sum + d.size, 0))}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                            type="text"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Search documents..."
                        />
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <FunnelIcon className="h-5 w-5 text-gray-400" />
                    <select
                        value={selectedType}
                        onChange={(e) => setSelectedType(e.target.value)}
                        className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    >
                        <option value="all">All Types</option>
                        <option value="pdf">PDF</option>
                        <option value="docx">Word</option>
                        <option value="txt">Text</option>
                        <option value="image">Image</option>
                        <option value="video">Video</option>
                        <option value="audio">Audio</option>
                    </select>
                </div>
            </div>

            {/* Documents List */}
            <div className="space-y-4">
                {filteredDocuments.length === 0 ? (
                    <Card>
                        <CardContent className="p-12 text-center">
                            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No documents found</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                {searchQuery || selectedType !== 'all' 
                                    ? 'Try adjusting your search or filter criteria.'
                                    : 'Get started by uploading your first document.'
                                }
                            </p>
                            {!searchQuery && selectedType === 'all' && (
                                <div className="mt-6">
                                    <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                                        Upload Document
                                    </button>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                ) : (
                    filteredDocuments.map((document) => (
                        <Card key={document.id}>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-4">
                                        <div className="flex-shrink-0">
                                            {getFileIcon(document.type)}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h3 className="text-lg font-medium text-gray-900 truncate">
                                                {document.name}
                                            </h3>
                                            <div className="flex items-center mt-1 space-x-2">
                                                {getStatusBadge(document.status)}
                                                <span className="text-sm text-gray-500">
                                                    {formatFileSize(document.size)}
                                                </span>
                                                <span className="text-gray-300">•</span>
                                                <span className="text-sm text-gray-500">
                                                    {formatDate(document.uploadedAt)}
                                                </span>
                                            </div>
                                            {document.description && (
                                                <p className="mt-2 text-sm text-gray-600">{document.description}</p>
                                            )}
                                            <div className="flex flex-wrap gap-1 mt-2">
                                                {document.tags.map((tag) => (
                                                    <span
                                                        key={tag}
                                                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                                                    >
                                                        {tag}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <EyeIcon className="h-4 w-4 mr-1" />
                                            View
                                        </button>
                                        <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                            <TrashIcon className="h-4 w-4 mr-1" />
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </div>
        </div>
    );
}
