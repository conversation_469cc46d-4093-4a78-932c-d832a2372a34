<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Broadcaster
    |--------------------------------------------------------------------------
    |
    | This option controls the default broadcaster that will be used by the
    | framework when an event needs to be broadcast. You may set this to
    | any of the connections defined in the "connections" array below.
    |
    | Supported: "pusher", "ably", "redis", "log", "null"
    |
    */

    'default' => env('BROADCAST_DRIVER', 'redis'),

    /*
    |--------------------------------------------------------------------------
    | Broadcast Connections
    |--------------------------------------------------------------------------
    |
    | Here you may define all of the broadcast connections that will be used
    | to broadcast events to other systems or over websockets. Samples of
    | each available type of connection are provided inside this array.
    |
    */

    'connections' => [

        'pusher' => [
            'driver' => 'pusher',
            'key' => env('PUSHER_APP_KEY'),
            'secret' => env('PUSHER_APP_SECRET'),
            'app_id' => env('PUSHER_APP_ID'),
            'options' => [
                'cluster' => env('PUSHER_APP_CLUSTER'),
                'host' => env('PUSHER_HOST') ?: 'api-'.env('PUSHER_APP_CLUSTER', 'mt1').'.pusherapp.com',
                'port' => env('PUSHER_PORT', 443),
                'scheme' => env('PUSHER_SCHEME', 'https'),
                'encrypted' => true,
                'useTLS' => env('PUSHER_SCHEME', 'https') === 'https',
            ],
            'client_options' => [
                // Guzzle client options: https://docs.guzzlephp.org/en/stable/request-options.html
            ],
        ],

        'ably' => [
            'driver' => 'ably',
            'key' => env('ABLY_KEY'),
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'default',
            'prefix' => env('BROADCAST_REDIS_PREFIX', 'axient_mcp_broadcast'),
        ],

        'log' => [
            'driver' => 'log',
        ],

        'null' => [
            'driver' => 'null',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | WebSocket Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for WebSocket server and real-time features
    |
    */

    'websocket' => [
        'enabled' => env('WEBSOCKET_ENABLED', true),
        'host' => env('WEBSOCKET_HOST', '0.0.0.0'),
        'port' => env('WEBSOCKET_PORT', 6001),
        'ssl' => [
            'enabled' => env('WEBSOCKET_SSL_ENABLED', false),
            'cert_path' => env('WEBSOCKET_SSL_CERT_PATH'),
            'key_path' => env('WEBSOCKET_SSL_KEY_PATH'),
        ],
        'auth' => [
            'endpoint' => '/broadcasting/auth',
            'middleware' => ['web', 'websocket.auth'],
        ],
        'allowed_origins' => explode(',', env('WEBSOCKET_ALLOWED_ORIGINS', '*')),
        'heartbeat_interval' => env('WEBSOCKET_HEARTBEAT_INTERVAL', 30),
        'connection_timeout' => env('WEBSOCKET_CONNECTION_TIMEOUT', 60),
        'max_connections_per_tenant' => env('WEBSOCKET_MAX_CONNECTIONS_PER_TENANT', 1000),
        'max_connections_per_user' => env('WEBSOCKET_MAX_CONNECTIONS_PER_USER', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Channel Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for channel management and tenant isolation
    |
    */

    'channels' => [
        'tenant_prefix' => 'tenant',
        'private_prefix' => 'private',
        'presence_prefix' => 'presence',
        'public_prefix' => 'public',
        'system_prefix' => 'system',
        'chat_prefix' => 'chat',
        'notification_prefix' => 'notification',
        'monitoring_prefix' => 'monitoring',
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Rate limiting configuration for WebSocket connections and events
    |
    */

    'rate_limiting' => [
        'enabled' => env('WEBSOCKET_RATE_LIMITING_ENABLED', true),
        'connections_per_minute' => env('WEBSOCKET_CONNECTIONS_PER_MINUTE', 60),
        'messages_per_minute' => env('WEBSOCKET_MESSAGES_PER_MINUTE', 1000),
        'events_per_minute' => env('WEBSOCKET_EVENTS_PER_MINUTE', 500),
        'tenant_multiplier' => env('WEBSOCKET_TENANT_RATE_MULTIPLIER', 1.0),
    ],

    /*
    |--------------------------------------------------------------------------
    | Message Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for queuing WebSocket events and messages
    |
    */

    'queue' => [
        'enabled' => env('WEBSOCKET_QUEUE_ENABLED', true),
        'connection' => env('WEBSOCKET_QUEUE_CONNECTION', 'redis'),
        'queue_name' => env('WEBSOCKET_QUEUE_NAME', 'websocket'),
        'retry_attempts' => env('WEBSOCKET_QUEUE_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('WEBSOCKET_QUEUE_RETRY_DELAY', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring and Logging
    |--------------------------------------------------------------------------
    |
    | Configuration for WebSocket monitoring and logging
    |
    */

    'monitoring' => [
        'enabled' => env('WEBSOCKET_MONITORING_ENABLED', true),
        'log_connections' => env('WEBSOCKET_LOG_CONNECTIONS', true),
        'log_messages' => env('WEBSOCKET_LOG_MESSAGES', true),
        'log_errors' => env('WEBSOCKET_LOG_ERRORS', true),
        'metrics_collection' => env('WEBSOCKET_METRICS_COLLECTION', true),
        'performance_tracking' => env('WEBSOCKET_PERFORMANCE_TRACKING', true),
    ],

];
