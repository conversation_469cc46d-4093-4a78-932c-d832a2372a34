// Axient MCP++ API Client Service (Sanctum Optimized)

import axios, {
    AxiosInstance,
    AxiosRequestConfig,
    AxiosResponse,
    AxiosError
} from 'axios';
import { config } from '@/lib/config';
import {
    ApiResponse,
    ErrorResponse,
    User,
    AuthResponse,
    AIProvider,
    ChatSession,
    ChatMessage,
    Document,
    Tool,
    Workflow,
    SystemHealth,
    LogEntry,
    UsageStatistics,
    AIModel
} from '@/types';
import { getStorageItem, setStorageItem, removeStorageItem } from '@/utils';

class APIClient {
    private client: AxiosInstance;
    private token: string | null = null;

    constructor() {
        this.client = axios.create({
            baseURL: config.api.baseUrl,
            timeout: config.api.timeout,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            withCredentials: true, // Important for Sanctum
            xsrfCookieName: 'XSRF-TOKEN',   // <PERSON>vel default
            xsrfHeaderName: 'X-XSRF-TOKEN', // <PERSON>vel default
        });

        this.setupInterceptors();
        this.loadToken();
    }

    private setupInterceptors(): void {
        this.client.interceptors.request.use(
            (config) => {
                if (this.token) {
                    config.headers.Authorization = `Bearer ${this.token}`;
                }
                return config;
            },
            (error) => Promise.reject(error)
        );

        this.client.interceptors.response.use(
            (response) => response,
            (error: AxiosError) => Promise.reject(this.handleError(error))
        );
    }

    private handleError(error: AxiosError): ErrorResponse {
        console.error('API Error:', error);
        if (error.response?.data) {
            const errorData = error.response.data as any;
            if (errorData.errors) {
                const firstError = Object.values(errorData.errors)[0];
                const message = Array.isArray(firstError) ? firstError[0] : firstError;
                return {
                    success: false,
                    message: message as string || errorData.message || 'Validation error',
                    code: error.response.status.toString(),
                    errors: errorData.errors,
                };
            }
            return {
                success: false,
                message: errorData.message || 'An error occurred',
                code: error.response.status.toString(),
            };
        }
        return {
            success: false,
            message: error.message || 'Network error occurred',
            code: error.code || 'NETWORK_ERROR',
        };
    }

    private loadToken(): void {
        this.token = getStorageItem(config.auth.cookieName, null);
    }

    public setToken(token: string): void {
        this.token = token;
        setStorageItem(config.auth.cookieName, token);
    }

    public clearToken(): void {
        this.token = null;
        removeStorageItem(config.auth.cookieName);
    }

    // =====================================================================
    // CSRF INIT FUNCTION (call once on app boot before any request)
    // =====================================================================

    public async initializeCSRF(): Promise<void> {
        await this.client.get('/sanctum/csrf-cookie');
    }

    // =====================================================================
    // Generic Request Methods
    // =====================================================================

    private async request<T>(
        method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
        url: string,
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        const response: AxiosResponse<ApiResponse<T>> = await this.client.request({
            method,
            url,
            data,
            ...config,
        });
        return response.data;
    }

    public async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.request<T>('GET', url, undefined, config);
    }

    public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.request<T>('POST', url, data, config);
    }

    public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.request<T>('PUT', url, data, config);
    }

    public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.request<T>('DELETE', url, undefined, config);
    }

    public async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.request<T>('PATCH', url, data, config);
    }

    // =====================================================================
    // Authentication API (unchanged)
    // =====================================================================

    public async login(email: string, password: string): Promise<ApiResponse<AuthResponse>> {
        const response = await this.post<AuthResponse>('/auth/login', { email, password });
        if (response.success && response.data?.token) {
            this.setToken(response.data.token);
        }
        return response;
    }

    public async register(userData: {
        name: string;
        email: string;
        password: string;
        password_confirmation: string;
        tenant_name: string;
        tenant_subdomain?: string;
        phone?: string;
        timezone?: string;
        language?: string;
    }): Promise<ApiResponse<AuthResponse>> {
        const response = await this.post<AuthResponse>('/auth/register', userData);
        if (response.success && response.data?.token) {
            this.setToken(response.data.token);
        }
        return response;
    }

    public async logout(): Promise<ApiResponse<void>> {
        const response = await this.post<void>('/auth/logout');
        this.clearToken();
        return response;
    }

    public async getUser(): Promise<ApiResponse<User>> {
        return this.get<User>('/auth/user');
    }

    public async updateProfile(userData: Partial<User>): Promise<ApiResponse<User>> {
        return this.put<User>('/auth/profile', userData);
    }

    public async changePassword(data: {
        current_password: string;
        password: string;
        password_confirmation: string;
    }): Promise<ApiResponse<void>> {
        return this.post<void>('/auth/change-password', data);
    }

    public async forgotPassword(email: string): Promise<ApiResponse<void>> {
        return this.post<void>('/auth/forgot-password', { email });
    }

    public async resetPassword(data: {
        token: string;
        email: string;
        password: string;
        password_confirmation: string;
    }): Promise<ApiResponse<void>> {
        return this.post<void>('/auth/reset-password', data);
    }

    // =====================================================================
    // The remaining modules (AI Providers, Chat, Documents, Tools, etc.)
    // =====================================================================

    // 🚀 NOTE: all your existing module methods (getAIProviders, getChatSessions, etc.)
    // remain fully compatible as they are, and can directly call this.get, this.post, etc.
    // No need to rewrite them, you simply continue your current structure below.

    // For brevity I’m not repeating all your module APIs again (they remain valid).

}

// =====================================================================
// Export Singleton Instance
// =====================================================================

export const apiClient = new APIClient();
export default apiClient;
