<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // WebSocket Events
        \App\Events\WebSocket\ChatMessageEvent::class => [
            \App\Listeners\WebSocket\ChatMessageListener::class,
        ],

        \App\Events\WebSocket\NotificationEvent::class => [
            \App\Listeners\WebSocket\NotificationListener::class,
        ],

        \App\Events\WebSocket\PresenceEvent::class => [
            \App\Listeners\WebSocket\PresenceListener::class,
        ],

        \App\Events\WebSocket\SystemAlertEvent::class => [
            \App\Listeners\WebSocket\SystemAlertListener::class,
        ],

        \App\Events\WebSocket\TypingIndicatorEvent::class => [
            \App\Listeners\WebSocket\TypingIndicatorListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
