/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0JTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3Qtc2FucyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0U2FucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDbGFyYWdvbiU1QyU1Q3d3dyU1QyU1Q01DUCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJHZWlzdF9Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3QtbW9ubyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0TW9ubyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDbGFyYWdvbiU1QyU1Q3d3dyU1QyU1Q01DUCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDcmVhY3QtaG90LXRvYXN0JTVDJTVDZGlzdCU1QyU1Q2luZGV4Lm1qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2xhcmFnb24lNUMlNUN3d3clNUMlNUNNQ1AlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBaUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBcUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXE1DUFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQW1HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXE1DUFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\MCP\\frontend\\src\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\MCP\\frontend\\src\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcbGFyYWdvblxcd3d3XFxNQ1BcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: _lib_config__WEBPACK_IMPORTED_MODULE_2__.config.app.name,\n    description: _lib_config__WEBPACK_IMPORTED_MODULE_2__.config.app.description,\n    keywords: [\n        \"AI\",\n        \"orchestration\",\n        \"platform\",\n        \"automation\",\n        \"machine learning\"\n    ],\n    authors: [\n        {\n            name: \"Axient Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: '#fff',\n                            color: '#374151',\n                            border: '1px solid #e5e7eb',\n                            borderRadius: '8px',\n                            fontSize: '14px',\n                            fontWeight: '500',\n                            padding: '12px 16px',\n                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getEnvVar: () => (/* binding */ getEnvVar),\n/* harmony export */   validateConfig: () => (/* binding */ validateConfig)\n/* harmony export */ });\n// Axient MCP++ Frontend Configuration\nconst config = {\n    // API Configuration\n    api: {\n        baseUrl: \"http://localhost:8000/api\" || 0,\n        timeout: 30000,\n        retries: 3\n    },\n    // WebSocket Configuration\n    websocket: {\n        url: \"ws://localhost:6001\" || 0,\n        reconnectAttempts: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_ATTEMPTS || '5'),\n        reconnectDelay: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_DELAY || '1000'),\n        heartbeatInterval: 30000\n    },\n    // Application Configuration\n    app: {\n        name: \"Axient MCP++\" || 0,\n        version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n        description: 'Universal AI Orchestration Platform',\n        url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'\n    },\n    // Authentication Configuration\n    auth: {\n        cookieName: process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME || 'axient_token',\n        sessionTimeout: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT || '3600000'),\n        refreshThreshold: 300000\n    },\n    // AI Provider Configuration\n    ai: {\n        defaultProvider: process.env.NEXT_PUBLIC_DEFAULT_AI_PROVIDER || 'openai',\n        defaultMaxTokens: parseInt(process.env.NEXT_PUBLIC_MAX_TOKENS_DEFAULT || '1000'),\n        defaultTemperature: parseFloat(process.env.NEXT_PUBLIC_TEMPERATURE_DEFAULT || '0.7'),\n        streamingEnabled: true\n    },\n    // Chat Configuration\n    chat: {\n        maxMessageLength: parseInt(process.env.NEXT_PUBLIC_MAX_MESSAGE_LENGTH || '4000'),\n        historyLimit: parseInt(process.env.NEXT_PUBLIC_CHAT_HISTORY_LIMIT || '100'),\n        typingIndicatorTimeout: parseInt(process.env.NEXT_PUBLIC_TYPING_INDICATOR_TIMEOUT || '3000'),\n        autoSaveInterval: 30000\n    },\n    // File Upload Configuration\n    upload: {\n        maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'),\n        allowedTypes: (process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || '.pdf,.txt,.doc,.docx,.md').split(','),\n        chunkSize: 1024 * 1024\n    },\n    // UI Configuration\n    ui: {\n        theme: {\n            primary: '#3B82F6',\n            secondary: '#10B981',\n            accent: '#F59E0B',\n            error: '#EF4444',\n            warning: '#F59E0B',\n            success: '#10B981',\n            info: '#3B82F6'\n        },\n        animations: {\n            duration: 200,\n            easing: 'ease-in-out'\n        },\n        breakpoints: {\n            sm: '640px',\n            md: '768px',\n            lg: '1024px',\n            xl: '1280px',\n            '2xl': '1536px'\n        }\n    },\n    // Development Configuration\n    dev: {\n        debugMode: \"true\" === 'true',\n        logLevel: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',\n        showDevTools: \"development\" === 'development'\n    },\n    // Feature Flags\n    features: {\n        enableWebSocket: true,\n        enableFileUpload: true,\n        enableVoiceInput: false,\n        enableDarkMode: true,\n        enableNotifications: true,\n        enableAnalytics: true,\n        enableMultiTenant: true,\n        enableWorkflows: true,\n        enableTools: true\n    },\n    // Performance Configuration\n    performance: {\n        enableServiceWorker: true,\n        enableImageOptimization: true,\n        enableCodeSplitting: true,\n        enablePrefetching: true,\n        cacheTimeout: 300000\n    },\n    // Monitoring Configuration\n    monitoring: {\n        enableErrorTracking: true,\n        enablePerformanceTracking: true,\n        enableUserTracking: false,\n        sampleRate: 0.1\n    },\n    // Security Configuration\n    security: {\n        enableCSP: true,\n        enableXSSProtection: true,\n        enableFrameGuard: true,\n        enableHSTS: true,\n        cookieSecure: \"development\" === 'production',\n        cookieSameSite: 'strict'\n    }\n};\n// Type-safe environment variable access\nconst getEnvVar = (key, defaultValue)=>{\n    const value = process.env[key];\n    if (!value && !defaultValue) {\n        throw new Error(`Environment variable ${key} is required but not set`);\n    }\n    return value || defaultValue || '';\n};\n// Validate required environment variables\nconst validateConfig = ()=>{\n    const requiredVars = [\n        'NEXT_PUBLIC_API_URL',\n        'NEXT_PUBLIC_WS_URL'\n    ];\n    const missing = requiredVars.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        console.warn(`Missing environment variables: ${missing.join(', ')}`);\n        console.warn('Using default values. This may cause issues in production.');\n    }\n};\n// Initialize configuration validation\nif (true) {\n    // Only validate on server-side to avoid hydration issues\n    validateConfig();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0JTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3Qtc2FucyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0U2FucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDbGFyYWdvbiU1QyU1Q3d3dyU1QyU1Q01DUCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJHZWlzdF9Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3QtbW9ubyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0TW9ubyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDbGFyYWdvbiU1QyU1Q3d3dyU1QyU1Q01DUCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDcmVhY3QtaG90LXRvYXN0JTVDJTVDZGlzdCU1QyU1Q2luZGV4Lm1qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2xhcmFnb24lNUMlNUN3d3clNUMlNUNNQ1AlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBaUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBcUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXE1DUFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDTUNQJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQW1HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxNQ1BcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CMCP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentTextIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Chat',\n        href: '/dashboard/chat',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'AI Providers',\n        href: '/dashboard/providers',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'Documents',\n        href: '/dashboard/documents',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Workflows',\n        href: '/dashboard/workflows',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'Team',\n        href: '/dashboard/team',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'Settings',\n        href: '/dashboard/settings',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    }\n];\nfunction DashboardLayout({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            // Check authentication\n            const token = localStorage.getItem('auth_token');\n            const userData = localStorage.getItem('user');\n            if (!token) {\n                router.push('/login');\n                return;\n            }\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n        }\n    }[\"DashboardLayout.useEffect\"], [\n        router\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('user');\n        router.push('/login');\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 66,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 65,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex overflow-hidden bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-0 pt-5 pb-4 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center px-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                            children: _lib_config__WEBPACK_IMPORTED_MODULE_4__.config.app.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"mt-5 px-2 space-y-1\",\n                                        children: navigation.map((item)=>{\n                                            const isActive = pathname === item.href;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: `group flex items-center px-2 py-2 text-base font-medium rounded-md ${isActive ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: `mr-4 flex-shrink-0 h-6 w-6 ${isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    item.name\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 37\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 74,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-shrink-0 px-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                            children: _lib_config__WEBPACK_IMPORTED_MODULE_4__.config.app.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"mt-5 flex-1 px-2 bg-white space-y-1\",\n                                        children: navigation.map((item)=>{\n                                            const isActive = pathname === item.href;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md ${isActive ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: `mr-3 flex-shrink-0 h-6 w-6 ${isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    item.name\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 41\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 flex border-t border-gray-200 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline-block h-9 w-9 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-700 group-hover:text-gray-900\",\n                                                    children: user?.name || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-medium text-gray-500 group-hover:text-gray-700\",\n                                                    children: user?.email || ''\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 118,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-0 flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 md:hidden\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 px-4 flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full flex md:ml-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full text-gray-400 focus-within:text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 flex items-center pointer-events-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        className: \"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent sm:text-sm\",\n                                                        placeholder: \"Search...\",\n                                                        type: \"search\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 flex items-center md:ml-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3 relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hidden md:block\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: [\n                                                                        user.first_name,\n                                                                        \" \",\n                                                                        user.last_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 41\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 41\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                                                            title: \"Logout\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_CpuChipIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 relative overflow-y-auto focus:outline-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 169,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 72,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,DocumentTextIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,DocumentTextIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,DocumentTextIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,DocumentTextIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,DocumentTextIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,DocumentTextIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,DocumentTextIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,DocumentTextIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalChats: 0,\n        activeProviders: 0,\n        documentsProcessed: 0,\n        workflowsRunning: 0,\n        responseTime: 0,\n        successRate: 0,\n        errorRate: 0,\n        tokensUsed: 0\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            // Simulate loading dashboard data\n            const loadDashboardData = {\n                \"DashboardPage.useEffect.loadDashboardData\": async ()=>{\n                    try {\n                        // TODO: Replace with actual API calls\n                        await new Promise({\n                            \"DashboardPage.useEffect.loadDashboardData\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"DashboardPage.useEffect.loadDashboardData\"]);\n                        setStats({\n                            totalChats: 1247,\n                            activeProviders: 8,\n                            documentsProcessed: 342,\n                            workflowsRunning: 12,\n                            responseTime: 1.2,\n                            successRate: 98.5,\n                            errorRate: 1.5,\n                            tokensUsed: 2847392\n                        });\n                        setRecentActivity([\n                            {\n                                id: '1',\n                                type: 'chat',\n                                title: 'New chat session started',\n                                description: 'User initiated conversation with GPT-4',\n                                timestamp: '2 minutes ago',\n                                status: 'success'\n                            },\n                            {\n                                id: '2',\n                                type: 'workflow',\n                                title: 'Document analysis workflow completed',\n                                description: 'Processed 15 documents with Claude-3',\n                                timestamp: '5 minutes ago',\n                                status: 'success'\n                            },\n                            {\n                                id: '3',\n                                type: 'provider',\n                                title: 'OpenAI API rate limit warning',\n                                description: 'Approaching rate limit for GPT-4 model',\n                                timestamp: '12 minutes ago',\n                                status: 'error'\n                            },\n                            {\n                                id: '4',\n                                type: 'document',\n                                title: 'Document uploaded',\n                                description: 'New PDF document added to knowledge base',\n                                timestamp: '18 minutes ago',\n                                status: 'success'\n                            },\n                            {\n                                id: '5',\n                                type: 'workflow',\n                                title: 'Workflow execution started',\n                                description: 'Multi-step analysis workflow initiated',\n                                timestamp: '25 minutes ago',\n                                status: 'pending'\n                            }\n                        ]);\n                    } catch (error) {\n                        console.error('Error loading dashboard data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.loadDashboardData\"];\n            loadDashboardData();\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case 'chat':\n                return _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case 'workflow':\n                return _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case 'document':\n                return _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case 'provider':\n                return _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 24\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 24\n                }, this);\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 24\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 24\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Welcome back! Here's what's happening with your AI orchestration platform.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Total Chats\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"flex items-baseline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-semibold text-gray-900\",\n                                                            children: stats.totalChats.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-2 flex items-baseline text-sm font-semibold text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"self-center flex-shrink-0 h-4 w-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Increased by\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                \"12%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Active Providers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"flex items-baseline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-semibold text-gray-900\",\n                                                            children: stats.activeProviders\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-2 flex items-baseline text-sm font-semibold text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"self-center flex-shrink-0 h-4 w-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"All operational\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                \"Online\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Documents Processed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"flex items-baseline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-semibold text-gray-900\",\n                                                            children: stats.documentsProcessed.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-2 flex items-baseline text-sm font-semibold text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"self-center flex-shrink-0 h-4 w-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Increased by\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                \"8%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-8 w-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Active Workflows\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"flex items-baseline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-semibold text-gray-900\",\n                                                            children: stats.workflowsRunning\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-2 flex items-baseline text-sm font-semibold text-yellow-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"self-center flex-shrink-0 h-4 w-4 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Running\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                \"Running\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-5 lg:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Performance Metrics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Average Response Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                    children: [\n                                                        stats.responseTime,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Success Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-green-600\",\n                                                    children: [\n                                                        stats.successRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Error Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-red-600\",\n                                                    children: [\n                                                        stats.errorRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Tokens Used (Today)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                    children: stats.tokensUsed.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Recent Activity\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flow-root\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"-mb-8\",\n                                        children: recentActivity.map((activity, activityIdx)=>{\n                                            const Icon = getActivityIcon(activity.type);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative pb-8\",\n                                                    children: [\n                                                        activityIdx !== recentActivity.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 53\n                                                        }, this) : null,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center ring-8 ring-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                            className: \"h-4 w-4 text-gray-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 53\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-900\",\n                                                                                    children: activity.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 330,\n                                                                                    columnNumber: 61\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: activity.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 61\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 57\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right text-sm whitespace-nowrap text-gray-500 flex items-center space-x-2\",\n                                                                            children: [\n                                                                                getStatusIcon(activity.status),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: activity.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 335,\n                                                                                    columnNumber: 61\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 57\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 45\n                                                }, this)\n                                            }, activity.id, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 41\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/dashboard/chat'),\n                                    className: \"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute inset-0\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        \"Start New Chat\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-2 text-sm text-gray-500\",\n                                                    children: \"Begin a new conversation with AI providers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/dashboard/providers'),\n                                    className: \"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute inset-0\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        \"Configure Providers\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-2 text-sm text-gray-500\",\n                                                    children: \"Manage AI provider settings and API keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/dashboard/documents'),\n                                    className: \"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute inset-0\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        \"Upload Documents\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-2 text-sm text-gray-500\",\n                                                    children: \"Add documents to your knowledge base\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/dashboard/workflows'),\n                                    className: \"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"rounded-lg inline-flex p-3 bg-orange-50 text-orange-700 ring-4 ring-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_DocumentTextIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute inset-0\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        \"Create Workflow\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-2 text-sm text-gray-500\",\n                                                    children: \"Build automated AI workflows\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 350,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(ssr)/./src/utils/index.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref)=>{\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';\n    const variants = {\n        primary: 'bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-600',\n        secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus-visible:ring-gray-500',\n        outline: 'border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus-visible:ring-gray-500',\n        ghost: 'bg-transparent text-gray-700 hover:bg-gray-100 focus-visible:ring-gray-500',\n        danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-600'\n    };\n    const sizes = {\n        sm: 'h-8 px-3 text-sm',\n        md: 'h-10 px-4 text-sm',\n        lg: 'h-12 px-6 text-base'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], loading && 'cursor-not-allowed', className),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 43,\n                columnNumber: 21\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 30,\n        columnNumber: 13\n    }, undefined);\n});\nButton.displayName = 'Button';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(ssr)/./src/utils/index.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, padding = 'md', shadow = 'sm', ...props }, ref)=>{\n    const paddingClasses = {\n        none: '',\n        sm: 'p-3',\n        md: 'p-6',\n        lg: 'p-8'\n    };\n    const shadowClasses = {\n        none: '',\n        sm: 'shadow-sm',\n        md: 'shadow-md',\n        lg: 'shadow-lg'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-white rounded-lg border border-gray-200', paddingClasses[padding], shadowClasses[shadow], className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 27,\n        columnNumber: 13\n    }, undefined);\n});\nCard.displayName = 'Card';\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex flex-col space-y-1.5 pb-6', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 53,\n        columnNumber: 13\n    }, undefined);\n});\nCardHeader.displayName = 'CardHeader';\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, as: Component = 'h3', ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ref: ref,\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('text-lg font-semibold leading-none tracking-tight', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 75,\n        columnNumber: 13\n    }, undefined);\n});\nCardTitle.displayName = 'CardTitle';\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('text-sm text-gray-600', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 96,\n        columnNumber: 13\n    }, undefined);\n});\nCardDescription.displayName = 'CardDescription';\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('pt-0', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 117,\n        columnNumber: 13\n    }, undefined);\n});\nCardContent.displayName = 'CardContent';\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center pt-6', className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 138,\n        columnNumber: 13\n    }, undefined);\n});\nCardFooter.displayName = 'CardFooter';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDRztBQVE3QixNQUFNRSxxQkFBT0YsdURBQWdCLENBQ3pCLENBQUMsRUFBRUksU0FBUyxFQUFFQyxRQUFRLEVBQUVDLFVBQVUsSUFBSSxFQUFFQyxTQUFTLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQy9ELE1BQU1DLGlCQUFpQjtRQUNuQkMsTUFBTTtRQUNOQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNSO0lBRUEsTUFBTUMsZ0JBQWdCO1FBQ2xCSixNQUFNO1FBQ05DLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ1I7SUFFQSxxQkFDSSw4REFBQ0U7UUFDR1AsS0FBS0E7UUFDTEwsV0FBV0gsMENBQUVBLENBQ1QsOENBQ0FTLGNBQWMsQ0FBQ0osUUFBUSxFQUN2QlMsYUFBYSxDQUFDUixPQUFPLEVBQ3JCSDtRQUVILEdBQUdJLEtBQUs7a0JBRVJIOzs7Ozs7QUFHYjtBQUdKSCxLQUFLZSxXQUFXLEdBQUc7QUFPbkIsTUFBTUMsMkJBQWFsQix1REFBZ0IsQ0FDL0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLFFBQVEsRUFBRSxHQUFHRyxPQUFPLEVBQUVDO0lBQ2hDLHFCQUNJLDhEQUFDTztRQUNHUCxLQUFLQTtRQUNMTCxXQUFXSCwwQ0FBRUEsQ0FBQyxrQ0FBa0NHO1FBQy9DLEdBQUdJLEtBQUs7a0JBRVJIOzs7Ozs7QUFHYjtBQUdKYSxXQUFXRCxXQUFXLEdBQUc7QUFRekIsTUFBTUUsMEJBQVluQix1REFBZ0IsQ0FDOUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLFFBQVEsRUFBRWUsSUFBSUMsWUFBWSxJQUFJLEVBQUUsR0FBR2IsT0FBTyxFQUFFQztJQUN0RCxxQkFDSSw4REFBQ1k7UUFDR1osS0FBS0E7UUFDTEwsV0FBV0gsMENBQUVBLENBQUMscURBQXFERztRQUNsRSxHQUFHSSxLQUFLO2tCQUVSSDs7Ozs7O0FBR2I7QUFHSmMsVUFBVUYsV0FBVyxHQUFHO0FBT3hCLE1BQU1LLGdDQUFrQnRCLHVEQUFnQixDQUNwQyxDQUFDLEVBQUVJLFNBQVMsRUFBRUMsUUFBUSxFQUFFLEdBQUdHLE9BQU8sRUFBRUM7SUFDaEMscUJBQ0ksOERBQUNjO1FBQ0dkLEtBQUtBO1FBQ0xMLFdBQVdILDBDQUFFQSxDQUFDLHlCQUF5Qkc7UUFDdEMsR0FBR0ksS0FBSztrQkFFUkg7Ozs7OztBQUdiO0FBR0ppQixnQkFBZ0JMLFdBQVcsR0FBRztBQU85QixNQUFNTyw0QkFBY3hCLHVEQUFnQixDQUNoQyxDQUFDLEVBQUVJLFNBQVMsRUFBRUMsUUFBUSxFQUFFLEdBQUdHLE9BQU8sRUFBRUM7SUFDaEMscUJBQ0ksOERBQUNPO1FBQ0dQLEtBQUtBO1FBQ0xMLFdBQVdILDBDQUFFQSxDQUFDLFFBQVFHO1FBQ3JCLEdBQUdJLEtBQUs7a0JBRVJIOzs7Ozs7QUFHYjtBQUdKbUIsWUFBWVAsV0FBVyxHQUFHO0FBTzFCLE1BQU1RLDJCQUFhekIsdURBQWdCLENBQy9CLENBQUMsRUFBRUksU0FBUyxFQUFFQyxRQUFRLEVBQUUsR0FBR0csT0FBTyxFQUFFQztJQUNoQyxxQkFDSSw4REFBQ087UUFDR1AsS0FBS0E7UUFDTEwsV0FBV0gsMENBQUVBLENBQUMsMEJBQTBCRztRQUN2QyxHQUFHSSxLQUFLO2tCQUVSSDs7Ozs7O0FBR2I7QUFHSm9CLFdBQVdSLFdBQVcsR0FBRztBQUV6QixpRUFBZWYsSUFBSUEsRUFBQztBQUN1RCIsInNvdXJjZXMiOlsiQzpcXGxhcmFnb25cXHd3d1xcTUNQXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcQ2FyZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgY24gfSBmcm9tICdAL3V0aWxzJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQ2FyZFByb3BzIGV4dGVuZHMgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+IHtcclxuICAgIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbiAgICBwYWRkaW5nPzogJ25vbmUnIHwgJ3NtJyB8ICdtZCcgfCAnbGcnO1xyXG4gICAgc2hhZG93PzogJ25vbmUnIHwgJ3NtJyB8ICdtZCcgfCAnbGcnO1xyXG59XHJcblxyXG5jb25zdCBDYXJkID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MRGl2RWxlbWVudCwgQ2FyZFByb3BzPihcclxuICAgICh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIHBhZGRpbmcgPSAnbWQnLCBzaGFkb3cgPSAnc20nLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgICAgICBjb25zdCBwYWRkaW5nQ2xhc3NlcyA9IHtcclxuICAgICAgICAgICAgbm9uZTogJycsXHJcbiAgICAgICAgICAgIHNtOiAncC0zJyxcclxuICAgICAgICAgICAgbWQ6ICdwLTYnLFxyXG4gICAgICAgICAgICBsZzogJ3AtOCcsXHJcbiAgICAgICAgfTtcclxuXHJcbiAgICAgICAgY29uc3Qgc2hhZG93Q2xhc3NlcyA9IHtcclxuICAgICAgICAgICAgbm9uZTogJycsXHJcbiAgICAgICAgICAgIHNtOiAnc2hhZG93LXNtJyxcclxuICAgICAgICAgICAgbWQ6ICdzaGFkb3ctbWQnLFxyXG4gICAgICAgICAgICBsZzogJ3NoYWRvdy1sZycsXHJcbiAgICAgICAgfTtcclxuXHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICdiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAnLFxyXG4gICAgICAgICAgICAgICAgICAgIHBhZGRpbmdDbGFzc2VzW3BhZGRpbmddLFxyXG4gICAgICAgICAgICAgICAgICAgIHNoYWRvd0NsYXNzZXNbc2hhZG93XSxcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWVcclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKTtcclxuICAgIH1cclxuKTtcclxuXHJcbkNhcmQuZGlzcGxheU5hbWUgPSAnQ2FyZCc7XHJcblxyXG4vLyBDYXJkIEhlYWRlciBDb21wb25lbnRcclxuZXhwb3J0IGludGVyZmFjZSBDYXJkSGVhZGVyUHJvcHMgZXh0ZW5kcyBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4ge1xyXG4gICAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufVxyXG5cclxuY29uc3QgQ2FyZEhlYWRlciA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTERpdkVsZW1lbnQsIENhcmRIZWFkZXJQcm9wcz4oXHJcbiAgICAoeyBjbGFzc05hbWUsIGNoaWxkcmVuLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oJ2ZsZXggZmxleC1jb2wgc3BhY2UteS0xLjUgcGItNicsIGNsYXNzTmFtZSl9XHJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKTtcclxuICAgIH1cclxuKTtcclxuXHJcbkNhcmRIZWFkZXIuZGlzcGxheU5hbWUgPSAnQ2FyZEhlYWRlcic7XHJcblxyXG4vLyBDYXJkIFRpdGxlIENvbXBvbmVudFxyXG5leHBvcnQgaW50ZXJmYWNlIENhcmRUaXRsZVByb3BzIGV4dGVuZHMgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PiB7XHJcbiAgICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG4gICAgYXM/OiAnaDEnIHwgJ2gyJyB8ICdoMycgfCAnaDQnIHwgJ2g1JyB8ICdoNic7XHJcbn1cclxuXHJcbmNvbnN0IENhcmRUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTEhlYWRpbmdFbGVtZW50LCBDYXJkVGl0bGVQcm9wcz4oXHJcbiAgICAoeyBjbGFzc05hbWUsIGNoaWxkcmVuLCBhczogQ29tcG9uZW50ID0gJ2gzJywgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgPENvbXBvbmVudFxyXG4gICAgICAgICAgICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKCd0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbGVhZGluZy1ub25lIHRyYWNraW5nLXRpZ2h0JywgY2xhc3NOYW1lKX1cclxuICAgICAgICAgICAgICAgIHsuLi5wcm9wc31cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgICA8L0NvbXBvbmVudD5cclxuICAgICAgICApO1xyXG4gICAgfVxyXG4pO1xyXG5cclxuQ2FyZFRpdGxlLmRpc3BsYXlOYW1lID0gJ0NhcmRUaXRsZSc7XHJcblxyXG4vLyBDYXJkIERlc2NyaXB0aW9uIENvbXBvbmVudFxyXG5leHBvcnQgaW50ZXJmYWNlIENhcmREZXNjcmlwdGlvblByb3BzIGV4dGVuZHMgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+IHtcclxuICAgIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn1cclxuXHJcbmNvbnN0IENhcmREZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTFBhcmFncmFwaEVsZW1lbnQsIENhcmREZXNjcmlwdGlvblByb3BzPihcclxuICAgICh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4ge1xyXG4gICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgIDxwXHJcbiAgICAgICAgICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oJ3RleHQtc20gdGV4dC1ncmF5LTYwMCcsIGNsYXNzTmFtZSl9XHJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICk7XHJcbiAgICB9XHJcbik7XHJcblxyXG5DYXJkRGVzY3JpcHRpb24uZGlzcGxheU5hbWUgPSAnQ2FyZERlc2NyaXB0aW9uJztcclxuXHJcbi8vIENhcmQgQ29udGVudCBDb21wb25lbnRcclxuZXhwb3J0IGludGVyZmFjZSBDYXJkQ29udGVudFByb3BzIGV4dGVuZHMgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+IHtcclxuICAgIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn1cclxuXHJcbmNvbnN0IENhcmRDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MRGl2RWxlbWVudCwgQ2FyZENvbnRlbnRQcm9wcz4oXHJcbiAgICAoeyBjbGFzc05hbWUsIGNoaWxkcmVuLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oJ3B0LTAnLCBjbGFzc05hbWUpfVxyXG4gICAgICAgICAgICAgICAgey4uLnByb3BzfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICk7XHJcbiAgICB9XHJcbik7XHJcblxyXG5DYXJkQ29udGVudC5kaXNwbGF5TmFtZSA9ICdDYXJkQ29udGVudCc7XHJcblxyXG4vLyBDYXJkIEZvb3RlciBDb21wb25lbnRcclxuZXhwb3J0IGludGVyZmFjZSBDYXJkRm9vdGVyUHJvcHMgZXh0ZW5kcyBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4ge1xyXG4gICAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufVxyXG5cclxuY29uc3QgQ2FyZEZvb3RlciA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTERpdkVsZW1lbnQsIENhcmRGb290ZXJQcm9wcz4oXHJcbiAgICAoeyBjbGFzc05hbWUsIGNoaWxkcmVuLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oJ2ZsZXggaXRlbXMtY2VudGVyIHB0LTYnLCBjbGFzc05hbWUpfVxyXG4gICAgICAgICAgICAgICAgey4uLnByb3BzfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICk7XHJcbiAgICB9XHJcbik7XHJcblxyXG5DYXJkRm9vdGVyLmRpc3BsYXlOYW1lID0gJ0NhcmRGb290ZXInO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ2FyZDtcclxuZXhwb3J0IHsgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24sIENhcmRDb250ZW50LCBDYXJkRm9vdGVyIH07ICJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJjaGlsZHJlbiIsInBhZGRpbmciLCJzaGFkb3ciLCJwcm9wcyIsInJlZiIsInBhZGRpbmdDbGFzc2VzIiwibm9uZSIsInNtIiwibWQiLCJsZyIsInNoYWRvd0NsYXNzZXMiLCJkaXYiLCJkaXNwbGF5TmFtZSIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJhcyIsIkNvbXBvbmVudCIsIkNhcmREZXNjcmlwdGlvbiIsInAiLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(ssr)/./src/utils/index.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, label, error, helperText, leftIcon, rightIcon, variant = 'default', inputSize = 'md', id, ...props }, ref)=>{\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n    const sizeClasses = {\n        sm: 'h-9 text-sm',\n        md: 'h-11 text-sm',\n        lg: 'h-12 text-base'\n    };\n    const paddingClasses = {\n        sm: leftIcon ? 'pl-9' : 'pl-3',\n        md: leftIcon ? 'pl-10' : 'pl-4',\n        lg: leftIcon ? 'pl-12' : 'pl-4'\n    };\n    const rightPaddingClasses = {\n        sm: rightIcon ? 'pr-9' : 'pr-3',\n        md: rightIcon ? 'pr-10' : 'pr-4',\n        lg: rightIcon ? 'pr-12' : 'pr-4'\n    };\n    const iconSizes = {\n        sm: 'h-4 w-4',\n        md: 'h-5 w-5',\n        lg: 'h-6 w-6'\n    };\n    const iconPositions = {\n        sm: leftIcon ? 'left-2.5' : '',\n        md: leftIcon ? 'left-3' : '',\n        lg: leftIcon ? 'left-3' : ''\n    };\n    const rightIconPositions = {\n        sm: rightIcon ? 'right-2.5' : '',\n        md: rightIcon ? 'right-3' : '',\n        lg: rightIcon ? 'right-3' : ''\n    };\n    const getVariantClasses = ()=>{\n        const baseClasses = `\n                block w-full rounded-lg border transition-all duration-200 ease-in-out\n                placeholder:text-gray-400 \n                focus:outline-none focus:ring-2 focus:ring-offset-0\n                disabled:cursor-not-allowed disabled:opacity-50\n            `;\n        switch(variant){\n            case 'filled':\n                return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, 'border-transparent bg-gray-50', 'hover:bg-gray-100', 'focus:bg-white focus:border-blue-500 focus:ring-blue-500/20', error && 'bg-red-50 border-red-200 focus:border-red-500 focus:ring-red-500/20');\n            case 'outlined':\n                return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, 'border-2 border-gray-200 bg-transparent', 'hover:border-gray-300', 'focus:border-blue-500 focus:ring-blue-500/20', error && 'border-red-300 focus:border-red-500 focus:ring-red-500/20');\n            default:\n                return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, 'border-gray-300 bg-white shadow-sm', 'hover:border-gray-400', 'focus:border-blue-500 focus:ring-blue-500/20', error && 'border-red-300 focus:border-red-500 focus:ring-red-500/20');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: inputId,\n                className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('block text-sm font-medium mb-2 transition-colors', error ? 'text-red-700' : 'text-gray-700', 'hover:text-gray-900'),\n                children: [\n                    label,\n                    props.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 ml-1\",\n                        \"aria-label\": \"required\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 29\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 104,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative group\",\n                children: [\n                    leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('absolute inset-y-0 flex items-center pointer-events-none z-10', iconPositions[inputSize]),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(iconSizes[inputSize], 'transition-colors duration-200', error ? 'text-red-400' : 'text-gray-400', 'group-focus-within:text-blue-500'),\n                            children: leftIcon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        id: inputId,\n                        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(getVariantClasses(), sizeClasses[inputSize], paddingClasses[inputSize], rightPaddingClasses[inputSize], className),\n                        ref: ref,\n                        \"aria-invalid\": error ? 'true' : 'false',\n                        \"aria-describedby\": error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined,\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 21\n                    }, undefined),\n                    rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('absolute inset-y-0 flex items-center z-10', rightIconPositions[inputSize], props.type === 'password' ? 'cursor-pointer' : 'pointer-events-none'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(iconSizes[inputSize], 'transition-colors duration-200', error ? 'text-red-400' : 'text-gray-400', props.type === 'password' ? 'hover:text-gray-600' : 'group-focus-within:text-blue-500'),\n                            children: rightIcon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 119,\n                columnNumber: 17\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                id: `${inputId}-error`,\n                className: \"mt-2 text-sm text-red-600 flex items-center animate-in slide-in-from-top-1 duration-200\",\n                role: \"alert\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-4 w-4 mr-1.5 flex-shrink-0\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 25\n                    }, undefined),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 173,\n                columnNumber: 21\n            }, undefined),\n            helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                id: `${inputId}-helper`,\n                className: \"mt-2 text-sm text-gray-500 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-4 w-4 mr-1.5 flex-shrink-0 text-gray-400\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 25\n                    }, undefined),\n                    helperText\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 186,\n                columnNumber: 21\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\MCP\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n        lineNumber: 102,\n        columnNumber: 13\n    }, undefined);\n});\nInput.displayName = 'Input';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Input);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/index.ts":
/*!************************************!*\
  !*** ./src/components/ui/index.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* reexport safe */ _Button__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Card: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   CardContent: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_2__.CardContent),\n/* harmony export */   CardDescription: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription),\n/* harmony export */   CardFooter: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_2__.CardFooter),\n/* harmony export */   CardHeader: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader),\n/* harmony export */   CardTitle: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle),\n/* harmony export */   Input: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _Input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Input */ \"(ssr)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n// UI Components Export\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFBLHVCQUF1QjtBQUNzQjtBQUdGO0FBVTNCIiwic291cmNlcyI6WyJDOlxcbGFyYWdvblxcd3d3XFxNQ1BcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBVSSBDb21wb25lbnRzIEV4cG9ydFxyXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJ1dHRvbiB9IGZyb20gJy4vQnV0dG9uJztcclxuZXhwb3J0IHR5cGUgeyBCdXR0b25Qcm9wcyB9IGZyb20gJy4vQnV0dG9uJztcclxuXHJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSW5wdXQgfSBmcm9tICcuL0lucHV0JztcclxuZXhwb3J0IHR5cGUgeyBJbnB1dFByb3BzIH0gZnJvbSAnLi9JbnB1dCc7XHJcblxyXG5leHBvcnQge1xyXG4gICAgZGVmYXVsdCBhcyBDYXJkLFxyXG4gICAgQ2FyZEhlYWRlcixcclxuICAgIENhcmRUaXRsZSxcclxuICAgIENhcmREZXNjcmlwdGlvbixcclxuICAgIENhcmRDb250ZW50LFxyXG4gICAgQ2FyZEZvb3RlclxyXG59IGZyb20gJy4vQ2FyZCc7XHJcbmV4cG9ydCB0eXBlIHtcclxuICAgIENhcmRQcm9wcyxcclxuICAgIENhcmRIZWFkZXJQcm9wcyxcclxuICAgIENhcmRUaXRsZVByb3BzLFxyXG4gICAgQ2FyZERlc2NyaXB0aW9uUHJvcHMsXHJcbiAgICBDYXJkQ29udGVudFByb3BzLFxyXG4gICAgQ2FyZEZvb3RlclByb3BzXHJcbn0gZnJvbSAnLi9DYXJkJzsgIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJCdXR0b24iLCJJbnB1dCIsIkNhcmQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZENvbnRlbnQiLCJDYXJkRm9vdGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getEnvVar: () => (/* binding */ getEnvVar),\n/* harmony export */   validateConfig: () => (/* binding */ validateConfig)\n/* harmony export */ });\n// Axient MCP++ Frontend Configuration\nconst config = {\n    // API Configuration\n    api: {\n        baseUrl: \"http://localhost:8000/api\" || 0,\n        timeout: 30000,\n        retries: 3\n    },\n    // WebSocket Configuration\n    websocket: {\n        url: \"ws://localhost:6001\" || 0,\n        reconnectAttempts: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_ATTEMPTS || '5'),\n        reconnectDelay: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_DELAY || '1000'),\n        heartbeatInterval: 30000\n    },\n    // Application Configuration\n    app: {\n        name: \"Axient MCP++\" || 0,\n        version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n        description: 'Universal AI Orchestration Platform',\n        url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'\n    },\n    // Authentication Configuration\n    auth: {\n        cookieName: process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME || 'axient_token',\n        sessionTimeout: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT || '3600000'),\n        refreshThreshold: 300000\n    },\n    // AI Provider Configuration\n    ai: {\n        defaultProvider: process.env.NEXT_PUBLIC_DEFAULT_AI_PROVIDER || 'openai',\n        defaultMaxTokens: parseInt(process.env.NEXT_PUBLIC_MAX_TOKENS_DEFAULT || '1000'),\n        defaultTemperature: parseFloat(process.env.NEXT_PUBLIC_TEMPERATURE_DEFAULT || '0.7'),\n        streamingEnabled: true\n    },\n    // Chat Configuration\n    chat: {\n        maxMessageLength: parseInt(process.env.NEXT_PUBLIC_MAX_MESSAGE_LENGTH || '4000'),\n        historyLimit: parseInt(process.env.NEXT_PUBLIC_CHAT_HISTORY_LIMIT || '100'),\n        typingIndicatorTimeout: parseInt(process.env.NEXT_PUBLIC_TYPING_INDICATOR_TIMEOUT || '3000'),\n        autoSaveInterval: 30000\n    },\n    // File Upload Configuration\n    upload: {\n        maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'),\n        allowedTypes: (process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || '.pdf,.txt,.doc,.docx,.md').split(','),\n        chunkSize: 1024 * 1024\n    },\n    // UI Configuration\n    ui: {\n        theme: {\n            primary: '#3B82F6',\n            secondary: '#10B981',\n            accent: '#F59E0B',\n            error: '#EF4444',\n            warning: '#F59E0B',\n            success: '#10B981',\n            info: '#3B82F6'\n        },\n        animations: {\n            duration: 200,\n            easing: 'ease-in-out'\n        },\n        breakpoints: {\n            sm: '640px',\n            md: '768px',\n            lg: '1024px',\n            xl: '1280px',\n            '2xl': '1536px'\n        }\n    },\n    // Development Configuration\n    dev: {\n        debugMode: \"true\" === 'true',\n        logLevel: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',\n        showDevTools: \"development\" === 'development'\n    },\n    // Feature Flags\n    features: {\n        enableWebSocket: true,\n        enableFileUpload: true,\n        enableVoiceInput: false,\n        enableDarkMode: true,\n        enableNotifications: true,\n        enableAnalytics: true,\n        enableMultiTenant: true,\n        enableWorkflows: true,\n        enableTools: true\n    },\n    // Performance Configuration\n    performance: {\n        enableServiceWorker: true,\n        enableImageOptimization: true,\n        enableCodeSplitting: true,\n        enablePrefetching: true,\n        cacheTimeout: 300000\n    },\n    // Monitoring Configuration\n    monitoring: {\n        enableErrorTracking: true,\n        enablePerformanceTracking: true,\n        enableUserTracking: false,\n        sampleRate: 0.1\n    },\n    // Security Configuration\n    security: {\n        enableCSP: true,\n        enableXSSProtection: true,\n        enableFrameGuard: true,\n        enableHSTS: true,\n        cookieSecure: \"development\" === 'production',\n        cookieSameSite: 'strict'\n    }\n};\n// Type-safe environment variable access\nconst getEnvVar = (key, defaultValue)=>{\n    const value = process.env[key];\n    if (!value && !defaultValue) {\n        throw new Error(`Environment variable ${key} is required but not set`);\n    }\n    return value || defaultValue || '';\n};\n// Validate required environment variables\nconst validateConfig = ()=>{\n    const requiredVars = [\n        'NEXT_PUBLIC_API_URL',\n        'NEXT_PUBLIC_WS_URL'\n    ];\n    const missing = requiredVars.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        console.warn(`Missing environment variables: ${missing.join(', ')}`);\n        console.warn('Using default values. This may cause issues in production.');\n    }\n};\n// Initialize configuration validation\nif (true) {\n    // Only validate on server-side to avoid hydration issues\n    validateConfig();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   camelToTitle: () => (/* binding */ camelToTitle),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   downloadFile: () => (/* binding */ downloadFile),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   formatTokens: () => (/* binding */ formatTokens),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStorageItem: () => (/* binding */ getStorageItem),\n/* harmony export */   groupBy: () => (/* binding */ groupBy),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isNetworkError: () => (/* binding */ isNetworkError),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidJson: () => (/* binding */ isValidJson),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   removeStorageItem: () => (/* binding */ removeStorageItem),\n/* harmony export */   retry: () => (/* binding */ retry),\n/* harmony export */   setStorageItem: () => (/* binding */ setStorageItem),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   sortBy: () => (/* binding */ sortBy),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   titleCase: () => (/* binding */ titleCase),\n/* harmony export */   truncate: () => (/* binding */ truncate),\n/* harmony export */   unique: () => (/* binding */ unique),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validateName: () => (/* binding */ validateName),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validatePasswordConfirmation: () => (/* binding */ validatePasswordConfirmation),\n/* harmony export */   validateRequired: () => (/* binding */ validateRequired)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isValid,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/parseISO.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isValid,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/isValid.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isValid,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isValid,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/formatDistanceToNow.js\");\n// Axient MCP++ Frontend Utilities\n\n\n\n// ============================================================================\n// CSS & Styling Utilities\n// ============================================================================\n/**\r\n * Combines class names with Tailwind CSS merge\r\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Generate consistent color classes for status indicators\r\n */ function getStatusColor(status) {\n    const statusColors = {\n        active: 'text-green-600 bg-green-100',\n        inactive: 'text-gray-600 bg-gray-100',\n        error: 'text-red-600 bg-red-100',\n        warning: 'text-yellow-600 bg-yellow-100',\n        pending: 'text-blue-600 bg-blue-100',\n        processing: 'text-purple-600 bg-purple-100',\n        completed: 'text-green-600 bg-green-100',\n        failed: 'text-red-600 bg-red-100',\n        healthy: 'text-green-600 bg-green-100',\n        unhealthy: 'text-red-600 bg-red-100',\n        degraded: 'text-yellow-600 bg-yellow-100'\n    };\n    return statusColors[status.toLowerCase()] || 'text-gray-600 bg-gray-100';\n}\n/**\r\n * Generate avatar initials from name\r\n */ function getInitials(name) {\n    return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n}\n// ============================================================================\n// Date & Time Utilities\n// ============================================================================\n/**\r\n * Format date with fallback for invalid dates\r\n */ function formatDate(date, formatStr = 'PPP') {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(dateObj)) return 'Invalid date';\n        return (0,_barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_4__.format)(dateObj, formatStr);\n    } catch  {\n        return 'Invalid date';\n    }\n}\n/**\r\n * Format relative time (e.g., \"2 hours ago\")\r\n */ function formatRelativeTime(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(dateObj)) return 'Invalid date';\n        return (0,_barrel_optimize_names_format_formatDistanceToNow_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_5__.formatDistanceToNow)(dateObj, {\n            addSuffix: true\n        });\n    } catch  {\n        return 'Invalid date';\n    }\n}\n/**\r\n * Format duration in milliseconds to human readable\r\n */ function formatDuration(ms) {\n    if (ms < 1000) return `${ms}ms`;\n    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;\n    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;\n    return `${(ms / 3600000).toFixed(1)}h`;\n}\n// ============================================================================\n// String Utilities\n// ============================================================================\n/**\r\n * Truncate text with ellipsis\r\n */ function truncate(text, length) {\n    if (text.length <= length) return text;\n    return text.slice(0, length) + '...';\n}\n/**\r\n * Convert string to slug format\r\n */ function slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '').replace(/[\\s_-]+/g, '-').replace(/^-+|-+$/g, '');\n}\n/**\r\n * Capitalize first letter of each word\r\n */ function titleCase(text) {\n    return text.replace(/\\w\\S*/g, (txt)=>txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());\n}\n/**\r\n * Convert camelCase to Title Case\r\n */ function camelToTitle(text) {\n    return text.replace(/([A-Z])/g, ' $1').replace(/^./, (str)=>str.toUpperCase()).trim();\n}\n// ============================================================================\n// Number & Currency Utilities\n// ============================================================================\n/**\r\n * Format number with commas\r\n */ function formatNumber(num) {\n    return new Intl.NumberFormat().format(num);\n}\n/**\r\n * Format currency\r\n */ function formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 4\n    }).format(amount);\n}\n/**\r\n * Format percentage\r\n */ function formatPercentage(value, decimals = 1) {\n    return `${(value * 100).toFixed(decimals)}%`;\n}\n/**\r\n * Format file size\r\n */ function formatFileSize(bytes) {\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;\n}\n/**\r\n * Format token count with K/M suffixes\r\n */ function formatTokens(tokens) {\n    if (tokens < 1000) return tokens.toString();\n    if (tokens < 1000000) return `${(tokens / 1000).toFixed(1)}K`;\n    return `${(tokens / 1000000).toFixed(1)}M`;\n}\n// ============================================================================\n// Array & Object Utilities\n// ============================================================================\n/**\r\n * Group array by key\r\n */ function groupBy(array, key) {\n    return array.reduce((groups, item)=>{\n        const group = String(item[key]);\n        groups[group] = groups[group] || [];\n        groups[group].push(item);\n        return groups;\n    }, {});\n}\n/**\r\n * Sort array by multiple keys\r\n */ function sortBy(array, ...keys) {\n    return [\n        ...array\n    ].sort((a, b)=>{\n        for (const key of keys){\n            const aVal = a[key];\n            const bVal = b[key];\n            if (aVal < bVal) return -1;\n            if (aVal > bVal) return 1;\n        }\n        return 0;\n    });\n}\n/**\r\n * Remove duplicates from array\r\n */ function unique(array) {\n    return [\n        ...new Set(array)\n    ];\n}\n/**\r\n * Deep clone object\r\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== 'object') return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === 'object') {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\r\n * Check if object is empty\r\n */ function isEmpty(obj) {\n    if (obj == null) return true;\n    if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;\n    if (typeof obj === 'object') return Object.keys(obj).length === 0;\n    return false;\n}\n// ============================================================================\n// Validation Utilities\n// ============================================================================\n/**\r\n * Validate email format\r\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email.trim());\n}\n/**\r\n * Validate URL format\r\n */ function isValidUrl(url) {\n    try {\n        new URL(url);\n        return true;\n    } catch  {\n        return false;\n    }\n}\n/**\r\n * Validate JSON string\r\n */ function isValidJson(str) {\n    try {\n        JSON.parse(str);\n        return true;\n    } catch  {\n        return false;\n    }\n}\n/**\r\n * Validate email address with better error messages\r\n */ function validateEmail(email) {\n    if (!email || !email.trim()) {\n        return {\n            isValid: false,\n            message: 'Email is required'\n        };\n    }\n    const trimmedEmail = email.trim();\n    if (!isValidEmail(trimmedEmail)) {\n        return {\n            isValid: false,\n            message: 'Please enter a valid email address'\n        };\n    }\n    return {\n        isValid: true\n    };\n}\n/**\r\n * Validate password with more reasonable requirements\r\n */ function validatePassword(password) {\n    if (!password) {\n        return {\n            isValid: false,\n            message: 'Password is required'\n        };\n    }\n    if (password.length < 8) {\n        return {\n            isValid: false,\n            message: 'Password must be at least 8 characters long'\n        };\n    }\n    // More reasonable requirements - just need length and some complexity\n    const hasLetter = /[a-zA-Z]/.test(password);\n    const hasNumber = /\\d/.test(password);\n    if (!hasLetter || !hasNumber) {\n        return {\n            isValid: false,\n            message: 'Password must contain at least one letter and one number'\n        };\n    }\n    return {\n        isValid: true\n    };\n}\n/**\r\n * Validate required field\r\n */ function validateRequired(value, fieldName) {\n    if (!value || !value.trim()) {\n        return {\n            isValid: false,\n            message: `${fieldName} is required`\n        };\n    }\n    return {\n        isValid: true\n    };\n}\n/**\r\n * Validate name field\r\n */ function validateName(name, fieldName) {\n    const requiredCheck = validateRequired(name, fieldName);\n    if (!requiredCheck.isValid) return requiredCheck;\n    if (name.trim().length < 2) {\n        return {\n            isValid: false,\n            message: `${fieldName} must be at least 2 characters long`\n        };\n    }\n    if (!/^[a-zA-Z\\s'-]+$/.test(name.trim())) {\n        return {\n            isValid: false,\n            message: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes`\n        };\n    }\n    return {\n        isValid: true\n    };\n}\n/**\r\n * Validate password confirmation\r\n */ function validatePasswordConfirmation(password, confirmPassword) {\n    if (!confirmPassword) {\n        return {\n            isValid: false,\n            message: 'Please confirm your password'\n        };\n    }\n    if (password !== confirmPassword) {\n        return {\n            isValid: false,\n            message: 'Passwords do not match'\n        };\n    }\n    return {\n        isValid: true\n    };\n}\n// ============================================================================\n// Local Storage Utilities\n// ============================================================================\n/**\r\n * Safe localStorage getter with fallback\r\n */ function getStorageItem(key, defaultValue) {\n    if (true) return defaultValue;\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : defaultValue;\n    } catch  {\n        return defaultValue;\n    }\n}\n/**\r\n * Safe localStorage setter\r\n */ function setStorageItem(key, value) {\n    if (true) return;\n    try {\n        localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n        console.warn('Failed to save to localStorage:', error);\n    }\n}\n/**\r\n * Remove item from localStorage\r\n */ function removeStorageItem(key) {\n    if (true) return;\n    try {\n        localStorage.removeItem(key);\n    } catch (error) {\n        console.warn('Failed to remove from localStorage:', error);\n    }\n}\n// ============================================================================\n// Async Utilities\n// ============================================================================\n/**\r\n * Sleep/delay function\r\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\r\n * Debounce function\r\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\r\n * Throttle function\r\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\r\n * Retry async function with exponential backoff\r\n */ async function retry(fn, maxAttempts = 3, baseDelay = 1000) {\n    let lastError;\n    for(let attempt = 1; attempt <= maxAttempts; attempt++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (attempt === maxAttempts) {\n                throw lastError;\n            }\n            const delay = baseDelay * Math.pow(2, attempt - 1);\n            await sleep(delay);\n        }\n    }\n    throw lastError;\n}\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n/**\r\n * Extract error message from various error types\r\n */ function getErrorMessage(error) {\n    if (typeof error === 'string') return error;\n    if (error instanceof Error) return error.message;\n    if (error && typeof error === 'object' && 'message' in error) {\n        return String(error.message);\n    }\n    return 'An unknown error occurred';\n}\n/**\r\n * Check if error is network related\r\n */ function isNetworkError(error) {\n    const message = getErrorMessage(error).toLowerCase();\n    return message.includes('network') || message.includes('fetch') || message.includes('connection') || message.includes('timeout');\n}\n// ============================================================================\n// Browser Utilities\n// ============================================================================\n/**\r\n * Copy text to clipboard\r\n */ async function copyToClipboard(text) {\n    if (!navigator.clipboard) {\n        // Fallback for older browsers\n        const textArea = document.createElement('textarea');\n        textArea.value = text;\n        document.body.appendChild(textArea);\n        textArea.select();\n        try {\n            document.execCommand('copy');\n            document.body.removeChild(textArea);\n            return true;\n        } catch  {\n            document.body.removeChild(textArea);\n            return false;\n        }\n    }\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch  {\n        return false;\n    }\n}\n/**\r\n * Download file from blob\r\n */ function downloadFile(blob, filename) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n}\n/**\r\n * Check if device is mobile\r\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\r\n * Get device type\r\n */ function getDeviceType() {\n    if (true) return 'desktop';\n    const width = window.innerWidth;\n    if (width < 768) return 'mobile';\n    if (width < 1024) return 'tablet';\n    return 'desktop';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/index.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/@heroicons","vendor-chunks/date-fns","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CMCP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();