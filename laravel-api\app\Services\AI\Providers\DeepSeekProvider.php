<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class DeepSeekProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://api.deepseek.com/v1';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(10)->get($this->baseUrl . '/models');

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'DeepSeek API connection successful',
                    'models_count' => count($response->json('data', [])),
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'DeepSeek API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'DeepSeek API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/models');

            if ($response->successful()) {
                $models = $response->json('data', []);
                return array_map(function ($model) {
                    return [
                        'id' => $model['id'],
                        'name' => $model['id'],
                        'description' => $this->getModelDescription($model['id']),
                        'capabilities' => $this->getModelCapabilities($model['id']),
                        'context_length' => $this->getModelContextLength($model['id']),
                        'cost_per_token' => $this->getModelCostPerToken($model['id']),
                    ];
                }, $models);
            }

            return $this->getDefaultModels();

        } catch (\Exception $e) {
            $this->loggingService->logAIProvider('deepseek_models_fetch_error', [
                'error' => $e->getMessage(),
            ], 'error');
            return $this->getDefaultModels();
        }
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        // DeepSeek uses chat completions, convert to chat format
        $messages = [['role' => 'user', 'content' => $prompt]];
        return $this->generateChatCompletion($messages, $options, $stream);
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'deepseek-chat';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'model' => $model,
                'messages' => $messages,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            if (isset($options['stop'])) {
                $payload['stop'] = $options['stop'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['message']['content'] ?? '',
                    'message' => $data['choices'][0]['message'] ?? [],
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('DeepSeek API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        // DeepSeek doesn't provide embeddings directly
        return [
            'success' => false,
            'error' => 'DeepSeek does not provide embeddings',
            'provider' => 'deepseek',
        ];
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'code_generation',
            'reasoning',
            'mathematics',
            'streaming',
        ];
    }

    public function getName(): string
    {
        return 'deepseek';
    }

    public function getDisplayName(): string
    {
        return 'DeepSeek';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 60,
            'tokens_per_minute' => 10000,
            'requests_per_day' => 1000,
        ];
    }

    public function calculateCost(array $usage): float
    {
        $promptTokens = $usage['prompt_tokens'] ?? 0;
        $completionTokens = $usage['completion_tokens'] ?? 0;

        // DeepSeek competitive pricing
        $promptCost = $promptTokens * 0.0000014; // $1.4 per 1M tokens
        $completionCost = $completionTokens * 0.0000028; // $2.8 per 1M tokens

        return $promptCost + $completionCost;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('deepseek_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'deepseek',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'deepseek-chat',
                'name' => 'DeepSeek Chat',
                'description' => 'General purpose chat model',
                'capabilities' => ['text_generation', 'chat', 'reasoning'],
                'context_length' => 32768,
                'cost_per_token' => 0.0000014,
            ],
            [
                'id' => 'deepseek-coder',
                'name' => 'DeepSeek Coder',
                'description' => 'Specialized coding model',
                'capabilities' => ['code_generation', 'chat'],
                'context_length' => 16384,
                'cost_per_token' => 0.0000014,
            ],
            [
                'id' => 'deepseek-math',
                'name' => 'DeepSeek Math',
                'description' => 'Mathematical reasoning model',
                'capabilities' => ['mathematics', 'reasoning', 'chat'],
                'context_length' => 4096,
                'cost_per_token' => 0.0000014,
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $descriptions = [
            'deepseek-chat' => 'General purpose chat model',
            'deepseek-coder' => 'Specialized coding model',
            'deepseek-math' => 'Mathematical reasoning model',
        ];

        return $descriptions[$modelId] ?? 'DeepSeek model';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        if (str_contains($modelId, 'coder')) {
            return ['code_generation', 'chat'];
        }

        if (str_contains($modelId, 'math')) {
            return ['mathematics', 'reasoning', 'chat'];
        }

        return ['text_generation', 'chat', 'reasoning'];
    }

    protected function getModelContextLength(string $modelId): int
    {
        $contextLengths = [
            'deepseek-chat' => 32768,
            'deepseek-coder' => 16384,
            'deepseek-math' => 4096,
        ];

        return $contextLengths[$modelId] ?? 32768;
    }

    protected function getModelCostPerToken(string $modelId): float
    {
        return 0.0000014; // Consistent pricing across models
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'code_generation',
            'reasoning',
            'mathematics',
            'streaming',
        ];
    }
}
