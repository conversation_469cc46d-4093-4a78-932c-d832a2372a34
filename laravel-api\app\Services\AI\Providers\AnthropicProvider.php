<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class AnthropicProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://api.anthropic.com/v1';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            // Test with a simple message
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'anthropic-version' => '2023-06-01',
            ])->timeout(10)->post($this->baseUrl . '/messages', [
                'model' => 'claude-3-haiku-20240307',
                'max_tokens' => 10,
                'messages' => [
                    ['role' => 'user', 'content' => 'Hi']
                ]
            ]);

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'Anthropic API connection successful',
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'Anthropic API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Anthropic API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        return $this->getDefaultModels();
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        // Convert to chat format for Claude
        $messages = [['role' => 'user', 'content' => $prompt]];
        return $this->generateChatCompletion($messages, $options, $stream);
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'claude-3-sonnet-20240229';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'model' => $model,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'messages' => $messages,
                'stream' => $stream,
            ];

            if (isset($options['system'])) {
                $payload['system'] = $options['system'];
            }

            if (isset($options['stop_sequences'])) {
                $payload['stop_sequences'] = $options['stop_sequences'];
            }

            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'anthropic-version' => '2023-06-01',
            ])->timeout(60)->post($this->baseUrl . '/messages', $payload);

            if ($response->successful()) {
                $data = $response->json();

                $content = '';
                if (isset($data['content']) && is_array($data['content'])) {
                    foreach ($data['content'] as $block) {
                        if ($block['type'] === 'text') {
                            $content .= $block['text'];
                        }
                    }
                }

                return [
                    'success' => true,
                    'content' => $content,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $content,
                    ],
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['stop_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Anthropic API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        // Anthropic doesn't provide embeddings directly
        return [
            'success' => false,
            'error' => 'Anthropic does not provide embeddings',
            'provider' => 'anthropic',
        ];
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'long_context',
            'safety',
            'constitutional_ai',
            'streaming',
        ];
    }

    public function getName(): string
    {
        return 'anthropic';
    }

    public function getDisplayName(): string
    {
        return 'Anthropic Claude';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 50,
            'tokens_per_minute' => 40000,
            'requests_per_day' => 1000,
        ];
    }

    public function calculateCost(array $usage): float
    {
        $inputTokens = $usage['input_tokens'] ?? 0;
        $outputTokens = $usage['output_tokens'] ?? 0;

        // Claude 3 Sonnet pricing
        $inputCost = $inputTokens * 0.000003; // $3 per 1M tokens
        $outputCost = $outputTokens * 0.000015; // $15 per 1M tokens

        return $inputCost + $outputCost;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('anthropic_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'anthropic',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'claude-3-opus-20240229',
                'name' => 'Claude 3 Opus',
                'description' => 'Most capable Claude 3 model',
                'capabilities' => ['text_generation', 'chat', 'long_context', 'safety'],
                'context_length' => 200000,
                'cost_per_token' => 0.000015,
            ],
            [
                'id' => 'claude-3-sonnet-20240229',
                'name' => 'Claude 3 Sonnet',
                'description' => 'Balanced performance and speed',
                'capabilities' => ['text_generation', 'chat', 'long_context', 'safety'],
                'context_length' => 200000,
                'cost_per_token' => 0.000003,
            ],
            [
                'id' => 'claude-3-haiku-20240307',
                'name' => 'Claude 3 Haiku',
                'description' => 'Fastest Claude 3 model',
                'capabilities' => ['text_generation', 'chat', 'safety'],
                'context_length' => 200000,
                'cost_per_token' => 0.00000025,
            ],
            [
                'id' => 'claude-2.1',
                'name' => 'Claude 2.1',
                'description' => 'Previous generation Claude model',
                'capabilities' => ['text_generation', 'chat', 'long_context'],
                'context_length' => 200000,
                'cost_per_token' => 0.000008,
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $descriptions = [
            'claude-3-opus-20240229' => 'Most capable Claude 3 model',
            'claude-3-sonnet-20240229' => 'Balanced performance and speed',
            'claude-3-haiku-20240307' => 'Fastest Claude 3 model',
            'claude-2.1' => 'Previous generation Claude model',
        ];

        return $descriptions[$modelId] ?? 'Claude model';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        if (str_contains($modelId, 'claude-3')) {
            return ['text_generation', 'chat', 'long_context', 'safety', 'constitutional_ai'];
        }

        return ['text_generation', 'chat', 'long_context'];
    }

    protected function getModelContextLength(string $modelId): int
    {
        return 200000; // All Claude models support 200K context
    }

    protected function getModelCostPerToken(string $modelId): float
    {
        $costs = [
            'claude-3-opus-20240229' => 0.000015,
            'claude-3-sonnet-20240229' => 0.000003,
            'claude-3-haiku-20240307' => 0.00000025,
            'claude-2.1' => 0.000008,
        ];

        return $costs[$modelId] ?? 0.000003;
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'long_context',
            'safety',
            'constitutional_ai',
            'streaming',
        ];
    }
}
