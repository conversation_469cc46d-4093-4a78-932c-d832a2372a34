<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class GrokProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://api.x.ai/v1';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(10)->post($this->baseUrl . '/chat/completions', [
                'model' => 'grok-beta',
                'messages' => [['role' => 'user', 'content' => 'Hi']],
                'max_tokens' => 10,
            ]);

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'Grok API connection successful',
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'Grok API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Grok API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        return $this->getDefaultModels();
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        // Grok uses chat completions, convert to chat format
        $messages = [['role' => 'user', 'content' => $prompt]];
        return $this->generateChatCompletion($messages, $options, $stream);
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'grok-beta';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'model' => $model,
                'messages' => $messages,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            // Grok-specific features
            if (isset($options['real_time_info'])) {
                $payload['real_time_info'] = $options['real_time_info'];
            }

            if (isset($options['humor_level'])) {
                $payload['humor_level'] = $options['humor_level'];
            }

            if (isset($options['rebellious_mode'])) {
                $payload['rebellious_mode'] = $options['rebellious_mode'];
            }

            if (isset($options['stop'])) {
                $payload['stop'] = $options['stop'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['message']['content'] ?? '',
                    'message' => $data['choices'][0]['message'] ?? [],
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'real_time_data' => $data['real_time_data'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Grok API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        // Grok doesn't provide embeddings directly
        return [
            'success' => false,
            'error' => 'Grok does not provide embeddings',
            'provider' => 'grok',
        ];
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'real_time_information',
            'humor',
            'rebellious_responses',
            'x_twitter_integration',
            'current_events',
            'streaming',
        ];
    }

    public function getName(): string
    {
        return 'grok';
    }

    public function getDisplayName(): string
    {
        return 'Grok (xAI)';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 100,
            'tokens_per_minute' => 10000,
            'requests_per_day' => 1000,
        ];
    }

    public function calculateCost(array $usage): float
    {
        $promptTokens = $usage['prompt_tokens'] ?? 0;
        $completionTokens = $usage['completion_tokens'] ?? 0;

        // Grok pricing (competitive with real-time features)
        $promptCost = $promptTokens * 0.000005; // $5 per 1M tokens
        $completionCost = $completionTokens * 0.000015; // $15 per 1M tokens

        return $promptCost + $completionCost;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('grok_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'grok',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'grok-beta',
                'name' => 'Grok Beta',
                'description' => 'Grok AI with real-time information access',
                'capabilities' => ['text_generation', 'chat', 'real_time_information', 'humor'],
                'context_length' => 25000,
                'cost_per_token' => 0.000005,
            ],
            [
                'id' => 'grok-1',
                'name' => 'Grok-1',
                'description' => 'First generation Grok model',
                'capabilities' => ['text_generation', 'chat', 'humor'],
                'context_length' => 8192,
                'cost_per_token' => 0.000003,
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $descriptions = [
            'grok-beta' => 'Grok AI with real-time information access',
            'grok-1' => 'First generation Grok model',
        ];

        return $descriptions[$modelId] ?? 'Grok AI model';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        $capabilities = ['text_generation', 'chat', 'humor'];

        if (str_contains($modelId, 'beta')) {
            $capabilities[] = 'real_time_information';
            $capabilities[] = 'current_events';
        }

        return $capabilities;
    }

    protected function getModelContextLength(string $modelId): int
    {
        $contextLengths = [
            'grok-beta' => 25000,
            'grok-1' => 8192,
        ];

        return $contextLengths[$modelId] ?? 8192;
    }

    protected function getModelCostPerToken(string $modelId): float
    {
        $costs = [
            'grok-beta' => 0.000005,
            'grok-1' => 0.000003,
        ];

        return $costs[$modelId] ?? 0.000005;
    }

    /**
     * Generate response with real-time information
     */
    public function generateWithRealTimeInfo(array $messages, array $options = []): array
    {
        $options['real_time_info'] = true;
        return $this->generateChatCompletion($messages, $options);
    }

    /**
     * Generate humorous response
     */
    public function generateHumorousResponse(array $messages, array $options = []): array
    {
        $options['humor_level'] = $options['humor_level'] ?? 'high';
        return $this->generateChatCompletion($messages, $options);
    }

    /**
     * Generate rebellious/unfiltered response
     */
    public function generateRebelliousResponse(array $messages, array $options = []): array
    {
        $options['rebellious_mode'] = true;
        return $this->generateChatCompletion($messages, $options);
    }

    /**
     * Get current events and trending topics
     */
    public function getCurrentEvents(array $topics = []): array
    {
        try {
            $prompt = "What are the current trending topics and recent events";
            if (!empty($topics)) {
                $prompt .= " related to: " . implode(', ', $topics);
            }
            $prompt .= "? Please provide real-time information.";

            $messages = [['role' => 'user', 'content' => $prompt]];

            return $this->generateWithRealTimeInfo($messages, [
                'max_tokens' => 2000,
                'temperature' => 0.3, // Lower temperature for factual information
            ]);

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Analyze X/Twitter sentiment or trends
     */
    public function analyzeTwitterTrends(string $topic, array $options = []): array
    {
        try {
            $prompt = "Analyze the current Twitter/X trends and sentiment around the topic: {$topic}. ";
            $prompt .= "Provide real-time insights about what people are saying, trending hashtags, and overall sentiment.";

            $messages = [['role' => 'user', 'content' => $prompt]];

            return $this->generateWithRealTimeInfo($messages, array_merge([
                'max_tokens' => 1500,
                'temperature' => 0.4,
            ], $options));

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Get witty commentary on current events
     */
    public function getWittyCommentary(string $topic, array $options = []): array
    {
        try {
            $prompt = "Provide a witty, humorous commentary on: {$topic}. ";
            $prompt .= "Be clever, sarcastic, and entertaining while staying informative.";

            $messages = [['role' => 'user', 'content' => $prompt]];

            return $this->generateHumorousResponse($messages, array_merge([
                'max_tokens' => 1000,
                'temperature' => 0.8,
                'real_time_info' => true,
            ], $options));

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'real_time_information',
            'humor',
            'rebellious_responses',
            'x_twitter_integration',
            'current_events',
            'streaming',
        ];
    }
}
