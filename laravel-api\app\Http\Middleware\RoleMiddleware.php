<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        // Ensure user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'error' => 'Unauthenticated',
                'message' => 'Authentication required'
            ], 401);
        }

        $user = Auth::user();
        $tenant = $request->get('tenant');

        // Ensure tenant context is set
        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant context missing',
                'message' => 'Tenant context must be established'
            ], 400);
        }

        // Check if user has any of the required roles
        $userRole = $user->role;
        $hasRequiredRole = in_array($userRole, $roles);

        if (!$hasRequiredRole) {
            // Log unauthorized access attempt
            $this->logUnauthorizedAccess($request, $user, $roles);

            return response()->json([
                'error' => 'Insufficient permissions',
                'message' => 'You do not have the required role to access this resource',
                'required_roles' => $roles,
                'user_role' => $userRole
            ], 403);
        }

        // Check role hierarchy and additional permissions
        if (!$this->checkRoleHierarchy($user, $roles, $tenant)) {
            return response()->json([
                'error' => 'Role hierarchy violation',
                'message' => 'Your role level does not permit this action'
            ], 403);
        }

        // Add role information to request
        $request->merge([
            'user_role' => $userRole,
            'role_permissions' => $this->getRolePermissions($userRole, $tenant)
        ]);

        return $next($request);
    }

    /**
     * Check role hierarchy and additional constraints
     */
    protected function checkRoleHierarchy($user, array $requiredRoles, $tenant): bool
    {
        $roleHierarchy = [
            'viewer' => 1,
            'user' => 2,
            'manager' => 3,
            'admin' => 4,
            'owner' => 5
        ];

        $userRoleLevel = $roleHierarchy[$user->role] ?? 0;

        // Check if user role meets minimum requirement
        $minRequiredLevel = min(array_map(fn($role) => $roleHierarchy[$role] ?? 0, $requiredRoles));

        if ($userRoleLevel < $minRequiredLevel) {
            return false;
        }

        // Additional checks for specific roles
        switch ($user->role) {
            case 'owner':
                // Owner can do everything within their tenant
                return true;

            case 'admin':
                // Admin cannot perform owner-only actions
                if (in_array('owner', $requiredRoles) && !in_array('admin', $requiredRoles)) {
                    return false;
                }
                return true;

            case 'manager':
                // Manager has limited admin capabilities
                return $this->checkManagerPermissions($user, $tenant);

            case 'user':
                // Regular user permissions
                return $this->checkUserPermissions($user, $tenant);

            case 'viewer':
                // Viewer has read-only access
                return $this->checkViewerPermissions($user, $tenant);

            default:
                return false;
        }
    }

    /**
     * Get permissions for a specific role
     */
    protected function getRolePermissions(string $role, $tenant): array
    {
        $basePermissions = [
            'owner' => [
                'tenant:manage', 'users:manage', 'settings:manage', 'billing:manage',
                'ai_providers:manage', 'tools:manage', 'workflows:manage',
                'documents:manage', 'chat:access', 'monitoring:access'
            ],
            'admin' => [
                'users:manage', 'settings:view', 'ai_providers:manage',
                'tools:manage', 'workflows:manage', 'documents:manage',
                'chat:access', 'monitoring:access'
            ],
            'manager' => [
                'users:view', 'ai_providers:view', 'tools:manage',
                'workflows:manage', 'documents:manage', 'chat:access'
            ],
            'user' => [
                'tools:use', 'workflows:use', 'documents:create',
                'documents:view', 'chat:access'
            ],
            'viewer' => [
                'tools:view', 'workflows:view', 'documents:view', 'chat:view'
            ]
        ];

        $permissions = $basePermissions[$role] ?? [];

        // Add tenant-specific permissions
        $tenantPermissions = $tenant->role_permissions[$role] ?? [];

        return array_unique(array_merge($permissions, $tenantPermissions));
    }

    /**
     * Check manager-specific permissions
     */
    protected function checkManagerPermissions($user, $tenant): bool
    {
        // Managers can be restricted by tenant settings
        $managerRestrictions = $tenant->manager_restrictions ?? [];

        // Check if manager is restricted from certain actions
        foreach ($managerRestrictions as $restriction) {
            if ($this->requestMatchesRestriction($restriction)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check user-specific permissions
     */
    protected function checkUserPermissions($user, $tenant): bool
    {
        // Users might have additional permissions granted
        $userPermissions = $user->additional_permissions ?? [];

        // Check if user has been granted specific permissions
        return !empty($userPermissions);
    }

    /**
     * Check viewer-specific permissions
     */
    protected function checkViewerPermissions($user, $tenant): bool
    {
        // Viewers typically only have read access
        // This could be expanded based on tenant settings
        return true;
    }

    /**
     * Check if current request matches a restriction pattern
     */
    protected function requestMatchesRestriction(array $restriction): bool
    {
        // This would implement pattern matching for restrictions
        // For now, return false (no restrictions)
        return false;
    }

    /**
     * Log unauthorized access attempt
     */
    protected function logUnauthorizedAccess(Request $request, $user, array $requiredRoles): void
    {
        // This would log to the system logs
        \Log::warning('Unauthorized access attempt', [
            'user_id' => $user->id,
            'tenant_id' => $user->tenant_id,
            'user_role' => $user->role,
            'required_roles' => $requiredRoles,
            'endpoint' => $request->fullUrl(),
            'method' => $request->method(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);
    }
}
