<?php

namespace App\Services\Core;

use App\Models\Tenant;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;

class ConfigurationService
{
    /**
     * Configuration cache TTL (1 hour)
     */
    protected int $cacheTtl = 3600;

    /**
     * Configuration cache prefix
     */
    protected string $cachePrefix = 'config:';

    /**
     * Default service configurations
     */
    protected array $defaultServiceConfigs = [
        'base_service' => [
            'enabled' => true,
            'cache_enabled' => true,
            'cache_ttl' => 3600,
            'performance_tracking' => true,
        ],
        'logging_service' => [
            'enabled' => true,
            'database_logging' => true,
            'file_logging' => true,
            'real_time_streaming' => false,
            'retention_days' => 30,
            'batch_size' => 100,
        ],
        'error_handling_service' => [
            'enabled' => true,
            'alert_critical_errors' => true,
            'alert_security_incidents' => true,
            'user_friendly_messages' => true,
        ],
        'tenant_context_service' => [
            'enabled' => true,
            'strict_isolation' => true,
            'cache_tenant_data' => true,
            'validate_subscriptions' => true,
        ],
        'mcp_orchestrator' => [
            'enabled' => true,
            'max_concurrent_operations' => 10,
            'operation_timeout_seconds' => 30,
            'retry_attempts' => 3,
            'cache_results' => true,
        ],
        'websocket_service' => [
            'enabled' => true,
            'max_connections_per_tenant' => 100,
            'connection_timeout_seconds' => 300,
            'heartbeat_interval_seconds' => 30,
            'message_rate_limit' => 60,
        ],
        'ai_provider_service' => [
            'enabled' => true,
            'default_provider' => 'openai',
            'fallback_providers' => ['groq', 'anthropic'],
            'request_timeout_seconds' => 30,
            'rate_limit_per_minute' => 60,
            'cache_responses' => true,
            'cache_ttl_seconds' => 300,
        ],
    ];

    /**
     * Sensitive configuration keys that should be encrypted
     */
    protected array $sensitiveKeys = [
        'api_key',
        'secret',
        'password',
        'token',
        'private_key',
        'webhook_secret',
        'database_password',
        'redis_password',
    ];

    /**
     * Configuration validation rules
     */
    protected array $validationRules = [
        'enabled' => 'boolean',
        'cache_enabled' => 'boolean',
        'cache_ttl' => 'integer|min:60|max:86400',
        'max_connections_per_tenant' => 'integer|min:1|max:1000',
        'connection_timeout_seconds' => 'integer|min:30|max:3600',
        'request_timeout_seconds' => 'integer|min:5|max:300',
        'rate_limit_per_minute' => 'integer|min:1|max:1000',
        'retention_days' => 'integer|min:1|max:365',
        'batch_size' => 'integer|min:10|max:1000',
    ];

    /**
     * Get service configuration
     */
    public function getServiceConfig(string $serviceName, ?string $tenantId = null): array
    {
        $cacheKey = $this->getCacheKey("service:{$serviceName}", $tenantId);

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($serviceName, $tenantId) {
            // Start with default configuration
            $config = $this->defaultServiceConfigs[$serviceName] ?? [];

            // Merge with global configuration
            $globalConfig = config("services.{$serviceName}", []);
            $config = array_merge($config, $globalConfig);

            // Merge with tenant-specific configuration if tenant provided
            if ($tenantId) {
                $tenantConfig = $this->getTenantServiceConfig($serviceName, $tenantId);
                $config = array_merge($config, $tenantConfig);
            }

            // Decrypt sensitive values
            $config = $this->decryptSensitiveValues($config);

            return $config;
        });
    }

    /**
     * Get cache configuration for a service
     */
    public function getCacheConfig(string $serviceName, ?string $tenantId = null): array
    {
        $serviceConfig = $this->getServiceConfig($serviceName, $tenantId);

        return [
            'enabled' => $serviceConfig['cache_enabled'] ?? true,
            'ttl' => $serviceConfig['cache_ttl'] ?? 3600,
            'prefix' => $serviceName . ':',
        ];
    }

    /**
     * Set service configuration
     */
    public function setServiceConfig(string $serviceName, array $config, ?string $tenantId = null): void
    {
        // Validate configuration
        $this->validateConfiguration($config);

        // Encrypt sensitive values
        $config = $this->encryptSensitiveValues($config);

        if ($tenantId) {
            $this->setTenantServiceConfig($serviceName, $config, $tenantId);
        } else {
            $this->setGlobalServiceConfig($serviceName, $config);
        }

        // Clear cache
        $this->clearServiceConfigCache($serviceName, $tenantId);
    }

    /**
     * Get AI provider configuration
     */
    public function getAIProviderConfig(string $provider, ?string $tenantId = null): array
    {
        $cacheKey = $this->getCacheKey("ai_provider:{$provider}", $tenantId);

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($provider, $tenantId) {
            $config = [];

            // Get global provider configuration
            $globalConfig = config("ai_providers.{$provider}", []);
            $config = array_merge($config, $globalConfig);

            // Get tenant-specific provider configuration
            if ($tenantId) {
                $tenantConfig = $this->getTenantAIProviderConfig($provider, $tenantId);
                $config = array_merge($config, $tenantConfig);
            }

            // Decrypt sensitive values
            $config = $this->decryptSensitiveValues($config);

            return $config;
        });
    }

    /**
     * Set AI provider configuration
     */
    public function setAIProviderConfig(string $provider, array $config, ?string $tenantId = null): void
    {
        // Validate provider configuration
        $this->validateAIProviderConfiguration($provider, $config);

        // Encrypt sensitive values
        $config = $this->encryptSensitiveValues($config);

        if ($tenantId) {
            $this->setTenantAIProviderConfig($provider, $config, $tenantId);
        } else {
            $this->setGlobalAIProviderConfig($provider, $config);
        }

        // Clear cache
        $this->clearAIProviderConfigCache($provider, $tenantId);
    }

    /**
     * Get tenant settings
     */
    public function getTenantSettings(string $tenantId): array
    {
        $cacheKey = $this->getCacheKey('tenant_settings', $tenantId);

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($tenantId) {
            $tenant = Tenant::find($tenantId);

            if (!$tenant) {
                return [];
            }

            $settings = $tenant->settings ?? [];

            // Decrypt sensitive values
            return $this->decryptSensitiveValues($settings);
        });
    }

    /**
     * Set tenant settings
     */
    public function setTenantSettings(string $tenantId, array $settings): void
    {
        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            throw new \InvalidArgumentException("Tenant not found: {$tenantId}");
        }

        // Encrypt sensitive values
        $settings = $this->encryptSensitiveValues($settings);

        $tenant->update(['settings' => $settings]);

        // Clear cache
        $this->clearTenantSettingsCache($tenantId);
    }

    /**
     * Get feature flags for tenant
     */
    public function getFeatureFlags(?string $tenantId = null): array
    {
        if (!$tenantId) {
            return config('features', []);
        }

        $cacheKey = $this->getCacheKey('feature_flags', $tenantId);

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($tenantId) {
            $tenant = Tenant::find($tenantId);

            if (!$tenant) {
                return config('features', []);
            }

            // Merge global features with tenant-specific features
            $globalFeatures = config('features', []);
            $tenantFeatures = $tenant->feature_flags ?? [];

            return array_merge($globalFeatures, $tenantFeatures);
        });
    }

    /**
     * Check if feature is enabled for tenant
     */
    public function isFeatureEnabled(string $feature, ?string $tenantId = null): bool
    {
        $features = $this->getFeatureFlags($tenantId);
        return $features[$feature] ?? false;
    }

    /**
     * Set feature flag for tenant
     */
    public function setFeatureFlag(string $feature, bool $enabled, ?string $tenantId = null): void
    {
        if (!$tenantId) {
            // Set global feature flag
            $features = config('features', []);
            $features[$feature] = $enabled;
            Config::set('features', $features);
            return;
        }

        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            throw new \InvalidArgumentException("Tenant not found: {$tenantId}");
        }

        $featureFlags = $tenant->feature_flags ?? [];
        $featureFlags[$feature] = $enabled;

        $tenant->update(['feature_flags' => $featureFlags]);

        // Clear cache
        $this->clearFeatureFlagsCache($tenantId);
    }

    /**
     * Get environment-specific configuration
     */
    public function getEnvironmentConfig(string $key, $default = null)
    {
        return config($key, $default);
    }

    /**
     * Validate configuration against rules
     */
    protected function validateConfiguration(array $config): void
    {
        foreach ($config as $key => $value) {
            if (isset($this->validationRules[$key])) {
                $rule = $this->validationRules[$key];

                if (!$this->validateValue($value, $rule)) {
                    throw new \InvalidArgumentException("Invalid configuration value for {$key}: {$value}");
                }
            }
        }
    }

    /**
     * Validate AI provider configuration
     */
    protected function validateAIProviderConfiguration(string $provider, array $config): void
    {
        $requiredKeys = [
            'openai' => ['api_key'],
            'groq' => ['api_key'],
            'anthropic' => ['api_key'],
            'gemini' => ['api_key'],
            'huggingface' => ['api_key'],
            'mistral' => ['api_key'],
            'deepseek' => ['api_key'],
            'codestral' => ['api_key'],
            'openrouter' => ['api_key'],
            'grok' => ['api_key'],
        ];

        if (isset($requiredKeys[$provider])) {
            foreach ($requiredKeys[$provider] as $key) {
                if (!isset($config[$key]) || empty($config[$key])) {
                    throw new \InvalidArgumentException("Missing required configuration key for {$provider}: {$key}");
                }
            }
        }
    }

    /**
     * Validate a single value against a rule
     */
    protected function validateValue($value, string $rule): bool
    {
        $rules = explode('|', $rule);

        foreach ($rules as $singleRule) {
            if (str_contains($singleRule, ':')) {
                [$ruleName, $ruleValue] = explode(':', $singleRule, 2);
            } else {
                $ruleName = $singleRule;
                $ruleValue = null;
            }

            switch ($ruleName) {
                case 'boolean':
                    if (!is_bool($value)) return false;
                    break;
                case 'integer':
                    if (!is_int($value)) return false;
                    break;
                case 'min':
                    if (is_numeric($value) && $value < (int)$ruleValue) return false;
                    break;
                case 'max':
                    if (is_numeric($value) && $value > (int)$ruleValue) return false;
                    break;
            }
        }

        return true;
    }

    /**
     * Encrypt sensitive configuration values
     */
    protected function encryptSensitiveValues(array $config): array
    {
        foreach ($config as $key => $value) {
            if ($this->isSensitiveKey($key) && is_string($value) && !empty($value)) {
                $config[$key] = Crypt::encryptString($value);
            } elseif (is_array($value)) {
                $config[$key] = $this->encryptSensitiveValues($value);
            }
        }

        return $config;
    }

    /**
     * Decrypt sensitive configuration values
     */
    protected function decryptSensitiveValues(array $config): array
    {
        foreach ($config as $key => $value) {
            if ($this->isSensitiveKey($key) && is_string($value) && !empty($value)) {
                try {
                    $config[$key] = Crypt::decryptString($value);
                } catch (\Exception $e) {
                    // If decryption fails, assume value is not encrypted
                    // This handles cases where values are stored in plain text
                }
            } elseif (is_array($value)) {
                $config[$key] = $this->decryptSensitiveValues($value);
            }
        }

        return $config;
    }

    /**
     * Check if a key is sensitive
     */
    protected function isSensitiveKey(string $key): bool
    {
        $key = strtolower($key);

        foreach ($this->sensitiveKeys as $sensitiveKey) {
            if (str_contains($key, $sensitiveKey)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get tenant-specific service configuration
     */
    protected function getTenantServiceConfig(string $serviceName, string $tenantId): array
    {
        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            return [];
        }

        $settings = $tenant->settings ?? [];
        return $settings['services'][$serviceName] ?? [];
    }

    /**
     * Set tenant-specific service configuration
     */
    protected function setTenantServiceConfig(string $serviceName, array $config, string $tenantId): void
    {
        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            throw new \InvalidArgumentException("Tenant not found: {$tenantId}");
        }

        $settings = $tenant->settings ?? [];
        $settings['services'][$serviceName] = $config;

        $tenant->update(['settings' => $settings]);
    }

    /**
     * Set global service configuration
     */
    protected function setGlobalServiceConfig(string $serviceName, array $config): void
    {
        Config::set("services.{$serviceName}", $config);
    }

    /**
     * Get tenant-specific AI provider configuration
     */
    protected function getTenantAIProviderConfig(string $provider, string $tenantId): array
    {
        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            return [];
        }

        $aiConfig = $tenant->ai_provider_config ?? [];
        return $aiConfig[$provider] ?? [];
    }

    /**
     * Set tenant-specific AI provider configuration
     */
    protected function setTenantAIProviderConfig(string $provider, array $config, string $tenantId): void
    {
        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            throw new \InvalidArgumentException("Tenant not found: {$tenantId}");
        }

        $aiConfig = $tenant->ai_provider_config ?? [];
        $aiConfig[$provider] = $config;

        $tenant->update(['ai_provider_config' => $aiConfig]);
    }

    /**
     * Set global AI provider configuration
     */
    protected function setGlobalAIProviderConfig(string $provider, array $config): void
    {
        Config::set("ai_providers.{$provider}", $config);
    }

    /**
     * Get cache key with optional tenant prefix
     */
    protected function getCacheKey(string $key, ?string $tenantId = null): string
    {
        $prefix = $this->cachePrefix;

        if ($tenantId) {
            return "{$prefix}tenant:{$tenantId}:{$key}";
        }

        return "{$prefix}global:{$key}";
    }

    /**
     * Clear service configuration cache
     */
    protected function clearServiceConfigCache(string $serviceName, ?string $tenantId = null): void
    {
        $cacheKey = $this->getCacheKey("service:{$serviceName}", $tenantId);
        Cache::forget($cacheKey);
    }

    /**
     * Clear AI provider configuration cache
     */
    protected function clearAIProviderConfigCache(string $provider, ?string $tenantId = null): void
    {
        $cacheKey = $this->getCacheKey("ai_provider:{$provider}", $tenantId);
        Cache::forget($cacheKey);
    }

    /**
     * Clear tenant settings cache
     */
    protected function clearTenantSettingsCache(string $tenantId): void
    {
        $cacheKey = $this->getCacheKey('tenant_settings', $tenantId);
        Cache::forget($cacheKey);
    }

    /**
     * Clear feature flags cache
     */
    protected function clearFeatureFlagsCache(string $tenantId): void
    {
        $cacheKey = $this->getCacheKey('feature_flags', $tenantId);
        Cache::forget($cacheKey);
    }

    /**
     * Get service health status
     */
    public function getHealthStatus(): array
    {
        return [
            'service' => 'configuration',
            'status' => 'enabled',
            'cache_enabled' => true,
            'cache_ttl' => $this->cacheTtl,
            'sensitive_keys_count' => count($this->sensitiveKeys),
            'validation_rules_count' => count($this->validationRules),
            'default_services_count' => count($this->defaultServiceConfigs),
            'timestamp' => now()->toISOString(),
        ];
    }
}
