<?php

namespace App\Enums;

enum SubscriptionStatus: string
{
    case TRIAL = 'trial';
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case SUSPENDED = 'suspended';
    case CANCELLED = 'cancelled';
    case PENDING = 'pending';

    /**
     * Get all values as array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human readable label
     */
    public function label(): string
    {
        return match($this) {
            self::TRIAL => 'Trial',
            self::ACTIVE => 'Active',
            self::INACTIVE => 'Inactive',
            self::SUSPENDED => 'Suspended',
            self::CANCELLED => 'Cancelled',
            self::PENDING => 'Pending',
        };
    }

    /**
     * Check if status is active
     */
    public function isActive(): bool
    {
        return in_array($this, [self::TRIAL, self::ACTIVE]);
    }

    /**
     * Check if status allows access
     */
    public function allowsAccess(): bool
    {
        return !in_array($this, [self::SUSPENDED, self::CANCELLED]);
    }
}
