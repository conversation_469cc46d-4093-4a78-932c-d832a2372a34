<?php

namespace App\Events\WebSocket;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SystemAlertEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $tenantId;
    public string $alertId;
    public string $alertType;
    public string $severity;
    public string $title;
    public string $message;
    public array $data;
    public array $metadata;
    public string $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $tenantId,
        string $alertId,
        string $alertType,
        string $severity,
        string $title,
        string $message,
        array $data = [],
        array $metadata = []
    ) {
        $this->tenantId = $tenantId;
        $this->alertId = $alertId;
        $this->alertType = $alertType;
        $this->severity = $severity;
        $this->title = $title;
        $this->message = $message;
        $this->data = $data;
        $this->metadata = $metadata;
        $this->timestamp = now()->toISOString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [
            new PrivateChannel("system.{$this->tenantId}"),
        ];

        // For critical alerts, also broadcast to monitoring channel
        if (in_array($this->severity, ['critical', 'high'])) {
            $channels[] = new PrivateChannel("monitoring.{$this->tenantId}");
        }

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event_type' => 'system_alert',
            'alert_id' => $this->alertId,
            'alert_type' => $this->alertType,
            'severity' => $this->severity,
            'title' => $this->title,
            'message' => $this->message,
            'data' => $this->data,
            'metadata' => $this->metadata,
            'timestamp' => $this->timestamp,
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return "system.alert.{$this->severity}";
    }

    /**
     * Determine if this event should be queued.
     */
    public function shouldQueue(): bool
    {
        // Critical alerts should be sent immediately
        if ($this->severity === 'critical') {
            return false;
        }

        return config('broadcasting.queue.enabled', true);
    }

    /**
     * Get the queue connection for the event.
     */
    public function broadcastQueue(): string
    {
        return config('broadcasting.queue.queue_name', 'websocket');
    }
}
