<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Core\TenantContextService;
use App\Services\Core\LoggingService;
use App\Services\MCP\RetrieverService;
use App\Models\Document;
use App\Models\Embedding;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocumentController extends Controller
{
    protected TenantContextService $tenantContext;
    protected LoggingService $loggingService;
    protected RetrieverService $retrieverService;

    public function __construct(
        TenantContextService $tenantContext,
        LoggingService $loggingService,
        RetrieverService $retrieverService
    ) {
        $this->tenantContext = $tenantContext;
        $this->loggingService = $loggingService;
        $this->retrieverService = $retrieverService;
    }

    /**
     * Get all documents for the tenant
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'nullable|string|in:text,pdf,docx,xlsx,csv,json,xml',
                'status' => 'nullable|string|in:pending,processing,processed,failed',
                'search' => 'nullable|string|max:255',
                'per_page' => 'nullable|integer|min:10|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();

            $query = Document::where('tenant_id', $tenant->id);

            // Apply filters
            if ($request->filled('type')) {
                $query->where('type', $request->input('type'));
            }

            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'LIKE', "%{$search}%")
                      ->orWhere('content', 'LIKE', "%{$search}%")
                      ->orWhere('metadata', 'LIKE', "%{$search}%");
                });
            }

            $perPage = $request->input('per_page', 20);
            $documents = $query->orderBy('created_at', 'desc')->paginate($perPage);

            $this->loggingService->logHttpRequest('documents_retrieved', [
                'tenant_id' => $tenant->id,
                'documents_count' => $documents->count(),
                'filters' => $request->only(['type', 'status', 'search']),
            ]);

            return response()->json([
                'success' => true,
                'data' => $documents,
                'message' => 'Documents retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('documents_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve documents',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload and create a new document
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required_without:content|file|max:10240', // 10MB max
                'content' => 'required_without:file|string',
                'title' => 'nullable|string|max:255',
                'type' => 'nullable|string|in:text,pdf,docx,xlsx,csv,json,xml',
                'metadata' => 'nullable|array',
                'auto_process' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $document = null;

            if ($request->hasFile('file')) {
                // Handle file upload
                $file = $request->file('file');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs("documents/{$tenant->id}", $filename, 'private');

                $document = Document::create([
                    'id' => Str::uuid(),
                    'tenant_id' => $tenant->id,
                    'title' => $request->input('title') ?: $file->getClientOriginalName(),
                    'type' => $request->input('type') ?: $this->detectFileType($file->getClientOriginalExtension()),
                    'file_path' => $path,
                    'file_size' => $file->getSize(),
                    'content' => null, // Will be extracted during processing
                    'metadata' => array_merge(
                        $request->input('metadata', []),
                        [
                            'original_filename' => $file->getClientOriginalName(),
                            'mime_type' => $file->getMimeType(),
                            'uploaded_by' => $user->id,
                        ]
                    ),
                    'status' => 'pending',
                ]);
            } else {
                // Handle direct content
                $document = Document::create([
                    'id' => Str::uuid(),
                    'tenant_id' => $tenant->id,
                    'title' => $request->input('title') ?: 'Text Document',
                    'type' => $request->input('type', 'text'),
                    'content' => $request->input('content'),
                    'metadata' => array_merge(
                        $request->input('metadata', []),
                        [
                            'created_by' => $user->id,
                            'content_length' => strlen($request->input('content')),
                        ]
                    ),
                    'status' => 'processed',
                ]);
            }

            // Auto-process if requested
            if ($request->input('auto_process', true) && $document->status === 'pending') {
                $this->processDocument($document);
            }

            $this->loggingService->logHttpRequest('document_created', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'document_id' => $document->id,
                'document_type' => $document->type,
                'has_file' => !is_null($document->file_path),
            ]);

            return response()->json([
                'success' => true,
                'data' => $document,
                'message' => 'Document created successfully'
            ], 201);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('document_creation_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to create document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific document
     */
    public function show(Request $request, string $documentId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $document = Document::where('id', $documentId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$document) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document not found'
                ], 404);
            }

            $this->loggingService->logHttpRequest('document_retrieved', [
                'tenant_id' => $tenant->id,
                'document_id' => $documentId,
            ]);

            return response()->json([
                'success' => true,
                'data' => $document,
                'message' => 'Document retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('document_retrieval_error', [
                'document_id' => $documentId,
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a document
     */
    public function update(Request $request, string $documentId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'nullable|string|max:255',
                'content' => 'nullable|string',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $document = Document::where('id', $documentId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$document) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document not found'
                ], 404);
            }

            $updateData = [];

            if ($request->filled('title')) {
                $updateData['title'] = $request->input('title');
            }

            if ($request->filled('content')) {
                $updateData['content'] = $request->input('content');
                // If content is updated, mark for reprocessing
                $updateData['status'] = 'pending';
            }

            if ($request->filled('metadata')) {
                $currentMetadata = $document->metadata ?? [];
                $newMetadata = array_merge($currentMetadata, $request->input('metadata'));
                $newMetadata['updated_by'] = $user->id;
                $newMetadata['updated_at'] = now()->toISOString();
                $updateData['metadata'] = $newMetadata;
            }

            $document->update($updateData);

            // Reprocess if content was updated
            if (isset($updateData['content'])) {
                $this->processDocument($document);
            }

            $this->loggingService->logHttpRequest('document_updated', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'document_id' => $documentId,
                'updated_fields' => array_keys($updateData),
            ]);

            return response()->json([
                'success' => true,
                'data' => $document->fresh(),
                'message' => 'Document updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('document_update_error', [
                'document_id' => $documentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to update document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a document
     */
    public function destroy(Request $request, string $documentId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $document = Document::where('id', $documentId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$document) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document not found'
                ], 404);
            }

            // Delete associated file if exists
            if ($document->file_path && Storage::disk('private')->exists($document->file_path)) {
                Storage::disk('private')->delete($document->file_path);
            }

            // Delete associated embeddings
            Embedding::where('document_id', $documentId)->delete();

            $documentTitle = $document->title;
            $document->delete();

            $this->loggingService->logHttpRequest('document_deleted', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'document_id' => $documentId,
                'document_title' => $documentTitle,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('document_deletion_error', [
                'document_id' => $documentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process a document (extract content and generate embeddings)
     */
    public function process(Request $request, string $documentId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $document = Document::where('id', $documentId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$document) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document not found'
                ], 404);
            }

            $result = $this->processDocument($document);

            $this->loggingService->logHttpRequest('document_processed', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'document_id' => $documentId,
                'processing_success' => $result['success'] ?? false,
            ]);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Document processing completed'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('document_processing_error', [
                'document_id' => $documentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Document processing failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search documents using vector similarity
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|max:1000',
                'limit' => 'nullable|integer|min:1|max:50',
                'similarity_threshold' => 'nullable|numeric|min:0|max:1',
                'document_types' => 'nullable|array',
                'document_types.*' => 'string|in:text,pdf,docx,xlsx,csv,json,xml',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();

            // Use RetrieverService for vector search
            $searchResults = $this->retrieverService->searchDocuments(
                $tenant->id,
                $request->input('query'),
                $request->input('limit', 10),
                $request->input('similarity_threshold', 0.7),
                $request->input('document_types', [])
            );

            $this->loggingService->logHttpRequest('documents_searched', [
                'tenant_id' => $tenant->id,
                'query' => $request->input('query'),
                'results_count' => count($searchResults),
            ]);

            return response()->json([
                'success' => true,
                'data' => $searchResults,
                'message' => 'Document search completed successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('document_search_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Document search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download a document file
     */
    public function download(Request $request, string $documentId): \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $document = Document::where('id', $documentId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$document) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document not found'
                ], 404);
            }

            if (!$document->file_path || !Storage::disk('private')->exists($document->file_path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document file not found'
                ], 404);
            }

            $this->loggingService->logHttpRequest('document_downloaded', [
                'tenant_id' => $tenant->id,
                'document_id' => $documentId,
            ]);

            return Storage::disk('private')->download(
                $document->file_path,
                $document->metadata['original_filename'] ?? $document->title
            );

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('document_download_error', [
                'document_id' => $documentId,
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Document download failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get document statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $stats = [
                'total_documents' => Document::where('tenant_id', $tenant->id)->count(),
                'by_type' => Document::where('tenant_id', $tenant->id)
                    ->selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type'),
                'by_status' => Document::where('tenant_id', $tenant->id)
                    ->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status'),
                'total_size' => Document::where('tenant_id', $tenant->id)->sum('file_size'),
                'embeddings_count' => Embedding::where('tenant_id', $tenant->id)->count(),
                'recent_uploads' => Document::where('tenant_id', $tenant->id)
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count(),
            ];

            $this->loggingService->logHttpRequest('document_statistics_retrieved', [
                'tenant_id' => $tenant->id,
            ]);

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Document statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('document_statistics_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve document statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process a document (internal method)
     */
    protected function processDocument(Document $document): array
    {
        try {
            $document->update(['status' => 'processing']);

            // Extract content if file exists
            if ($document->file_path && !$document->content) {
                $content = $this->extractContentFromFile($document);
                $document->update(['content' => $content]);
            }

            // Generate embeddings using RetrieverService
            if ($document->content) {
                $this->retrieverService->generateEmbeddings($document->tenant_id, $document->id, $document->content);
            }

            $document->update([
                'status' => 'processed',
                'processed_at' => now(),
            ]);

            return [
                'success' => true,
                'message' => 'Document processed successfully',
                'document_id' => $document->id,
            ];

        } catch (\Exception $e) {
            $document->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Document processing failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Extract content from file based on type
     */
    protected function extractContentFromFile(Document $document): string
    {
        if (!$document->file_path || !Storage::disk('private')->exists($document->file_path)) {
            throw new \Exception('Document file not found');
        }

        $filePath = Storage::disk('private')->path($document->file_path);

        switch ($document->type) {
            case 'text':
                return file_get_contents($filePath);

            case 'json':
                $json = json_decode(file_get_contents($filePath), true);
                return json_encode($json, JSON_PRETTY_PRINT);

            case 'csv':
                $content = '';
                if (($handle = fopen($filePath, 'r')) !== false) {
                    while (($data = fgetcsv($handle)) !== false) {
                        $content .= implode(', ', $data) . "\n";
                    }
                    fclose($handle);
                }
                return $content;

            case 'xml':
                return file_get_contents($filePath);

            default:
                // For PDF, DOCX, etc., would need additional libraries
                // For now, return placeholder
                return "Content extraction for {$document->type} files requires additional processing libraries.";
        }
    }

    /**
     * Detect file type from extension
     */
    protected function detectFileType(string $extension): string
    {
        $typeMap = [
            'txt' => 'text',
            'pdf' => 'pdf',
            'doc' => 'docx',
            'docx' => 'docx',
            'xls' => 'xlsx',
            'xlsx' => 'xlsx',
            'csv' => 'csv',
            'json' => 'json',
            'xml' => 'xml',
        ];

        return $typeMap[strtolower($extension)] ?? 'text';
    }
}
