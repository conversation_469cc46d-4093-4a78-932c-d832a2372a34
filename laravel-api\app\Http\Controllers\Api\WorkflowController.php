<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Core\TenantContextService;
use App\Services\Core\LoggingService;
use App\Services\MCP\WorkflowService;
use App\Models\Workflow;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class WorkflowController extends Controller
{
    protected TenantContextService $tenantContext;
    protected LoggingService $loggingService;
    protected WorkflowService $workflowService;

    public function __construct(
        TenantContextService $tenantContext,
        LoggingService $loggingService,
        WorkflowService $workflowService
    ) {
        $this->tenantContext = $tenantContext;
        $this->loggingService = $loggingService;
        $this->workflowService = $workflowService;
    }

    /**
     * Get all workflows for the tenant
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $workflows = Workflow::where('tenant_id', $tenant->id)
                ->orderBy('name')
                ->paginate(20);

            $this->loggingService->logHttpRequest('workflows_retrieved', [
                'tenant_id' => $tenant->id,
                'workflows_count' => $workflows->count(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $workflows,
                'message' => 'Workflows retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('workflows_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve workflows',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new workflow
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'steps' => 'required|array|min:1',
                'steps.*.type' => 'required|string|in:tool,condition,loop,parallel,delay',
                'steps.*.configuration' => 'required|array',
                'trigger_conditions' => 'nullable|array',
                'is_active' => 'nullable|boolean',
                'timeout_minutes' => 'nullable|integer|min:1|max:1440',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Check if workflow name already exists for this tenant
            $existingWorkflow = Workflow::where('tenant_id', $tenant->id)
                ->where('name', $request->input('name'))
                ->first();

            if ($existingWorkflow) {
                return response()->json([
                    'success' => false,
                    'message' => 'Workflow with this name already exists'
                ], 422);
            }

            $workflow = Workflow::create([
                'id' => Str::uuid(),
                'tenant_id' => $tenant->id,
                'name' => $request->input('name'),
                'description' => $request->input('description'),
                'steps' => $request->input('steps'),
                'trigger_conditions' => $request->input('trigger_conditions', []),
                'is_active' => $request->input('is_active', true),
                'timeout_minutes' => $request->input('timeout_minutes', 60),
                'execution_count' => 0,
                'success_count' => 0,
                'error_count' => 0,
            ]);

            $this->loggingService->logHttpRequest('workflow_created', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'workflow_id' => $workflow->id,
                'workflow_name' => $workflow->name,
                'steps_count' => count($workflow->steps),
            ]);

            return response()->json([
                'success' => true,
                'data' => $workflow,
                'message' => 'Workflow created successfully'
            ], 201);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('workflow_creation_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to create workflow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific workflow
     */
    public function show(Request $request, string $workflowId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();

            $workflow = Workflow::where('id', $workflowId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$workflow) {
                return response()->json([
                    'success' => false,
                    'message' => 'Workflow not found'
                ], 404);
            }

            $this->loggingService->logHttpRequest('workflow_retrieved', [
                'tenant_id' => $tenant->id,
                'workflow_id' => $workflowId,
            ]);

            return response()->json([
                'success' => true,
                'data' => $workflow,
                'message' => 'Workflow retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('workflow_retrieval_error', [
                'workflow_id' => $workflowId,
                'error' => $e->getMessage(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve workflow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a workflow
     */
    public function update(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:255',
                'description' => 'nullable|string|max:1000',
                'steps' => 'nullable|array|min:1',
                'steps.*.type' => 'required_with:steps|string|in:tool,condition,loop,parallel,delay',
                'steps.*.configuration' => 'required_with:steps|array',
                'trigger_conditions' => 'nullable|array',
                'is_active' => 'nullable|boolean',
                'timeout_minutes' => 'nullable|integer|min:1|max:1440',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $workflow = Workflow::where('id', $workflowId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$workflow) {
                return response()->json([
                    'success' => false,
                    'message' => 'Workflow not found'
                ], 404);
            }

            $updateData = [];

            if ($request->filled('name')) {
                // Check if new name conflicts with existing workflow
                $existingWorkflow = Workflow::where('tenant_id', $tenant->id)
                    ->where('name', $request->input('name'))
                    ->where('id', '!=', $workflowId)
                    ->first();

                if ($existingWorkflow) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Workflow with this name already exists'
                    ], 422);
                }

                $updateData['name'] = $request->input('name');
            }

            if ($request->filled('description')) {
                $updateData['description'] = $request->input('description');
            }

            if ($request->filled('steps')) {
                $updateData['steps'] = $request->input('steps');
            }

            if ($request->filled('trigger_conditions')) {
                $updateData['trigger_conditions'] = $request->input('trigger_conditions');
            }

            if ($request->filled('is_active')) {
                $updateData['is_active'] = $request->input('is_active');
            }

            if ($request->filled('timeout_minutes')) {
                $updateData['timeout_minutes'] = $request->input('timeout_minutes');
            }

            $workflow->update($updateData);

            $this->loggingService->logHttpRequest('workflow_updated', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'workflow_id' => $workflowId,
                'updated_fields' => array_keys($updateData),
            ]);

            return response()->json([
                'success' => true,
                'data' => $workflow->fresh(),
                'message' => 'Workflow updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('workflow_update_error', [
                'workflow_id' => $workflowId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to update workflow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a workflow
     */
    public function destroy(Request $request, string $workflowId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $workflow = Workflow::where('id', $workflowId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$workflow) {
                return response()->json([
                    'success' => false,
                    'message' => 'Workflow not found'
                ], 404);
            }

            $workflowName = $workflow->name;
            $workflow->delete();

            $this->loggingService->logHttpRequest('workflow_deleted', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'workflow_id' => $workflowId,
                'workflow_name' => $workflowName,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Workflow deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('workflow_deletion_error', [
                'workflow_id' => $workflowId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete workflow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Execute a workflow
     */
    public function execute(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'input_data' => 'nullable|array',
                'context' => 'nullable|array',
                'async' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $workflow = Workflow::where('id', $workflowId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$workflow) {
                return response()->json([
                    'success' => false,
                    'message' => 'Workflow not found'
                ], 404);
            }

            if (!$workflow->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Workflow is not active'
                ], 422);
            }

            $async = $request->input('async', false);

            // Execute the workflow using WorkflowService
            $executionResult = $this->workflowService->executeWorkflow(
                $tenant->id,
                $workflowId,
                $request->input('input_data', []),
                $request->input('context', []),
                $async
            );

            $this->loggingService->logMCPOperation('workflow_executed', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'workflow_id' => $workflowId,
                'workflow_name' => $workflow->name,
                'execution_success' => $executionResult['success'] ?? false,
                'execution_time' => $executionResult['execution_time'] ?? 0,
                'async' => $async,
            ]);

            return response()->json([
                'success' => true,
                'data' => $executionResult,
                'message' => 'Workflow executed successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logMCPOperation('workflow_execution_error', [
                'workflow_id' => $workflowId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Workflow execution failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test a workflow
     */
    public function test(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'test_input' => 'nullable|array',
                'dry_run' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $workflow = Workflow::where('id', $workflowId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$workflow) {
                return response()->json([
                    'success' => false,
                    'message' => 'Workflow not found'
                ], 404);
            }

            // Test the workflow
            $testResult = $this->workflowService->testWorkflow(
                $tenant->id,
                $workflowId,
                $request->input('test_input', []),
                $request->input('dry_run', true)
            );

            $this->loggingService->logMCPOperation('workflow_tested', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'workflow_id' => $workflowId,
                'workflow_name' => $workflow->name,
                'test_success' => $testResult['success'] ?? false,
                'dry_run' => $request->input('dry_run', true),
            ]);

            return response()->json([
                'success' => true,
                'data' => $testResult,
                'message' => 'Workflow test completed'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logMCPOperation('workflow_test_error', [
                'workflow_id' => $workflowId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Workflow test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get workflow execution history
     */
    public function getExecutionHistory(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|integer|min:1|max:100',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'status' => 'nullable|string|in:pending,running,completed,failed,cancelled',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();

            $workflow = Workflow::where('id', $workflowId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$workflow) {
                return response()->json([
                    'success' => false,
                    'message' => 'Workflow not found'
                ], 404);
            }

            // Get execution history from WorkflowService
            $history = $this->workflowService->getExecutionHistory(
                $tenant->id,
                $workflowId,
                $request->input('limit', 50),
                $request->input('start_date'),
                $request->input('end_date'),
                $request->input('status')
            );

            $this->loggingService->logHttpRequest('workflow_history_retrieved', [
                'tenant_id' => $tenant->id,
                'workflow_id' => $workflowId,
                'history_count' => count($history),
            ]);

            return response()->json([
                'success' => true,
                'data' => $history,
                'message' => 'Workflow execution history retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('workflow_history_error', [
                'workflow_id' => $workflowId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve workflow execution history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get workflow templates
     */
    public function getTemplates(Request $request): JsonResponse
    {
        try {
            $templates = [
                'data_processing' => [
                    'name' => 'Data Processing Pipeline',
                    'description' => 'Process and transform data through multiple steps',
                    'steps' => [
                        [
                            'type' => 'tool',
                            'name' => 'fetch_data',
                            'configuration' => [
                                'tool_id' => 'data_fetcher',
                                'parameters' => ['source' => 'api'],
                            ],
                        ],
                        [
                            'type' => 'tool',
                            'name' => 'transform_data',
                            'configuration' => [
                                'tool_id' => 'data_transformer',
                                'parameters' => ['format' => 'json'],
                            ],
                        ],
                        [
                            'type' => 'tool',
                            'name' => 'save_data',
                            'configuration' => [
                                'tool_id' => 'data_saver',
                                'parameters' => ['destination' => 'database'],
                            ],
                        ],
                    ],
                ],
                'approval_workflow' => [
                    'name' => 'Approval Workflow',
                    'description' => 'Multi-step approval process with conditions',
                    'steps' => [
                        [
                            'type' => 'condition',
                            'name' => 'check_amount',
                            'configuration' => [
                                'condition' => 'amount > 1000',
                                'true_step' => 'manager_approval',
                                'false_step' => 'auto_approve',
                            ],
                        ],
                        [
                            'type' => 'tool',
                            'name' => 'manager_approval',
                            'configuration' => [
                                'tool_id' => 'send_notification',
                                'parameters' => ['recipient' => 'manager'],
                            ],
                        ],
                        [
                            'type' => 'tool',
                            'name' => 'auto_approve',
                            'configuration' => [
                                'tool_id' => 'approve_request',
                                'parameters' => ['auto' => true],
                            ],
                        ],
                    ],
                ],
                'notification_cascade' => [
                    'name' => 'Notification Cascade',
                    'description' => 'Send notifications through multiple channels',
                    'steps' => [
                        [
                            'type' => 'parallel',
                            'name' => 'send_notifications',
                            'configuration' => [
                                'steps' => [
                                    [
                                        'type' => 'tool',
                                        'configuration' => [
                                            'tool_id' => 'email_sender',
                                            'parameters' => ['channel' => 'email'],
                                        ],
                                    ],
                                    [
                                        'type' => 'tool',
                                        'configuration' => [
                                            'tool_id' => 'sms_sender',
                                            'parameters' => ['channel' => 'sms'],
                                        ],
                                    ],
                                    [
                                        'type' => 'tool',
                                        'configuration' => [
                                            'tool_id' => 'slack_sender',
                                            'parameters' => ['channel' => 'slack'],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
                'scheduled_report' => [
                    'name' => 'Scheduled Report Generation',
                    'description' => 'Generate and distribute reports on schedule',
                    'steps' => [
                        [
                            'type' => 'tool',
                            'name' => 'generate_report',
                            'configuration' => [
                                'tool_id' => 'report_generator',
                                'parameters' => ['type' => 'monthly'],
                            ],
                        ],
                        [
                            'type' => 'condition',
                            'name' => 'check_report_size',
                            'configuration' => [
                                'condition' => 'file_size < 10MB',
                                'true_step' => 'email_report',
                                'false_step' => 'upload_to_storage',
                            ],
                        ],
                        [
                            'type' => 'tool',
                            'name' => 'email_report',
                            'configuration' => [
                                'tool_id' => 'email_sender',
                                'parameters' => ['attachment' => true],
                            ],
                        ],
                        [
                            'type' => 'tool',
                            'name' => 'upload_to_storage',
                            'configuration' => [
                                'tool_id' => 'file_uploader',
                                'parameters' => ['storage' => 'cloud'],
                            ],
                        ],
                    ],
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $templates,
                'message' => 'Workflow templates retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('workflow_templates_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve workflow templates',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
