<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id')->nullable(); // Nullable for system-wide logs
            $table->uuid('user_id')->nullable();

            // Log Classification
            $table->enum('log_type', [
                'http_request', 'websocket_event', 'ai_provider_request', 'ai_provider_response',
                'mcp_agent', 'tool_execution', 'workflow_execution', 'error', 'security',
                'system_health', 'performance', 'audit', 'debug'
            ]);
            $table->enum('log_level', ['emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug'])->default('info');
            $table->string('log_channel')->default('default');

            // Request/Response Information
            $table->string('method')->nullable(); // GET, POST, etc.
            $table->text('url')->nullable();
            $table->string('route_name')->nullable();
            $table->string('controller_action')->nullable();
            $table->integer('status_code')->nullable();
            $table->decimal('response_time_ms', 10, 2)->nullable();

            // Content & Context
            $table->text('message');
            $table->longText('context')->nullable(); // JSON context data
            $table->longText('request_data')->nullable(); // Request payload
            $table->longText('response_data')->nullable(); // Response payload
            $table->json('headers')->nullable();
            $table->json('metadata')->nullable();

            // User & Session Information
            $table->string('session_id')->nullable();
            $table->string('websocket_session_id')->nullable();
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->string('referer')->nullable();

            // AI Provider Specific
            $table->uuid('ai_provider_id')->nullable();
            $table->string('ai_model')->nullable();
            $table->decimal('tokens_used', 15, 0)->nullable();
            $table->decimal('estimated_cost', 10, 6)->nullable();
            $table->json('ai_parameters')->nullable();

            // MCP Agent Information
            $table->string('mcp_agent_type')->nullable(); // intent, retriever, tool, etc.
            $table->string('mcp_agent_name')->nullable();
            $table->json('mcp_input')->nullable();
            $table->json('mcp_output')->nullable();
            $table->decimal('mcp_processing_time_ms', 10, 2)->nullable();

            // Tool Execution
            $table->uuid('tool_id')->nullable();
            $table->string('tool_name')->nullable();
            $table->json('tool_parameters')->nullable();
            $table->json('tool_result')->nullable();
            $table->boolean('tool_success')->nullable();

            // Workflow Information
            $table->uuid('workflow_id')->nullable();
            $table->string('workflow_step')->nullable();
            $table->integer('workflow_step_number')->nullable();
            $table->json('workflow_context')->nullable();

            // Error Information
            $table->string('error_code')->nullable();
            $table->text('error_message')->nullable();
            $table->longText('stack_trace')->nullable();
            $table->string('error_file')->nullable();
            $table->integer('error_line')->nullable();
            $table->json('error_context')->nullable();

            // Security Events
            $table->enum('security_event_type', [
                'login_attempt', 'login_success', 'login_failure', 'logout',
                'permission_denied', 'rate_limit_exceeded', 'suspicious_activity',
                'data_breach_attempt', 'unauthorized_access'
            ])->nullable();
            $table->boolean('is_security_incident')->default(false);
            $table->integer('risk_score')->nullable(); // 1-10

            // Performance Metrics
            $table->decimal('memory_usage_mb', 10, 2)->nullable();
            $table->decimal('cpu_usage_percent', 5, 2)->nullable();
            $table->integer('database_queries_count')->nullable();
            $table->decimal('database_query_time_ms', 10, 2)->nullable();
            $table->integer('cache_hits')->nullable();
            $table->integer('cache_misses')->nullable();

            // System Health
            $table->enum('health_status', ['healthy', 'warning', 'critical', 'down'])->nullable();
            $table->json('health_metrics')->nullable();
            $table->string('service_name')->nullable();
            $table->string('service_version')->nullable();

            // Correlation & Tracing
            $table->string('correlation_id')->nullable(); // For tracing requests
            $table->string('trace_id')->nullable();
            $table->string('span_id')->nullable();
            $table->uuid('parent_log_id')->nullable();

            // Compliance & Audit
            $table->boolean('contains_pii')->default(false);
            $table->boolean('is_audit_log')->default(false);
            $table->json('compliance_tags')->nullable();
            $table->timestamp('retention_expires_at')->nullable();

            // Processing & Analysis
            $table->boolean('is_processed')->default(false);
            $table->timestamp('processed_at')->nullable();
            $table->json('analysis_results')->nullable();
            $table->boolean('requires_attention')->default(false);
            $table->boolean('is_archived')->default(false);

            // Additional Context
            $table->string('environment')->default('production'); // production, staging, development
            $table->string('server_name')->nullable();
            $table->string('application_version')->nullable();
            $table->json('tags')->nullable(); // Custom tags for filtering

            $table->timestamp('logged_at'); // When the event actually occurred
            $table->timestamps(); // When the log was created/updated

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('ai_provider_id')->references('id')->on('ai_providers')->onDelete('set null');
            $table->foreign('tool_id')->references('id')->on('tools')->onDelete('set null');
            $table->foreign('workflow_id')->references('id')->on('workflows')->onDelete('set null');
            $table->foreign('parent_log_id')->references('id')->on('system_logs')->onDelete('set null');

            // Indexes for performance
            $table->index(['tenant_id', 'log_type', 'log_level']);
            $table->index(['tenant_id', 'logged_at']);
            $table->index(['log_type', 'log_level', 'logged_at']);
            $table->index(['user_id', 'logged_at']);
            $table->index(['correlation_id', 'trace_id']);
            $table->index(['is_security_incident', 'risk_score']);
            $table->index(['requires_attention', 'is_processed']);
            $table->index(['ai_provider_id', 'logged_at']);
            $table->index(['error_code', 'logged_at']);
            $table->index('session_id');
            $table->index('ip_address');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_logs');
    }
};
