import React from 'react';
import Link from 'next/link';
import { config } from '@/lib/config';

interface AuthLayoutProps {
    children: React.ReactNode;
    title: string;
    subtitle: string;
    showBackToHome?: boolean;
}

export default function AuthLayout({
    children,
    title,
    subtitle,
    showBackToHome = true
}: AuthLayoutProps) {
    return (
        <div className="min-h-screen flex">
            {/* Left Column - Brand & Visual */}
            <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
                {/* Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800" />

                {/* Overlay Pattern */}
                <div className="absolute inset-0 bg-black/20" />
                <div
                    className="absolute inset-0 opacity-10"
                    style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                    }}
                />

                {/* Content */}
                <div className="relative z-10 flex flex-col justify-between p-8 lg:p-12 text-white">
                    {/* Logo & Brand */}
                    <div className="animate-in fade-in slide-in-from-left-4 duration-700">
                        <Link href="/" className="inline-block group">
                            <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-all duration-300 group-hover:scale-110">
                                    <svg
                                        className="w-6 h-6 text-white"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M13 10V3L4 14h7v7l9-11h-7z"
                                        />
                                    </svg>
                                </div>
                                <span className="text-2xl font-bold group-hover:text-blue-100 transition-colors duration-300">
                                    {config.app.name}
                                </span>
                            </div>
                        </Link>
                    </div>

                    {/* Main Content */}
                    <div className="space-y-6 animate-in fade-in slide-in-from-left-4 duration-700 delay-200">
                        <div className="space-y-4">
                            <h1 className="text-3xl lg:text-4xl font-bold leading-tight">
                                Universal AI
                                <br />
                                <span className="text-blue-200">Orchestration</span>
                            </h1>
                            <p className="text-lg lg:text-xl text-blue-100 leading-relaxed max-w-md">
                                Connect, coordinate, and control multiple AI providers from a single, powerful platform.
                            </p>
                        </div>

                        {/* Features */}
                        <div className="space-y-3">
                            <div className="flex items-center space-x-3 animate-in fade-in slide-in-from-left-4 duration-700 delay-300">
                                <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse" />
                                <span className="text-blue-100">Multi-provider AI integration</span>
                            </div>
                            <div className="flex items-center space-x-3 animate-in fade-in slide-in-from-left-4 duration-700 delay-400">
                                <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse delay-100" />
                                <span className="text-blue-100">Real-time collaboration</span>
                            </div>
                            <div className="flex items-center space-x-3 animate-in fade-in slide-in-from-left-4 duration-700 delay-500">
                                <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse delay-200" />
                                <span className="text-blue-100">Advanced workflow automation</span>
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="text-sm text-blue-200 animate-in fade-in slide-in-from-left-4 duration-700 delay-600">
                        © 2024 {config.app.name}. All rights reserved.
                    </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute top-20 right-20 w-32 h-32 bg-white/5 rounded-full blur-xl animate-pulse" />
                <div className="absolute bottom-32 right-32 w-24 h-24 bg-blue-300/10 rounded-full blur-lg animate-pulse delay-1000" />
                <div className="absolute top-1/2 right-10 w-16 h-16 bg-purple-300/10 rounded-full blur-md animate-pulse delay-500" />
            </div>

            {/* Right Column - Form */}
            <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-20 xl:px-24 bg-gray-50 min-h-screen">
                <div className="mx-auto w-full max-w-sm lg:max-w-md animate-in fade-in slide-in-from-right-4 duration-700">
                    {/* Mobile Logo */}
                    <div className="lg:hidden text-center mb-8">
                        <Link href="/" className="inline-block group">
                            <div className="flex items-center justify-center space-x-3">
                                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center group-hover:from-blue-700 group-hover:to-indigo-700 transition-all duration-300 group-hover:scale-110">
                                    <svg
                                        className="w-6 h-6 text-white"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M13 10V3L4 14h7v7l9-11h-7z"
                                        />
                                    </svg>
                                </div>
                                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                                    {config.app.name}
                                </span>
                            </div>
                        </Link>
                    </div>

                    {/* Header */}
                    <div className="text-center mb-8">
                        <h2 className="text-3xl font-bold text-gray-900 mb-2">
                            {title}
                        </h2>
                        <p className="text-gray-600">
                            {subtitle}
                        </p>
                    </div>

                    {/* Form Content */}
                    <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6 lg:p-8 hover:shadow-2xl transition-shadow duration-300">
                        {children}
                    </div>

                    {/* Back to Home */}
                    {showBackToHome && (
                        <div className="text-center mt-8">
                            <Link
                                href="/"
                                className="text-sm text-gray-500 hover:text-gray-700 transition-colors inline-flex items-center space-x-1 group"
                            >
                                <svg className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                </svg>
                                <span>Back to home</span>
                            </Link>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
} 