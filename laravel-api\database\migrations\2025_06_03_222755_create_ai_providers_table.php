<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_providers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id');
            $table->uuid('created_by_user_id');

            // Provider Information
            $table->enum('provider_name', [
                'openai', 'groq', 'huggingface', 'gemini', 'deepseek',
                'mistral', 'codestral', 'anthropic', 'openrouter', 'grok'
            ]);
            $table->string('display_name');
            $table->text('description')->nullable();

            // Configuration
            $table->text('api_key'); // Encrypted
            $table->string('organization_id')->nullable(); // For OpenAI, etc.
            $table->string('base_url')->nullable(); // Custom endpoints
            $table->json('configuration')->nullable(); // Provider-specific settings
            $table->json('available_models')->nullable(); // Cached model list
            $table->string('default_model')->nullable();

            // Rate Limiting & Usage
            $table->integer('rate_limit_per_minute')->default(60);
            $table->integer('rate_limit_per_hour')->default(1000);
            $table->integer('rate_limit_per_day')->default(10000);
            $table->decimal('cost_per_1k_tokens', 8, 6)->nullable();
            $table->decimal('cost_per_1k_output_tokens', 8, 6)->nullable();

            // Usage Tracking
            $table->decimal('tokens_used_today', 15, 0)->default(0);
            $table->decimal('tokens_used_this_month', 15, 0)->default(0);
            $table->decimal('total_tokens_used', 15, 0)->default(0);
            $table->integer('requests_today')->default(0);
            $table->integer('requests_this_month')->default(0);
            $table->integer('total_requests')->default(0);
            $table->decimal('total_cost', 10, 2)->default(0);

            // Performance Metrics
            $table->decimal('average_response_time_ms', 8, 2)->nullable();
            $table->decimal('success_rate_percentage', 5, 2)->default(100);
            $table->integer('error_count_today')->default(0);
            $table->timestamp('last_successful_request')->nullable();
            $table->timestamp('last_error')->nullable();
            $table->text('last_error_message')->nullable();

            // Health Monitoring
            $table->enum('health_status', ['healthy', 'degraded', 'unhealthy', 'unknown'])->default('unknown');
            $table->timestamp('last_health_check')->nullable();
            $table->json('health_check_details')->nullable();
            $table->boolean('auto_failover_enabled')->default(false);
            $table->integer('failover_priority')->default(1); // Lower = higher priority

            // Features & Capabilities
            $table->boolean('supports_streaming')->default(false);
            $table->boolean('supports_function_calling')->default(false);
            $table->boolean('supports_vision')->default(false);
            $table->boolean('supports_code_generation')->default(false);
            $table->integer('max_context_length')->nullable();
            $table->json('supported_features')->nullable();

            // Security & Access
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->json('allowed_user_roles')->nullable(); // Which roles can use this provider
            $table->json('restricted_features')->nullable(); // Feature restrictions

            // Webhook & Notifications
            $table->string('webhook_url')->nullable();
            $table->string('webhook_secret')->nullable();
            $table->boolean('notifications_enabled')->default(true);
            $table->json('notification_thresholds')->nullable(); // Error rates, costs, etc.

            // Status & Metadata
            $table->enum('status', ['active', 'inactive', 'testing', 'maintenance'])->default('active');
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('created_by_user_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->unique(['tenant_id', 'provider_name']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['tenant_id', 'is_default']);
            $table->index(['health_status', 'is_active']);
            $table->index('last_health_check');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_providers');
    }
};
