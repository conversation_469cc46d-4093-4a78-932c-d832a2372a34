<?php

namespace App\Listeners\WebSocket;

use App\Events\WebSocket\NotificationEvent;
use App\Services\Core\LoggingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;

class NotificationListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected LoggingService $loggingService;

    /**
     * Create the event listener.
     */
    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    /**
     * Handle the event.
     */
    public function handle(NotificationEvent $event): void
    {
        try {
            // Log the notification event
            $this->loggingService->logWebSocketEvent('notification_broadcasted', [
                'tenant_id' => $event->tenantId,
                'user_id' => $event->userId,
                'notification_id' => $event->notificationId,
                'notification_type' => $event->notificationType,
                'priority' => $event->priority,
                'timestamp' => $event->timestamp,
            ]);

            // Store notification for persistence
            $this->storeNotification($event);

            // Handle priority-based processing
            $this->processByPriority($event);

        } catch (\Exception $e) {
            $this->loggingService->logWebSocketEvent('notification_listener_error', [
                'tenant_id' => $event->tenantId,
                'notification_id' => $event->notificationId,
                'error' => $e->getMessage(),
            ], 'error');

            throw $e;
        }
    }

    /**
     * Store notification for persistence
     */
    protected function storeNotification(NotificationEvent $event): void
    {
        $notificationData = [
            'notification_id' => $event->notificationId,
            'tenant_id' => $event->tenantId,
            'user_id' => $event->userId,
            'type' => $event->notificationType,
            'title' => $event->title,
            'message' => $event->message,
            'data' => $event->data,
            'priority' => $event->priority,
            'metadata' => $event->metadata,
            'created_at' => $event->timestamp,
            'read_at' => null,
        ];

        // Store in cache for quick access
        $cacheKey = "notification:{$event->tenantId}:{$event->notificationId}";
        Cache::put($cacheKey, $notificationData, 86400); // 24 hours

        // Also add to user's notification list
        if ($event->userId) {
            $userNotificationsKey = "user_notifications:{$event->tenantId}:{$event->userId}";
            $userNotifications = Cache::get($userNotificationsKey, []);
            $userNotifications[] = $event->notificationId;
            Cache::put($userNotificationsKey, $userNotifications, 86400);
        }
    }

    /**
     * Process notification by priority
     */
    protected function processByPriority(NotificationEvent $event): void
    {
        switch ($event->priority) {
            case 'critical':
                $this->handleCriticalNotification($event);
                break;

            case 'high':
                $this->handleHighPriorityNotification($event);
                break;

            case 'normal':
                $this->handleNormalNotification($event);
                break;

            case 'low':
                $this->handleLowPriorityNotification($event);
                break;
        }
    }

    /**
     * Handle critical notification
     */
    protected function handleCriticalNotification(NotificationEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('critical_notification_processed', [
            'tenant_id' => $event->tenantId,
            'notification_id' => $event->notificationId,
            'title' => $event->title,
        ]);

        // Critical notifications might trigger additional alerts
        // This could integrate with external alerting systems
    }

    /**
     * Handle high priority notification
     */
    protected function handleHighPriorityNotification(NotificationEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('high_priority_notification_processed', [
            'tenant_id' => $event->tenantId,
            'notification_id' => $event->notificationId,
            'title' => $event->title,
        ]);
    }

    /**
     * Handle normal notification
     */
    protected function handleNormalNotification(NotificationEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('normal_notification_processed', [
            'tenant_id' => $event->tenantId,
            'notification_id' => $event->notificationId,
        ]);
    }

    /**
     * Handle low priority notification
     */
    protected function handleLowPriorityNotification(NotificationEvent $event): void
    {
        $this->loggingService->logWebSocketEvent('low_priority_notification_processed', [
            'tenant_id' => $event->tenantId,
            'notification_id' => $event->notificationId,
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(NotificationEvent $event, \Throwable $exception): void
    {
        $this->loggingService->logWebSocketEvent('notification_listener_failed', [
            'tenant_id' => $event->tenantId,
            'notification_id' => $event->notificationId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');
    }
}
