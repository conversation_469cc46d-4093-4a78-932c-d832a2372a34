<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('websocket_sessions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id');
            $table->uuid('user_id');

            // Session Information
            $table->string('session_id')->unique();
            $table->string('socket_id')->unique();
            $table->string('channel_name');
            $table->enum('connection_type', ['private', 'presence', 'public'])->default('private');

            // Connection Details
            $table->string('ip_address');
            $table->text('user_agent');
            $table->string('origin')->nullable();
            $table->string('referer')->nullable();
            $table->json('headers')->nullable();

            // Authentication & Authorization
            $table->string('auth_token')->nullable();
            $table->timestamp('authenticated_at')->nullable();
            $table->json('permissions')->nullable();
            $table->json('subscribed_channels')->nullable();

            // Connection Status
            $table->enum('status', ['connecting', 'connected', 'disconnected', 'error'])->default('connecting');
            $table->timestamp('connected_at')->nullable();
            $table->timestamp('disconnected_at')->nullable();
            $table->timestamp('last_ping_at')->nullable();
            $table->timestamp('last_pong_at')->nullable();
            $table->integer('ping_interval_ms')->default(30000);

            // Activity Tracking
            $table->integer('messages_sent')->default(0);
            $table->integer('messages_received')->default(0);
            $table->integer('events_triggered')->default(0);
            $table->integer('events_received')->default(0);
            $table->timestamp('last_activity_at')->nullable();
            $table->json('last_message')->nullable();

            // Performance Metrics
            $table->decimal('average_latency_ms', 8, 2)->nullable();
            $table->integer('total_bytes_sent')->default(0);
            $table->integer('total_bytes_received')->default(0);
            $table->integer('connection_errors')->default(0);
            $table->integer('reconnection_attempts')->default(0);

            // Chat Session Association
            $table->uuid('chat_session_id')->nullable();
            $table->boolean('is_typing')->default(false);
            $table->timestamp('typing_started_at')->nullable();
            $table->json('typing_users')->nullable(); // For group chats

            // Device & Browser Information
            $table->string('device_type')->nullable(); // mobile, desktop, tablet
            $table->string('browser_name')->nullable();
            $table->string('browser_version')->nullable();
            $table->string('os_name')->nullable();
            $table->string('os_version')->nullable();
            $table->json('device_info')->nullable();

            // Rate Limiting
            $table->integer('rate_limit_remaining')->default(100);
            $table->timestamp('rate_limit_reset_at')->nullable();
            $table->integer('rate_limit_violations')->default(0);

            // Error Tracking
            $table->text('last_error_message')->nullable();
            $table->timestamp('last_error_at')->nullable();
            $table->json('error_history')->nullable(); // Last 10 errors

            // Presence Information (for presence channels)
            $table->json('presence_data')->nullable();
            $table->boolean('is_visible')->default(true);
            $table->enum('presence_status', ['online', 'away', 'busy', 'offline'])->default('online');

            // Session Metadata
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->index(['tenant_id', 'user_id']);
            $table->index(['tenant_id', 'status']);
            $table->index(['channel_name', 'status']);
            $table->index(['connected_at', 'disconnected_at']);
            $table->index('last_activity_at');
            $table->index('chat_session_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('websocket_sessions');
    }
};
