<?php

namespace App\Services\Core;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class TenantContextService
{
    /**
     * Current tenant instance
     */
    protected ?Tenant $currentTenant = null;

    /**
     * Current user instance
     */
    protected ?User $currentUser = null;

    /**
     * Tenant resolution methods in order of priority
     */
    protected array $resolutionMethods = [
        'header',
        'subdomain',
        'api_key',
        'user_token',
        'request_parameter',
    ];

    /**
     * Cache configuration
     */
    protected array $cacheConfig = [
        'enabled' => true,
        'ttl' => 3600, // 1 hour
        'prefix' => 'tenant_context:',
    ];

    /**
     * Tenant context configuration
     */
    protected array $config = [
        'strict_isolation' => true,
        'validate_subscriptions' => true,
        'cache_tenant_data' => true,
        'require_active_subscription' => true,
        'allow_trial_access' => true,
        'max_inactive_days' => 30,
    ];

    public function __construct()
    {
        $this->loadConfiguration();
    }

    /**
     * Resolve tenant context from request
     */
    public function resolveTenantContext(Request $request): ?Tenant
    {
        // Try each resolution method in order
        foreach ($this->resolutionMethods as $method) {
            $tenant = $this->{"resolveFrom" . ucfirst($method)}($request);

            if ($tenant) {
                $this->setCurrentTenant($tenant);
                return $tenant;
            }
        }

        return null;
    }

    /**
     * Set current tenant
     */
    public function setCurrentTenant(?Tenant $tenant): void
    {
        $this->currentTenant = $tenant;

        if ($tenant) {
            // Validate tenant status
            $this->validateTenantStatus($tenant);

            // Cache tenant data if enabled
            if ($this->config['cache_tenant_data']) {
                $this->cacheTenantData($tenant);
            }
        }
    }

    /**
     * Get current tenant
     */
    public function getCurrentTenant(): ?Tenant
    {
        return $this->currentTenant;
    }

    /**
     * Get current tenant ID
     */
    public function getCurrentTenantId(): ?string
    {
        return $this->currentTenant?->id;
    }

    /**
     * Set current user
     */
    public function setCurrentUser(?User $user): void
    {
        $this->currentUser = $user;

        // If user is set and no tenant is set, try to resolve from user
        if ($user && !$this->currentTenant) {
            $this->setCurrentTenant($user->tenant);
        }
    }

    /**
     * Get current user
     */
    public function getCurrentUser(): ?User
    {
        return $this->currentUser ?? Auth::user();
    }

    /**
     * Validate tenant context is available
     */
    public function validateTenantContext(): void
    {
        if (!$this->currentTenant) {
            throw new \Exception('Tenant context is required but not available');
        }

        $this->validateTenantStatus($this->currentTenant);
    }

    /**
     * Check if tenant has access to feature
     */
    public function hasFeatureAccess(string $feature): bool
    {
        if (!$this->currentTenant) {
            return false;
        }

        $featureFlags = $this->currentTenant->feature_flags ?? [];
        return $featureFlags[$feature] ?? false;
    }

    /**
     * Check if tenant is within usage limits
     */
    public function isWithinUsageLimits(string $limitType): bool
    {
        if (!$this->currentTenant) {
            return false;
        }

        $limits = $this->currentTenant->usage_limits ?? [];
        $currentUsage = $this->getCurrentUsage($limitType);
        $limit = $limits[$limitType] ?? null;

        if ($limit === null) {
            return true; // No limit set
        }

        return $currentUsage < $limit;
    }

    /**
     * Get tenant subscription status
     */
    public function getSubscriptionStatus(): array
    {
        if (!$this->currentTenant) {
            return [
                'status' => 'no_tenant',
                'active' => false,
                'trial' => false,
                'expires_at' => null,
            ];
        }

        $tenant = $this->currentTenant;

        return [
            'status' => $tenant->subscription_status,
            'active' => $tenant->subscription_status === 'active',
            'trial' => $tenant->subscription_status === 'trial',
            'expires_at' => $tenant->subscription_expires_at,
            'plan' => $tenant->subscription_plan,
            'billing_cycle' => $tenant->billing_cycle,
        ];
    }

    /**
     * Switch tenant context (for admin users)
     */
    public function switchTenantContext(string $tenantId): bool
    {
        $user = $this->getCurrentUser();

        if (!$user || !$user->canSwitchTenants()) {
            return false;
        }

        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            return false;
        }

        $this->setCurrentTenant($tenant);
        return true;
    }

    /**
     * Get tenant isolation scope for database queries
     */
    public function getTenantScope(): array
    {
        if (!$this->currentTenant) {
            throw new \Exception('Tenant context required for database scoping');
        }

        return ['tenant_id' => $this->currentTenant->id];
    }

    /**
     * Resolve tenant from X-Tenant-ID header
     */
    protected function resolveFromHeader(Request $request): ?Tenant
    {
        $tenantId = $request->header('X-Tenant-ID');

        if (!$tenantId) {
            return null;
        }

        return $this->findTenantById($tenantId);
    }

    /**
     * Resolve tenant from subdomain
     */
    protected function resolveFromSubdomain(Request $request): ?Tenant
    {
        $host = $request->getHost();
        $parts = explode('.', $host);

        if (count($parts) < 3) {
            return null; // No subdomain
        }

        $subdomain = $parts[0];

        if (in_array($subdomain, ['www', 'api', 'admin'])) {
            return null; // Reserved subdomains
        }

        return $this->findTenantBySubdomain($subdomain);
    }

    /**
     * Resolve tenant from API key
     */
    protected function resolveFromApiKey(Request $request): ?Tenant
    {
        $apiKey = $request->header('X-API-Key') ?? $request->get('api_key');

        if (!$apiKey) {
            return null;
        }

        return $this->findTenantByApiKey($apiKey);
    }

    /**
     * Resolve tenant from user token
     */
    protected function resolveFromUserToken(Request $request): ?Tenant
    {
        $user = Auth::user();

        if (!$user) {
            return null;
        }

        $this->setCurrentUser($user);
        return $user->tenant;
    }

    /**
     * Resolve tenant from request parameter
     */
    protected function resolveFromRequestParameter(Request $request): ?Tenant
    {
        $tenantId = $request->get('tenant_id');

        if (!$tenantId) {
            return null;
        }

        return $this->findTenantById($tenantId);
    }

    /**
     * Find tenant by ID with caching
     */
    protected function findTenantById(string $tenantId): ?Tenant
    {
        if (!$this->config['cache_tenant_data']) {
            return Tenant::find($tenantId);
        }

        $cacheKey = $this->getCacheKey("tenant_id:{$tenantId}");

        return Cache::remember($cacheKey, $this->cacheConfig['ttl'], function () use ($tenantId) {
            return Tenant::find($tenantId);
        });
    }

    /**
     * Find tenant by subdomain with caching
     */
    protected function findTenantBySubdomain(string $subdomain): ?Tenant
    {
        if (!$this->config['cache_tenant_data']) {
            return Tenant::where('subdomain', $subdomain)->first();
        }

        $cacheKey = $this->getCacheKey("subdomain:{$subdomain}");

        return Cache::remember($cacheKey, $this->cacheConfig['ttl'], function () use ($subdomain) {
            return Tenant::where('subdomain', $subdomain)->first();
        });
    }

    /**
     * Find tenant by API key with caching
     */
    protected function findTenantByApiKey(string $apiKey): ?Tenant
    {
        if (!$this->config['cache_tenant_data']) {
            return Tenant::where('api_key', $apiKey)->first();
        }

        $cacheKey = $this->getCacheKey("api_key:" . hash('sha256', $apiKey));

        return Cache::remember($cacheKey, $this->cacheConfig['ttl'], function () use ($apiKey) {
            return Tenant::where('api_key', $apiKey)->first();
        });
    }

    /**
     * Validate tenant status and subscription
     */
    protected function validateTenantStatus(Tenant $tenant): void
    {
        // Check if tenant is active
        if ($tenant->status !== 'active') {
            throw new \Exception("Tenant is not active: {$tenant->status}");
        }

        // Check subscription if validation is enabled
        if ($this->config['validate_subscriptions']) {
            $this->validateSubscription($tenant);
        }

        // Check if tenant has been inactive too long
        if ($this->config['max_inactive_days'] > 0) {
            $this->validateInactivityPeriod($tenant);
        }
    }

    /**
     * Validate tenant subscription
     */
    protected function validateSubscription(Tenant $tenant): void
    {
        $subscriptionStatus = $tenant->subscription_status;

        // Allow trial access if configured
        if ($subscriptionStatus === 'trial' && $this->config['allow_trial_access']) {
            // Check if trial has expired
            if ($tenant->subscription_expires_at && $tenant->subscription_expires_at->isPast()) {
                throw new \Exception('Trial subscription has expired');
            }
            return;
        }

        // Require active subscription if configured
        if ($this->config['require_active_subscription'] && $subscriptionStatus !== 'active') {
            throw new \Exception("Active subscription required. Current status: {$subscriptionStatus}");
        }

        // Check if subscription has expired
        if ($tenant->subscription_expires_at && $tenant->subscription_expires_at->isPast()) {
            throw new \Exception('Subscription has expired');
        }
    }

    /**
     * Validate tenant inactivity period
     */
    protected function validateInactivityPeriod(Tenant $tenant): void
    {
        if (!$tenant->last_activity_at) {
            return; // No activity recorded yet
        }

        $maxInactiveDays = $this->config['max_inactive_days'];
        $inactiveSince = $tenant->last_activity_at->diffInDays(now());

        if ($inactiveSince > $maxInactiveDays) {
            throw new \Exception("Tenant has been inactive for {$inactiveSince} days (max: {$maxInactiveDays})");
        }
    }

    /**
     * Cache tenant data
     */
    protected function cacheTenantData(Tenant $tenant): void
    {
        if (!$this->cacheConfig['enabled']) {
            return;
        }

        $cacheKey = $this->getCacheKey("tenant_data:{$tenant->id}");
        Cache::put($cacheKey, $tenant, $this->cacheConfig['ttl']);
    }

    /**
     * Get current usage for a specific limit type
     */
    protected function getCurrentUsage(string $limitType): int
    {
        if (!$this->currentTenant) {
            return 0;
        }

        // This would typically query usage tracking tables
        // For now, return a placeholder
        return 0;
    }

    /**
     * Get cache key with prefix
     */
    protected function getCacheKey(string $key): string
    {
        return $this->cacheConfig['prefix'] . $key;
    }

    /**
     * Load configuration
     */
    protected function loadConfiguration(): void
    {
        $this->config = array_merge($this->config, config('tenant_context', []));
        $this->cacheConfig = array_merge($this->cacheConfig, config('tenant_context.cache', []));
    }

    /**
     * Update tenant activity timestamp
     */
    public function updateTenantActivity(): void
    {
        if (!$this->currentTenant) {
            return;
        }

        // Update activity timestamp (throttled to avoid too many updates)
        $lastUpdate = Cache::get("tenant_activity_updated:{$this->currentTenant->id}");

        if (!$lastUpdate || now()->diffInMinutes($lastUpdate) >= 5) {
            $this->currentTenant->update(['last_activity_at' => now()]);
            Cache::put("tenant_activity_updated:{$this->currentTenant->id}", now(), 300); // 5 minutes
        }
    }

    /**
     * Get tenant permissions
     */
    public function getTenantPermissions(): array
    {
        if (!$this->currentTenant) {
            return [];
        }

        return $this->currentTenant->permissions ?? [];
    }

    /**
     * Check if tenant has specific permission
     */
    public function hasPermission(string $permission): bool
    {
        $permissions = $this->getTenantPermissions();
        return in_array($permission, $permissions);
    }

    /**
     * Get tenant configuration
     */
    public function getTenantConfiguration(): array
    {
        if (!$this->currentTenant) {
            return [];
        }

        return $this->currentTenant->settings ?? [];
    }

    /**
     * Clear tenant cache
     */
    public function clearTenantCache(?string $tenantId = null): void
    {
        $tenantId = $tenantId ?? $this->getCurrentTenantId();

        if (!$tenantId) {
            return;
        }

        $keys = [
            "tenant_id:{$tenantId}",
            "tenant_data:{$tenantId}",
            "tenant_activity_updated:{$tenantId}",
        ];

        foreach ($keys as $key) {
            Cache::forget($this->getCacheKey($key));
        }

        // Also clear by subdomain and API key if we have the tenant
        if ($this->currentTenant) {
            if ($this->currentTenant->subdomain) {
                Cache::forget($this->getCacheKey("subdomain:{$this->currentTenant->subdomain}"));
            }

            if ($this->currentTenant->api_key) {
                $apiKeyHash = hash('sha256', $this->currentTenant->api_key);
                Cache::forget($this->getCacheKey("api_key:{$apiKeyHash}"));
            }
        }
    }

    /**
     * Get service health status
     */
    public function getHealthStatus(): array
    {
        $tenantCount = Tenant::count();
        $activeTenants = Tenant::where('status', 'active')->count();
        $trialTenants = Tenant::where('subscription_status', 'trial')->count();

        return [
            'service' => 'tenant_context',
            'status' => 'enabled',
            'current_tenant_id' => $this->getCurrentTenantId(),
            'strict_isolation' => $this->config['strict_isolation'],
            'validate_subscriptions' => $this->config['validate_subscriptions'],
            'cache_enabled' => $this->cacheConfig['enabled'],
            'total_tenants' => $tenantCount,
            'active_tenants' => $activeTenants,
            'trial_tenants' => $trialTenants,
            'resolution_methods' => $this->resolutionMethods,
            'timestamp' => now()->toISOString(),
        ];
    }
}
