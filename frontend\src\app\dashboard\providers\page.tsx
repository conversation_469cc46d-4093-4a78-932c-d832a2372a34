'use client';

import { useState, useEffect } from 'react';
import {
    CpuChipIcon,
    PlusIcon,
    Cog6ToothIcon,
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    EyeIcon,
    EyeSlashIcon,
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

interface AIProvider {
    id: string;
    name: string;
    type: 'openai' | 'anthropic' | 'google' | 'azure' | 'custom';
    status: 'active' | 'inactive' | 'error';
    apiKey: string;
    endpoint?: string;
    models: string[];
    usage: {
        requestsToday: number;
        tokensUsed: number;
        cost: number;
    };
    lastUsed: string;
    createdAt: string;
}

export default function ProvidersPage() {
    const [providers, setProviders] = useState<AIProvider[]>([]);
    const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({});
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Load AI providers
        const loadProviders = async () => {
            try {
                // TODO: Replace with actual API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                setProviders([
                    {
                        id: '1',
                        name: 'OpenAI GPT-4',
                        type: 'openai',
                        status: 'active',
                        apiKey: 'sk-...abc123',
                        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
                        usage: {
                            requestsToday: 245,
                            tokensUsed: 125000,
                            cost: 12.50,
                        },
                        lastUsed: '2 minutes ago',
                        createdAt: '2024-01-15',
                    },
                    {
                        id: '2',
                        name: 'Anthropic Claude',
                        type: 'anthropic',
                        status: 'active',
                        apiKey: 'sk-ant-...xyz789',
                        models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
                        usage: {
                            requestsToday: 89,
                            tokensUsed: 67000,
                            cost: 8.90,
                        },
                        lastUsed: '15 minutes ago',
                        createdAt: '2024-01-10',
                    },
                    {
                        id: '3',
                        name: 'Google Gemini',
                        type: 'google',
                        status: 'inactive',
                        apiKey: 'AIza...def456',
                        models: ['gemini-pro', 'gemini-pro-vision'],
                        usage: {
                            requestsToday: 0,
                            tokensUsed: 0,
                            cost: 0,
                        },
                        lastUsed: '2 days ago',
                        createdAt: '2024-01-08',
                    },
                    {
                        id: '4',
                        name: 'Azure OpenAI',
                        type: 'azure',
                        status: 'error',
                        apiKey: 'azure-...ghi789',
                        endpoint: 'https://myresource.openai.azure.com/',
                        models: ['gpt-4', 'gpt-35-turbo'],
                        usage: {
                            requestsToday: 12,
                            tokensUsed: 5000,
                            cost: 0.75,
                        },
                        lastUsed: '1 hour ago',
                        createdAt: '2024-01-05',
                    },
                ]);
            } catch (error) {
                console.error('Error loading providers:', error);
            } finally {
                setLoading(false);
            }
        };

        loadProviders();
    }, []);

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active':
                return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
            case 'inactive':
                return <XCircleIcon className="h-5 w-5 text-gray-400" />;
            case 'error':
                return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
            default:
                return <XCircleIcon className="h-5 w-5 text-gray-400" />;
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'active':
                return 'Active';
            case 'inactive':
                return 'Inactive';
            case 'error':
                return 'Error';
            default:
                return 'Unknown';
        }
    };

    const getProviderLogo = (type: string) => {
        // In a real app, you'd return actual logo components or images
        return <CpuChipIcon className="h-8 w-8 text-gray-600" />;
    };

    const toggleApiKeyVisibility = (providerId: string) => {
        setShowApiKeys(prev => ({
            ...prev,
            [providerId]: !prev[providerId]
        }));
    };

    const maskApiKey = (apiKey: string) => {
        if (apiKey.length <= 8) return '***';
        return apiKey.substring(0, 6) + '...' + apiKey.substring(apiKey.length - 4);
    };

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="space-y-4">
                        {[1, 2, 3].map(i => (
                            <div key={i} className="h-32 bg-gray-200 rounded"></div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">AI Providers</h1>
                    <p className="mt-1 text-sm text-gray-500">
                        Manage your AI provider configurations and API keys.
                    </p>
                </div>
                <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Provider
                </button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Active Providers</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {providers.filter(p => p.status === 'active').length}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <CpuChipIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Requests Today</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {providers.reduce((sum, p) => sum + p.usage.requestsToday, 0).toLocaleString()}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span className="text-purple-600 font-semibold text-sm">T</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Tokens Used</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        {(providers.reduce((sum, p) => sum + p.usage.tokensUsed, 0) / 1000).toFixed(0)}K
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <span className="text-green-600 font-semibold text-sm">$</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Cost Today</dt>
                                    <dd className="text-2xl font-semibold text-gray-900">
                                        ${providers.reduce((sum, p) => sum + p.usage.cost, 0).toFixed(2)}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Providers List */}
            <div className="space-y-4">
                {providers.map((provider) => (
                    <Card key={provider.id}>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                    <div className="flex-shrink-0">
                                        {getProviderLogo(provider.type)}
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-900">{provider.name}</h3>
                                        <div className="flex items-center mt-1">
                                            {getStatusIcon(provider.status)}
                                            <span className="ml-2 text-sm text-gray-500">
                                                {getStatusText(provider.status)}
                                            </span>
                                            <span className="mx-2 text-gray-300">•</span>
                                            <span className="text-sm text-gray-500">
                                                Last used {provider.lastUsed}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <Cog6ToothIcon className="h-4 w-4 mr-1" />
                                        Configure
                                    </button>
                                </div>
                            </div>

                            <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">API Key</dt>
                                    <dd className="mt-1 flex items-center">
                                        <span className="text-sm text-gray-900 font-mono">
                                            {showApiKeys[provider.id] ? provider.apiKey : maskApiKey(provider.apiKey)}
                                        </span>
                                        <button
                                            onClick={() => toggleApiKeyVisibility(provider.id)}
                                            className="ml-2 text-gray-400 hover:text-gray-600"
                                        >
                                            {showApiKeys[provider.id] ? (
                                                <EyeSlashIcon className="h-4 w-4" />
                                            ) : (
                                                <EyeIcon className="h-4 w-4" />
                                            )}
                                        </button>
                                    </dd>
                                </div>

                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Models</dt>
                                    <dd className="mt-1 text-sm text-gray-900">
                                        {provider.models.slice(0, 2).join(', ')}
                                        {provider.models.length > 2 && ` +${provider.models.length - 2} more`}
                                    </dd>
                                </div>

                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Requests Today</dt>
                                    <dd className="mt-1 text-sm text-gray-900">
                                        {provider.usage.requestsToday.toLocaleString()}
                                    </dd>
                                </div>

                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Cost Today</dt>
                                    <dd className="mt-1 text-sm text-gray-900">
                                        ${provider.usage.cost.toFixed(2)}
                                    </dd>
                                </div>
                            </div>

                            {provider.endpoint && (
                                <div className="mt-4">
                                    <dt className="text-sm font-medium text-gray-500">Endpoint</dt>
                                    <dd className="mt-1 text-sm text-gray-900 font-mono">{provider.endpoint}</dd>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
