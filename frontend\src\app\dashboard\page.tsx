'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
    CpuChipIcon,
    ChatBubbleLeftRightIcon,
    DocumentTextIcon,
    ChartBarIcon,
    ClockIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    ArrowUpIcon
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

interface DashboardStats {
    totalChats: number;
    activeProviders: number;
    documentsProcessed: number;
    workflowsRunning: number;
    responseTime: number;
    successRate: number;
    errorRate: number;
    tokensUsed: number;
}

interface RecentActivity {
    id: string;
    type: 'chat' | 'workflow' | 'document' | 'provider';
    title: string;
    description: string;
    timestamp: string;
    status: 'success' | 'error' | 'pending';
}

export default function DashboardPage() {
    const router = useRouter();
    const [stats, setStats] = useState<DashboardStats>({
        totalChats: 0,
        activeProviders: 0,
        documentsProcessed: 0,
        workflowsRunning: 0,
        responseTime: 0,
        successRate: 0,
        errorRate: 0,
        tokensUsed: 0,
    });

    const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Simulate loading dashboard data
        const loadDashboardData = async () => {
            try {
                // TODO: Replace with actual API calls
                await new Promise(resolve => setTimeout(resolve, 1000));

                setStats({
                    totalChats: 1247,
                    activeProviders: 8,
                    documentsProcessed: 342,
                    workflowsRunning: 12,
                    responseTime: 1.2,
                    successRate: 98.5,
                    errorRate: 1.5,
                    tokensUsed: 2847392,
                });

                setRecentActivity([
                    {
                        id: '1',
                        type: 'chat',
                        title: 'New chat session started',
                        description: 'User initiated conversation with GPT-4',
                        timestamp: '2 minutes ago',
                        status: 'success',
                    },
                    {
                        id: '2',
                        type: 'workflow',
                        title: 'Document analysis workflow completed',
                        description: 'Processed 15 documents with Claude-3',
                        timestamp: '5 minutes ago',
                        status: 'success',
                    },
                    {
                        id: '3',
                        type: 'provider',
                        title: 'OpenAI API rate limit warning',
                        description: 'Approaching rate limit for GPT-4 model',
                        timestamp: '12 minutes ago',
                        status: 'error',
                    },
                    {
                        id: '4',
                        type: 'document',
                        title: 'Document uploaded',
                        description: 'New PDF document added to knowledge base',
                        timestamp: '18 minutes ago',
                        status: 'success',
                    },
                    {
                        id: '5',
                        type: 'workflow',
                        title: 'Workflow execution started',
                        description: 'Multi-step analysis workflow initiated',
                        timestamp: '25 minutes ago',
                        status: 'pending',
                    },
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            } finally {
                setLoading(false);
            }
        };

        loadDashboardData();
    }, []);

    const getActivityIcon = (type: string) => {
        switch (type) {
            case 'chat':
                return ChatBubbleLeftRightIcon;
            case 'workflow':
                return ChartBarIcon;
            case 'document':
                return DocumentTextIcon;
            case 'provider':
                return CpuChipIcon;
            default:
                return ClockIcon;
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'success':
                return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
            case 'error':
                return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
            case 'pending':
                return <ClockIcon className="h-5 w-5 text-yellow-500" />;
            default:
                return <ClockIcon className="h-5 w-5 text-gray-500" />;
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="mt-1 text-sm text-gray-500">
                    Welcome back! Here&apos;s what&apos;s happening with your AI orchestration platform.
                </p>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                {/* Total Chats */}
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ChatBubbleLeftRightIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Chats</dt>
                                    <dd className="flex items-baseline">
                                        <div className="text-2xl font-semibold text-gray-900">
                                            {stats.totalChats.toLocaleString()}
                                        </div>
                                        <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                                            <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4 text-green-500" />
                                            <span className="sr-only">Increased by</span>
                                            12%
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Active Providers */}
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <CpuChipIcon className="h-8 w-8 text-green-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Active Providers</dt>
                                    <dd className="flex items-baseline">
                                        <div className="text-2xl font-semibold text-gray-900">{stats.activeProviders}</div>
                                        <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                                            <CheckCircleIcon className="self-center flex-shrink-0 h-4 w-4 text-green-500" />
                                            <span className="sr-only">All operational</span>
                                            Online
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Documents Processed */}
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <DocumentTextIcon className="h-8 w-8 text-purple-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Documents Processed</dt>
                                    <dd className="flex items-baseline">
                                        <div className="text-2xl font-semibold text-gray-900">
                                            {stats.documentsProcessed.toLocaleString()}
                                        </div>
                                        <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                                            <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4 text-green-500" />
                                            <span className="sr-only">Increased by</span>
                                            8%
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Workflows Running */}
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ChartBarIcon className="h-8 w-8 text-orange-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Active Workflows</dt>
                                    <dd className="flex items-baseline">
                                        <div className="text-2xl font-semibold text-gray-900">{stats.workflowsRunning}</div>
                                        <div className="ml-2 flex items-baseline text-sm font-semibold text-yellow-600">
                                            <ClockIcon className="self-center flex-shrink-0 h-4 w-4 text-yellow-500" />
                                            <span className="sr-only">Running</span>
                                            Running
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Performance Metrics */}
            <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
                {/* Performance Stats */}
                <Card>
                    <CardHeader>
                        <CardTitle>Performance Metrics</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-500">Average Response Time</span>
                                <span className="text-sm font-semibold text-gray-900">{stats.responseTime}s</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-500">Success Rate</span>
                                <span className="text-sm font-semibold text-green-600">{stats.successRate}%</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-500">Error Rate</span>
                                <span className="text-sm font-semibold text-red-600">{stats.errorRate}%</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-500">Tokens Used (Today)</span>
                                <span className="text-sm font-semibold text-gray-900">
                                    {stats.tokensUsed.toLocaleString()}
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Recent Activity */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Activity</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flow-root">
                            <ul className="-mb-8">
                                {recentActivity.map((activity, activityIdx) => {
                                    const Icon = getActivityIcon(activity.type);
                                    return (
                                        <li key={activity.id}>
                                            <div className="relative pb-8">
                                                {activityIdx !== recentActivity.length - 1 ? (
                                                    <span
                                                        className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                                                        aria-hidden="true"
                                                    />
                                                ) : null}
                                                <div className="relative flex space-x-3">
                                                    <div>
                                                        <span className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center ring-8 ring-white">
                                                            <Icon className="h-4 w-4 text-gray-500" />
                                                        </span>
                                                    </div>
                                                    <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                        <div>
                                                            <p className="text-sm text-gray-900">{activity.title}</p>
                                                            <p className="text-sm text-gray-500">{activity.description}</p>
                                                        </div>
                                                        <div className="text-right text-sm whitespace-nowrap text-gray-500 flex items-center space-x-2">
                                                            {getStatusIcon(activity.status)}
                                                            <span>{activity.timestamp}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    );
                                })}
                            </ul>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                        <button
                            onClick={() => router.push('/dashboard/chat')}
                            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
                        >
                            <div>
                                <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                                    <ChatBubbleLeftRightIcon className="h-6 w-6" />
                                </span>
                            </div>
                            <div className="mt-8">
                                <h3 className="text-lg font-medium">
                                    <span className="absolute inset-0" aria-hidden="true" />
                                    Start New Chat
                                </h3>
                                <p className="mt-2 text-sm text-gray-500">
                                    Begin a new conversation with AI providers
                                </p>
                            </div>
                        </button>

                        <button
                            onClick={() => router.push('/dashboard/providers')}
                            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
                        >
                            <div>
                                <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                                    <CpuChipIcon className="h-6 w-6" />
                                </span>
                            </div>
                            <div className="mt-8">
                                <h3 className="text-lg font-medium">
                                    <span className="absolute inset-0" aria-hidden="true" />
                                    Configure Providers
                                </h3>
                                <p className="mt-2 text-sm text-gray-500">
                                    Manage AI provider settings and API keys
                                </p>
                            </div>
                        </button>

                        <button
                            onClick={() => router.push('/dashboard/documents')}
                            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
                        >
                            <div>
                                <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                                    <DocumentTextIcon className="h-6 w-6" />
                                </span>
                            </div>
                            <div className="mt-8">
                                <h3 className="text-lg font-medium">
                                    <span className="absolute inset-0" aria-hidden="true" />
                                    Upload Documents
                                </h3>
                                <p className="mt-2 text-sm text-gray-500">
                                    Add documents to your knowledge base
                                </p>
                            </div>
                        </button>

                        <button
                            onClick={() => router.push('/dashboard/workflows')}
                            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
                        >
                            <div>
                                <span className="rounded-lg inline-flex p-3 bg-orange-50 text-orange-700 ring-4 ring-white">
                                    <ChartBarIcon className="h-6 w-6" />
                                </span>
                            </div>
                            <div className="mt-8">
                                <h3 className="text-lg font-medium">
                                    <span className="absolute inset-0" aria-hidden="true" />
                                    Create Workflow
                                </h3>
                                <p className="mt-2 text-sm text-gray-500">
                                    Build automated AI workflows
                                </p>
                            </div>
                        </button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
} 