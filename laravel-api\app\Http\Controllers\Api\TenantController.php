<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Core\TenantContextService;
use App\Services\Core\LoggingService;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class TenantController extends Controller
{
    protected TenantContextService $tenantContext;
    protected LoggingService $loggingService;

    public function __construct(
        TenantContextService $tenantContext,
        LoggingService $loggingService
    ) {
        $this->tenantContext = $tenantContext;
        $this->loggingService = $loggingService;
    }

    /**
     * Get current tenant information
     */
    public function show(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            $tenantData = [
                'id' => $tenant->id,
                'name' => $tenant->name,
                'domain' => $tenant->domain,
                'status' => $tenant->status,
                'subscription_plan' => $tenant->subscription_plan,
                'subscription_status' => $tenant->subscription_status,
                'settings' => $tenant->settings,
                'created_at' => $tenant->created_at,
                'updated_at' => $tenant->updated_at,
                'user_count' => $tenant->users()->count(),
                'is_admin' => $user->hasRole('admin'),
            ];

            $this->loggingService->logHttpRequest('tenant_info_retrieved', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
            ]);

            return response()->json([
                'success' => true,
                'data' => $tenantData,
                'message' => 'Tenant information retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tenant_info_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenant information',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update tenant settings (admin only)
     */
    public function updateSettings(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:255',
                'settings' => 'nullable|array',
                'settings.ai_providers' => 'nullable|array',
                'settings.websocket_config' => 'nullable|array',
                'settings.mcp_config' => 'nullable|array',
                'settings.branding' => 'nullable|array',
                'settings.features' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Check admin permission
            if (!$user->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient permissions'
                ], 403);
            }

            $updateData = [];

            if ($request->filled('name')) {
                $updateData['name'] = $request->input('name');
            }

            if ($request->filled('settings')) {
                $currentSettings = $tenant->settings ?? [];
                $newSettings = array_merge($currentSettings, $request->input('settings'));
                $updateData['settings'] = $newSettings;
            }

            $tenant->update($updateData);

            $this->loggingService->logHttpRequest('tenant_settings_updated', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'updated_fields' => array_keys($updateData),
            ]);

            return response()->json([
                'success' => true,
                'data' => $tenant->fresh(),
                'message' => 'Tenant settings updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tenant_settings_update_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to update tenant settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tenant users (admin only)
     */
    public function getUsers(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Check admin permission
            if (!$user->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient permissions'
                ], 403);
            }

            $users = User::where('tenant_id', $tenant->id)
                ->with(['roles', 'permissions'])
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            $this->loggingService->logHttpRequest('tenant_users_retrieved', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'users_count' => $users->count(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $users,
                'message' => 'Tenant users retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tenant_users_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenant users',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new user (admin only)
     */
    public function createUser(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255',
                'password' => 'required|string|min:8',
                'role' => 'nullable|string|in:admin,user,viewer',
                'permissions' => 'nullable|array',
                'permissions.*' => 'string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $currentUser = $request->user();

            // Check admin permission
            if (!$currentUser->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient permissions'
                ], 403);
            }

            // Check if email already exists in this tenant
            $existingUser = User::where('tenant_id', $tenant->id)
                ->where('email', $request->input('email'))
                ->first();

            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'User with this email already exists'
                ], 422);
            }

            DB::beginTransaction();

            // Create user
            $newUser = User::create([
                'id' => Str::uuid(),
                'tenant_id' => $tenant->id,
                'name' => $request->input('name'),
                'email' => $request->input('email'),
                'password' => Hash::make($request->input('password')),
                'email_verified_at' => now(),
            ]);

            // Assign role
            $role = $request->input('role', 'user');
            $newUser->assignRole($role);

            // Assign permissions if provided
            if ($request->filled('permissions')) {
                $newUser->givePermissionTo($request->input('permissions'));
            }

            DB::commit();

            $this->loggingService->logHttpRequest('tenant_user_created', [
                'tenant_id' => $tenant->id,
                'admin_user_id' => $currentUser->id,
                'new_user_id' => $newUser->id,
                'new_user_email' => $newUser->email,
                'role' => $role,
            ]);

            return response()->json([
                'success' => true,
                'data' => $newUser->load(['roles', 'permissions']),
                'message' => 'User created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            $this->loggingService->logHttpRequest('tenant_user_creation_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to create user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user (admin only)
     */
    public function updateUser(Request $request, string $userId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:255',
                'email' => 'nullable|string|email|max:255',
                'password' => 'nullable|string|min:8',
                'role' => 'nullable|string|in:admin,user,viewer',
                'permissions' => 'nullable|array',
                'permissions.*' => 'string',
                'status' => 'nullable|string|in:active,inactive,suspended',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenant = $this->tenantContext->getCurrentTenant();
            $currentUser = $request->user();

            // Check admin permission
            if (!$currentUser->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient permissions'
                ], 403);
            }

            $targetUser = User::where('id', $userId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$targetUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            DB::beginTransaction();

            $updateData = [];

            if ($request->filled('name')) {
                $updateData['name'] = $request->input('name');
            }

            if ($request->filled('email')) {
                // Check if email already exists for another user
                $existingUser = User::where('tenant_id', $tenant->id)
                    ->where('email', $request->input('email'))
                    ->where('id', '!=', $userId)
                    ->first();

                if ($existingUser) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Email already exists for another user'
                    ], 422);
                }

                $updateData['email'] = $request->input('email');
            }

            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->input('password'));
            }

            if ($request->filled('status')) {
                $updateData['status'] = $request->input('status');
            }

            if (!empty($updateData)) {
                $targetUser->update($updateData);
            }

            // Update role if provided
            if ($request->filled('role')) {
                $targetUser->syncRoles([$request->input('role')]);
            }

            // Update permissions if provided
            if ($request->filled('permissions')) {
                $targetUser->syncPermissions($request->input('permissions'));
            }

            DB::commit();

            $this->loggingService->logHttpRequest('tenant_user_updated', [
                'tenant_id' => $tenant->id,
                'admin_user_id' => $currentUser->id,
                'target_user_id' => $targetUser->id,
                'updated_fields' => array_keys($updateData),
            ]);

            return response()->json([
                'success' => true,
                'data' => $targetUser->fresh()->load(['roles', 'permissions']),
                'message' => 'User updated successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            $this->loggingService->logHttpRequest('tenant_user_update_error', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to update user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete user (admin only)
     */
    public function deleteUser(Request $request, string $userId): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $currentUser = $request->user();

            // Check admin permission
            if (!$currentUser->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient permissions'
                ], 403);
            }

            $targetUser = User::where('id', $userId)
                ->where('tenant_id', $tenant->id)
                ->first();

            if (!$targetUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }

            // Prevent self-deletion
            if ($targetUser->id === $currentUser->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete your own account'
                ], 422);
            }

            // Soft delete the user
            $targetUser->delete();

            $this->loggingService->logHttpRequest('tenant_user_deleted', [
                'tenant_id' => $tenant->id,
                'admin_user_id' => $currentUser->id,
                'deleted_user_id' => $targetUser->id,
                'deleted_user_email' => $targetUser->email,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tenant_user_deletion_error', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tenant statistics (admin only)
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $tenant = $this->tenantContext->getCurrentTenant();
            $user = $request->user();

            // Check admin permission
            if (!$user->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient permissions'
                ], 403);
            }

            $stats = [
                'users' => [
                    'total' => User::where('tenant_id', $tenant->id)->count(),
                    'active' => User::where('tenant_id', $tenant->id)->where('status', 'active')->count(),
                    'admins' => User::where('tenant_id', $tenant->id)->whereHas('roles', function ($query) {
                        $query->where('name', 'admin');
                    })->count(),
                ],
                'activity' => [
                    'chat_sessions_today' => DB::table('websocket_sessions')
                        ->where('tenant_id', $tenant->id)
                        ->where('session_type', 'chat')
                        ->whereDate('created_at', today())
                        ->count(),
                    'messages_today' => DB::table('chat_histories')
                        ->where('tenant_id', $tenant->id)
                        ->whereDate('created_at', today())
                        ->count(),
                    'active_sessions' => DB::table('websocket_sessions')
                        ->where('tenant_id', $tenant->id)
                        ->where('status', 'active')
                        ->count(),
                ],
                'storage' => [
                    'documents_count' => DB::table('documents')->where('tenant_id', $tenant->id)->count(),
                    'embeddings_count' => DB::table('embeddings')->where('tenant_id', $tenant->id)->count(),
                ],
                'subscription' => [
                    'plan' => $tenant->subscription_plan,
                    'status' => $tenant->subscription_status,
                    'expires_at' => $tenant->subscription_expires_at,
                ],
            ];

            $this->loggingService->logHttpRequest('tenant_statistics_retrieved', [
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
            ]);

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Tenant statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logHttpRequest('tenant_statistics_error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 'error');

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenant statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
