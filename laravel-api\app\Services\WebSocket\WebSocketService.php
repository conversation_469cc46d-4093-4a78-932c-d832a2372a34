<?php

namespace App\Services\WebSocket;

use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;
use App\Models\WebsocketSession;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

class WebSocketService extends BaseService
{
    protected ChannelManager $channelManager;
    protected SessionManager $sessionManager;

    /**
     * Active connections tracking
     */
    protected array $activeConnections = [];

    /**
     * Connection statistics
     */
    protected array $connectionStats = [
        'total_connections' => 0,
        'active_connections' => 0,
        'connections_by_tenant' => [],
        'connections_by_user' => [],
    ];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService,
        ChannelManager $channelManager,
        SessionManager $sessionManager
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);

        $this->channelManager = $channelManager;
        $this->sessionManager = $sessionManager;
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'websocket_service';
    }

    /**
     * Initialize WebSocket connection
     */
    public function initializeConnection(array $connectionData): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('initialize_connection', function () use ($connectionData) {
            // Validate connection data
            $this->validateConnectionData($connectionData);

            // Check connection limits
            $this->checkConnectionLimits($connectionData);

            // Create connection session
            $connectionId = $this->generateConnectionId();
            $session = $this->sessionManager->createSession($connectionId, $connectionData);

            // Register connection
            $this->registerConnection($connectionId, $session);

            // Set up tenant-specific channels
            $channels = $this->channelManager->setupTenantChannels($session['tenant_id'], $session['user_id']);

            $this->logActivity('WebSocket connection initialized', [
                'connection_id' => $connectionId,
                'tenant_id' => $session['tenant_id'],
                'user_id' => $session['user_id'],
                'channels_count' => count($channels),
            ]);

            return [
                'connection_id' => $connectionId,
                'session_id' => $session['session_id'],
                'channels' => $channels,
                'heartbeat_interval' => config('broadcasting.websocket.heartbeat_interval'),
                'connection_timeout' => config('broadcasting.websocket.connection_timeout'),
                'status' => 'connected',
            ];
        }, [
            'tenant_id' => $connectionData['tenant_id'] ?? null,
            'user_id' => $connectionData['user_id'] ?? null,
        ]);
    }

    /**
     * Handle WebSocket disconnection
     */
    public function handleDisconnection(string $connectionId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('handle_disconnection', function () use ($connectionId) {
            $connection = $this->getConnection($connectionId);

            if (!$connection) {
                return ['status' => 'not_found'];
            }

            // Update session status
            $this->sessionManager->endSession($connection['session_id']);

            // Clean up channels
            $this->channelManager->cleanupUserChannels($connection['tenant_id'], $connection['user_id']);

            // Unregister connection
            $this->unregisterConnection($connectionId);

            $this->logActivity('WebSocket connection closed', [
                'connection_id' => $connectionId,
                'tenant_id' => $connection['tenant_id'],
                'user_id' => $connection['user_id'],
                'duration_seconds' => $connection['duration_seconds'] ?? 0,
            ]);

            return [
                'connection_id' => $connectionId,
                'status' => 'disconnected',
                'cleanup_completed' => true,
            ];
        }, [
            'connection_id' => $connectionId,
        ]);
    }

    /**
     * Broadcast message to channels
     */
    public function broadcastMessage(array $messageData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('broadcast_message', function () use ($messageData) {
            // Validate message data
            $this->validateMessageData($messageData);

            // Check rate limits
            $this->checkBroadcastRateLimit($messageData);

            // Prepare broadcast data
            $broadcastData = $this->prepareBroadcastData($messageData);

            // Get target channels
            $channels = $this->resolveTargetChannels($messageData);

            // Broadcast to channels
            $results = [];
            foreach ($channels as $channel) {
                try {
                    Broadcast::channel($channel)->send($broadcastData);
                    $results[$channel] = 'sent';
                } catch (\Exception $e) {
                    $results[$channel] = 'failed';
                    $this->logActivity('Broadcast failed', [
                        'channel' => $channel,
                        'error' => $e->getMessage(),
                    ], 'error');
                }
            }

            $this->logActivity('Message broadcasted', [
                'message_type' => $messageData['type'] ?? 'unknown',
                'channels_count' => count($channels),
                'successful_broadcasts' => count(array_filter($results, fn($r) => $r === 'sent')),
                'failed_broadcasts' => count(array_filter($results, fn($r) => $r === 'failed')),
            ]);

            return [
                'message_id' => $broadcastData['message_id'],
                'channels_targeted' => count($channels),
                'broadcast_results' => $results,
                'timestamp' => $broadcastData['timestamp'],
            ];
        }, [
            'message_type' => $messageData['type'] ?? 'unknown',
            'channels_count' => count($this->resolveTargetChannels($messageData)),
        ]);
    }

    /**
     * Send direct message to user
     */
    public function sendDirectMessage(string $userId, array $messageData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('send_direct_message', function () use ($userId, $messageData) {
            $tenantId = $this->getCurrentTenantId();

            // Get user's active channels
            $userChannels = $this->channelManager->getUserChannels($tenantId, $userId);

            if (empty($userChannels)) {
                return [
                    'status' => 'user_not_connected',
                    'user_id' => $userId,
                ];
            }

            // Prepare message
            $messageData['recipient_user_id'] = $userId;
            $messageData['channels'] = $userChannels;

            // Broadcast message
            $result = $this->broadcastMessage($messageData);

            return [
                'user_id' => $userId,
                'message_delivered' => true,
                'channels_used' => $userChannels,
                'message_id' => $result['message_id'],
            ];
        }, [
            'target_user_id' => $userId,
            'message_type' => $messageData['type'] ?? 'direct',
        ]);
    }

    /**
     * Get connection statistics
     */
    public function getConnectionStatistics(): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_connection_statistics', function () {
            $tenantId = $this->getCurrentTenantId();

            // Get real-time statistics
            $stats = [
                'tenant_id' => $tenantId,
                'total_active_connections' => count($this->activeConnections),
                'tenant_connections' => $this->connectionStats['connections_by_tenant'][$tenantId] ?? 0,
                'connections_by_user' => $this->connectionStats['connections_by_user'],
                'channel_statistics' => $this->channelManager->getChannelStatistics($tenantId),
                'session_statistics' => $this->sessionManager->getSessionStatistics($tenantId),
                'performance_metrics' => $this->getPerformanceMetrics(),
                'timestamp' => now()->toISOString(),
            ];

            return $stats;
        });
    }

    /**
     * Get active connections for tenant
     */
    public function getActiveConnections(?string $tenantId = null): array
    {
        $this->requireEnabled();
        $tenantId = $tenantId ?? $this->getCurrentTenantId();

        return $this->executeWithTracking('get_active_connections', function () use ($tenantId) {
            $tenantConnections = array_filter($this->activeConnections, function ($connection) use ($tenantId) {
                return $connection['tenant_id'] === $tenantId;
            });

            return [
                'tenant_id' => $tenantId,
                'active_connections' => array_values($tenantConnections),
                'connection_count' => count($tenantConnections),
                'users_online' => count(array_unique(array_column($tenantConnections, 'user_id'))),
            ];
        }, [
            'tenant_id' => $tenantId,
        ]);
    }

    /**
     * Handle heartbeat from connection
     */
    public function handleHeartbeat(string $connectionId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('handle_heartbeat', function () use ($connectionId) {
            $connection = $this->getConnection($connectionId);

            if (!$connection) {
                return ['status' => 'connection_not_found'];
            }

            // Update last activity
            $this->updateConnectionActivity($connectionId);

            // Update session heartbeat
            $this->sessionManager->updateHeartbeat($connection['session_id']);

            return [
                'connection_id' => $connectionId,
                'status' => 'heartbeat_received',
                'last_activity' => now()->toISOString(),
            ];
        }, [
            'connection_id' => $connectionId,
        ]);
    }

    /**
     * Clean up inactive connections
     */
    public function cleanupInactiveConnections(): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('cleanup_inactive_connections', function () {
            $timeout = config('broadcasting.websocket.connection_timeout', 60);
            $cutoffTime = now()->subSeconds($timeout);

            $cleanedConnections = [];

            foreach ($this->activeConnections as $connectionId => $connection) {
                if ($connection['last_activity'] < $cutoffTime) {
                    $this->handleDisconnection($connectionId);
                    $cleanedConnections[] = $connectionId;
                }
            }

            $this->logActivity('Inactive connections cleaned up', [
                'cleaned_connections' => count($cleanedConnections),
                'timeout_seconds' => $timeout,
            ]);

            return [
                'cleaned_connections' => count($cleanedConnections),
                'connection_ids' => $cleanedConnections,
                'timeout_seconds' => $timeout,
            ];
        });
    }

    /**
     * Validate connection data
     */
    protected function validateConnectionData(array $connectionData): void
    {
        $requiredFields = ['tenant_id', 'user_id', 'token'];

        foreach ($requiredFields as $field) {
            if (!isset($connectionData[$field])) {
                throw new \InvalidArgumentException("Missing required connection field: {$field}");
            }
        }

        // Validate token (this would integrate with Sanctum)
        if (!$this->validateConnectionToken($connectionData['token'], $connectionData['user_id'])) {
            throw new \UnauthorizedException('Invalid connection token');
        }
    }

    /**
     * Validate connection token
     */
    protected function validateConnectionToken(string $token, string $userId): bool
    {
        // This would integrate with Laravel Sanctum token validation
        // For now, return true as placeholder
        return true;
    }

    /**
     * Check connection limits
     */
    protected function checkConnectionLimits(array $connectionData): void
    {
        $tenantId = $connectionData['tenant_id'];
        $userId = $connectionData['user_id'];

        // Check tenant connection limit
        $tenantLimit = config('broadcasting.websocket.max_connections_per_tenant', 1000);
        $tenantConnections = $this->connectionStats['connections_by_tenant'][$tenantId] ?? 0;

        if ($tenantConnections >= $tenantLimit) {
            throw new \Exception("Tenant connection limit exceeded: {$tenantLimit}");
        }

        // Check user connection limit
        $userLimit = config('broadcasting.websocket.max_connections_per_user', 10);
        $userConnections = $this->connectionStats['connections_by_user'][$userId] ?? 0;

        if ($userConnections >= $userLimit) {
            throw new \Exception("User connection limit exceeded: {$userLimit}");
        }
    }

    /**
     * Register connection
     */
    protected function registerConnection(string $connectionId, array $session): void
    {
        $connection = [
            'connection_id' => $connectionId,
            'session_id' => $session['session_id'],
            'tenant_id' => $session['tenant_id'],
            'user_id' => $session['user_id'],
            'connected_at' => now(),
            'last_activity' => now(),
            'metadata' => $session['metadata'] ?? [],
        ];

        $this->activeConnections[$connectionId] = $connection;

        // Update statistics
        $this->connectionStats['total_connections']++;
        $this->connectionStats['active_connections']++;
        $this->connectionStats['connections_by_tenant'][$session['tenant_id']] =
            ($this->connectionStats['connections_by_tenant'][$session['tenant_id']] ?? 0) + 1;
        $this->connectionStats['connections_by_user'][$session['user_id']] =
            ($this->connectionStats['connections_by_user'][$session['user_id']] ?? 0) + 1;
    }

    /**
     * Unregister connection
     */
    protected function unregisterConnection(string $connectionId): void
    {
        $connection = $this->activeConnections[$connectionId] ?? null;

        if ($connection) {
            unset($this->activeConnections[$connectionId]);

            // Update statistics
            $this->connectionStats['active_connections']--;
            $this->connectionStats['connections_by_tenant'][$connection['tenant_id']]--;
            $this->connectionStats['connections_by_user'][$connection['user_id']]--;

            // Clean up zero counts
            if ($this->connectionStats['connections_by_tenant'][$connection['tenant_id']] <= 0) {
                unset($this->connectionStats['connections_by_tenant'][$connection['tenant_id']]);
            }
            if ($this->connectionStats['connections_by_user'][$connection['user_id']] <= 0) {
                unset($this->connectionStats['connections_by_user'][$connection['user_id']]);
            }
        }
    }

    /**
     * Get connection by ID
     */
    protected function getConnection(string $connectionId): ?array
    {
        return $this->activeConnections[$connectionId] ?? null;
    }

    /**
     * Update connection activity
     */
    protected function updateConnectionActivity(string $connectionId): void
    {
        if (isset($this->activeConnections[$connectionId])) {
            $this->activeConnections[$connectionId]['last_activity'] = now();
        }
    }

    /**
     * Validate message data
     */
    protected function validateMessageData(array $messageData): void
    {
        $requiredFields = ['type', 'data'];

        foreach ($requiredFields as $field) {
            if (!isset($messageData[$field])) {
                throw new \InvalidArgumentException("Missing required message field: {$field}");
            }
        }
    }

    /**
     * Check broadcast rate limit
     */
    protected function checkBroadcastRateLimit(array $messageData): void
    {
        if (!config('broadcasting.rate_limiting.enabled', true)) {
            return;
        }

        $tenantId = $this->getCurrentTenantId();
        $userId = $this->getCurrentUserId();

        $cacheKey = "websocket_rate_limit:{$tenantId}:{$userId}";
        $limit = config('broadcasting.rate_limiting.messages_per_minute', 1000);

        $currentCount = $this->getFromCache($cacheKey, 0);

        if ($currentCount >= $limit) {
            throw new \Exception("WebSocket message rate limit exceeded: {$limit} per minute");
        }

        $this->putToCache($cacheKey, $currentCount + 1, 60);
    }

    /**
     * Prepare broadcast data
     */
    protected function prepareBroadcastData(array $messageData): array
    {
        return [
            'message_id' => Str::uuid()->toString(),
            'type' => $messageData['type'],
            'data' => $messageData['data'],
            'sender' => [
                'tenant_id' => $this->getCurrentTenantId(),
                'user_id' => $this->getCurrentUserId(),
            ],
            'timestamp' => now()->toISOString(),
            'metadata' => $messageData['metadata'] ?? [],
        ];
    }

    /**
     * Resolve target channels
     */
    protected function resolveTargetChannels(array $messageData): array
    {
        if (isset($messageData['channels'])) {
            return $messageData['channels'];
        }

        $tenantId = $this->getCurrentTenantId();
        $channels = [];

        // Default to tenant broadcast channel
        $channels[] = $this->channelManager->getTenantBroadcastChannel($tenantId);

        // Add specific channels based on message type
        switch ($messageData['type']) {
            case 'chat_message':
                $sessionId = $messageData['session_id'] ?? null;
                if ($sessionId) {
                    $channels[] = $this->channelManager->getChatChannel($tenantId, $sessionId);
                }
                break;

            case 'notification':
                $channels[] = $this->channelManager->getNotificationChannel($tenantId);
                break;

            case 'system_alert':
                $channels[] = $this->channelManager->getSystemChannel($tenantId);
                break;
        }

        return array_unique($channels);
    }

    /**
     * Get performance metrics
     */
    protected function getPerformanceMetrics(): array
    {
        return [
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'active_connections' => count($this->activeConnections),
            'redis_connections' => $this->getRedisConnectionCount(),
            'uptime_seconds' => $this->getUptimeSeconds(),
        ];
    }

    /**
     * Get Redis connection count
     */
    protected function getRedisConnectionCount(): int
    {
        try {
            $info = Redis::info();
            return $info['connected_clients'] ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get uptime in seconds
     */
    protected function getUptimeSeconds(): int
    {
        // This would be tracked from service start time
        // For now, return a placeholder
        return 0;
    }

    /**
     * Generate connection ID
     */
    protected function generateConnectionId(): string
    {
        return 'WS_' . strtoupper(Str::random(12)) . '_' . time();
    }
}
