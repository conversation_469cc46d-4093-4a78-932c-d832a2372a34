<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\TenantController;
use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\AIProviderController;
use App\Http\Controllers\Api\ToolController;
use App\Http\Controllers\Api\WorkflowController;
use App\Http\Controllers\Api\DocumentController;
use App\Http\Controllers\Api\WebSocketController;
use App\Http\Controllers\Api\MonitoringController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// CSRF token
Route::get('/sanctum/csrf-cookie', function () {
    return response()->json(['csrf_token' => csrf_token()]);
});

// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
    Route::post('verify-email', [AuthController::class, 'verifyEmail']);
});

// Tenant-aware authenticated routes
Route::middleware(['auth:sanctum', 'tenant.context'])->group(function () {

    // Authentication management
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::get('user', [AuthController::class, 'user']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
        Route::post('enable-2fa', [AuthController::class, 'enable2FA']);
        Route::post('disable-2fa', [AuthController::class, 'disable2FA']);
    });

    // Tenant management (admin only)
    Route::middleware(['role:admin'])->prefix('tenant')->group(function () {
        Route::get('/', [TenantController::class, 'show']);
        Route::put('/settings', [TenantController::class, 'updateSettings']);
        Route::get('/users', [TenantController::class, 'getUsers']);
        Route::post('/users', [TenantController::class, 'createUser']);
        Route::put('/users/{userId}', [TenantController::class, 'updateUser']);
        Route::delete('/users/{userId}', [TenantController::class, 'deleteUser']);
        Route::get('/statistics', [TenantController::class, 'getStatistics']);
    });

    // Chat API
    Route::prefix('chat')->group(function () {
        Route::get('/sessions', [ChatController::class, 'getSessions']);
        Route::post('/sessions', [ChatController::class, 'createSession']);
        Route::get('/sessions/{sessionId}', [ChatController::class, 'getSession']);
        Route::post('/sessions/{sessionId}/messages', [ChatController::class, 'sendMessage']);
        Route::get('/sessions/{sessionId}/messages', [ChatController::class, 'getMessages']);
        Route::delete('/sessions/{sessionId}', [ChatController::class, 'deleteSession']);
        Route::post('/sessions/{sessionId}/clear', [ChatController::class, 'clearSession']);
        Route::get('/history', [ChatController::class, 'getChatHistory']);
    });

    // AI Provider Management
    Route::prefix('ai-providers')->group(function () {
        Route::get('/', [AIProviderController::class, 'index']);
        Route::post('/', [AIProviderController::class, 'store'])->middleware(['role:admin']);
        Route::get('/available', [AIProviderController::class, 'getAvailableProviders']);
        Route::get('/{providerId}', [AIProviderController::class, 'show']);
        Route::put('/{providerId}', [AIProviderController::class, 'update'])->middleware(['role:admin']);
        Route::delete('/{providerId}', [AIProviderController::class, 'destroy'])->middleware(['role:admin']);
        Route::post('/{providerId}/test', [AIProviderController::class, 'testConnection']);
        Route::get('/{providerName}/models', [AIProviderController::class, 'getProviderModels']);
        Route::post('/generate', [AIProviderController::class, 'generateText']);
    });

    // Tool Management
    Route::prefix('tools')->group(function () {
        Route::get('/', [ToolController::class, 'index']);
        Route::post('/', [ToolController::class, 'store']);
        Route::get('/types', [ToolController::class, 'getAvailableTypes']);
        Route::get('/{toolId}', [ToolController::class, 'show']);
        Route::put('/{toolId}', [ToolController::class, 'update']);
        Route::delete('/{toolId}', [ToolController::class, 'destroy']);
        Route::post('/{toolId}/execute', [ToolController::class, 'execute']);
        Route::post('/{toolId}/test', [ToolController::class, 'test']);
        Route::get('/{toolId}/history', [ToolController::class, 'getExecutionHistory']);
    });

    // Workflow Management
    Route::prefix('workflows')->group(function () {
        Route::get('/', [WorkflowController::class, 'index']);
        Route::post('/', [WorkflowController::class, 'store']);
        Route::get('/templates', [WorkflowController::class, 'getTemplates']);
        Route::get('/{workflowId}', [WorkflowController::class, 'show']);
        Route::put('/{workflowId}', [WorkflowController::class, 'update']);
        Route::delete('/{workflowId}', [WorkflowController::class, 'destroy']);
        Route::post('/{workflowId}/execute', [WorkflowController::class, 'execute']);
        Route::post('/{workflowId}/test', [WorkflowController::class, 'test']);
        Route::get('/{workflowId}/history', [WorkflowController::class, 'getExecutionHistory']);
    });

    // Document Management
    Route::prefix('documents')->group(function () {
        Route::get('/', [DocumentController::class, 'index']);
        Route::post('/', [DocumentController::class, 'store']);
        Route::post('/search', [DocumentController::class, 'search']);
        Route::get('/statistics', [DocumentController::class, 'getStatistics']);
        Route::get('/{documentId}', [DocumentController::class, 'show']);
        Route::put('/{documentId}', [DocumentController::class, 'update']);
        Route::delete('/{documentId}', [DocumentController::class, 'destroy']);
        Route::post('/{documentId}/process', [DocumentController::class, 'process']);
        Route::get('/{documentId}/download', [DocumentController::class, 'download']);
    });

    // WebSocket Management
    Route::prefix('websocket')->group(function () {
        Route::post('/authenticate', [WebSocketController::class, 'authenticate']);
        Route::get('/sessions', [WebSocketController::class, 'getSessions']);
        Route::post('/sessions', [WebSocketController::class, 'createSession']);
        Route::get('/sessions/{sessionId}', [WebSocketController::class, 'getSession']);
        Route::delete('/sessions/{sessionId}', [WebSocketController::class, 'disconnectSession']);
        Route::post('/broadcast', [WebSocketController::class, 'broadcast'])->middleware(['role:admin']);
        Route::get('/statistics', [WebSocketController::class, 'getStatistics']);
    });

    // Monitoring & Analytics
    Route::prefix('monitoring')->group(function () {
        Route::get('/health', [MonitoringController::class, 'health']);
        Route::get('/logs', [MonitoringController::class, 'getLogs']);
        Route::get('/metrics', [MonitoringController::class, 'getMetrics']);
        Route::get('/status', [MonitoringController::class, 'getRealtimeStatus']);
        Route::post('/logs/clear', [MonitoringController::class, 'clearLogs'])->middleware(['role:admin']);
    });
});

// Public health check
Route::get('health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'version' => config('app.version', '1.0.0')
    ]);
});
