'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  ChevronRightIcon,
  SparklesIcon,
  CpuChipIcon,
  CloudIcon,
  ShieldCheckIcon,
  BoltIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { config } from '@/lib/config';

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="relative z-10">
        <nav className="mx-auto max-w-7xl px-6 lg:px-8" aria-label="Global">
          <div className="flex h-16 items-center justify-between">
            <div className="flex lg:flex-1">
              <Link href="/" className="-m-1.5 p-1.5">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  {config.app.name}
                </span>
              </Link>
            </div>
            <div className="flex lg:hidden">
              <button
                type="button"
                className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
              >
                <span className="sr-only">Open main menu</span>
              </button>
            </div>
            <div className="hidden lg:flex lg:gap-x-12">
              <Link href="/features" className="text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600">
                Features
              </Link>
              <Link href="/providers" className="text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600">
                AI Providers
              </Link>
              <Link href="/docs" className="text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600">
                Documentation
              </Link>
              <Link href="/pricing" className="text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600">
                Pricing
              </Link>
            </div>
            <div className="hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4">
              <Link
                href="/login"
                className="text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600"
              >
                Log in
              </Link>
              <Link
                href="/register"
                className="rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
              >
                Get Started
              </Link>
            </div>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main>
        <div className="relative isolate px-6 pt-14 lg:px-8">
          <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80">
            <div className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-blue-400 to-indigo-400 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]" />
          </div>

          <div className="mx-auto max-w-4xl py-32 sm:py-48 lg:py-56">
            <div className="text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Universal AI{' '}
                <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  Orchestration
                </span>{' '}
                Platform
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto">
                Connect, orchestrate, and scale AI providers seamlessly. Build intelligent workflows
                with real-time collaboration, comprehensive monitoring, and enterprise-grade security.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Link
                  href="/register"
                  className="rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors"
                >
                  Start Free Trial
                </Link>
                <Link
                  href="/demo"
                  className="text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 flex items-center gap-1 transition-colors"
                >
                  Watch Demo <ChevronRightIcon className="h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className="mx-auto max-w-7xl px-6 lg:px-8 pb-24">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Everything you need to orchestrate AI
              </h2>
              <p className="mt-4 text-lg leading-8 text-gray-600">
                Powerful features designed for modern AI applications
              </p>
            </div>

            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <SparklesIcon className="h-5 w-5 flex-none text-blue-600" />
                    10+ AI Providers
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Connect to OpenAI, Anthropic, Google Gemini, Groq, Mistral, and more.
                      Switch providers seamlessly with unified APIs.
                    </p>
                  </dd>
                </div>

                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <ChatBubbleLeftRightIcon className="h-5 w-5 flex-none text-blue-600" />
                    Real-time Collaboration
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      WebSocket-powered real-time chat, typing indicators, and presence management
                      for seamless team collaboration.
                    </p>
                  </dd>
                </div>

                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <CpuChipIcon className="h-5 w-5 flex-none text-blue-600" />
                    MCP Agent System
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Intelligent agent orchestration with intent classification, RAG retrieval,
                      and workflow automation.
                    </p>
                  </dd>
                </div>

                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <CloudIcon className="h-5 w-5 flex-none text-blue-600" />
                    Enterprise Scale
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Multi-tenant architecture with comprehensive monitoring, logging,
                      and performance analytics.
                    </p>
                  </dd>
                </div>

                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <ShieldCheckIcon className="h-5 w-5 flex-none text-blue-600" />
                    Security First
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      End-to-end encryption, secure API key management, and comprehensive
                      audit logging for enterprise compliance.
                    </p>
                  </dd>
                </div>

                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <BoltIcon className="h-5 w-5 flex-none text-blue-600" />
                    High Performance
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Optimized for speed with intelligent caching, load balancing,
                      and automatic failover mechanisms.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </div>

          {/* CTA Section */}
          <div className="mx-auto max-w-7xl px-6 lg:px-8 pb-24">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Ready to get started?
              </h2>
              <p className="mt-4 text-lg leading-8 text-gray-600">
                Join thousands of developers building the future of AI applications
              </p>
              <div className="mt-8 flex items-center justify-center gap-x-6">
                <Link
                  href="/register"
                  className="rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors"
                >
                  Start Building Today
                </Link>
                <Link
                  href="/contact"
                  className="text-base font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors"
                >
                  Contact Sales
                </Link>
              </div>
            </div>
          </div>

          <div className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
            <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-400 to-indigo-400 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]" />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900">
        <div className="mx-auto max-w-7xl px-6 py-12 lg:px-8">
          <div className="xl:grid xl:grid-cols-3 xl:gap-8">
            <div className="space-y-8">
              <span className="text-2xl font-bold text-white">
                {config.app.name}
              </span>
              <p className="text-sm leading-6 text-gray-300">
                The Universal AI Orchestration Platform for modern applications.
              </p>
            </div>
            <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold leading-6 text-white">Product</h3>
                  <ul role="list" className="mt-6 space-y-4">
                    <li>
                      <Link href="/features" className="text-sm leading-6 text-gray-300 hover:text-white">
                        Features
                      </Link>
                    </li>
                    <li>
                      <Link href="/providers" className="text-sm leading-6 text-gray-300 hover:text-white">
                        AI Providers
                      </Link>
                    </li>
                    <li>
                      <Link href="/pricing" className="text-sm leading-6 text-gray-300 hover:text-white">
                        Pricing
                      </Link>
                    </li>
                  </ul>
                </div>
                <div className="mt-10 md:mt-0">
                  <h3 className="text-sm font-semibold leading-6 text-white">Support</h3>
                  <ul role="list" className="mt-6 space-y-4">
                    <li>
                      <Link href="/docs" className="text-sm leading-6 text-gray-300 hover:text-white">
                        Documentation
                      </Link>
                    </li>
                    <li>
                      <Link href="/contact" className="text-sm leading-6 text-gray-300 hover:text-white">
                        Contact
                      </Link>
                    </li>
                    <li>
                      <Link href="/status" className="text-sm leading-6 text-gray-300 hover:text-white">
                        Status
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-16 border-t border-gray-900/10 pt-8 sm:mt-20 lg:mt-24">
            <p className="text-xs leading-5 text-gray-400">
              &copy; 2024 {config.app.name}. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
