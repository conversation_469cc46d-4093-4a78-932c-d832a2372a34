<?php

namespace App\Services\WebSocket;

use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;
use App\Models\WebsocketSession;
use Illuminate\Support\Str;

class SessionManager extends BaseService
{
    /**
     * Active sessions tracking
     */
    protected array $activeSessions = [];

    /**
     * Session metadata cache
     */
    protected array $sessionMetadata = [];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'session_manager';
    }

    /**
     * Create a new WebSocket session
     */
    public function createSession(string $connectionId, array $connectionData): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('create_session', function () use ($connectionId, $connectionData) {
            return $this->executeInTransaction(function () use ($connectionId, $connectionData) {
                $sessionId = $this->generateSessionId();

                // Create database record
                $session = WebsocketSession::create([
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId,
                    'tenant_id' => $connectionData['tenant_id'],
                    'user_id' => $connectionData['user_id'],
                    'ip_address' => $connectionData['ip_address'] ?? null,
                    'user_agent' => $connectionData['user_agent'] ?? null,
                    'connection_metadata' => [
                        'token' => $connectionData['token'],
                        'client_info' => $connectionData['client_info'] ?? [],
                        'connection_time' => now()->toISOString(),
                    ],
                    'status' => 'active',
                    'last_activity_at' => now(),
                ]);

                // Store in memory for quick access
                $sessionData = [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId,
                    'tenant_id' => $connectionData['tenant_id'],
                    'user_id' => $connectionData['user_id'],
                    'status' => 'active',
                    'created_at' => now(),
                    'last_activity' => now(),
                    'metadata' => $session->connection_metadata,
                    'performance_metrics' => [
                        'messages_sent' => 0,
                        'messages_received' => 0,
                        'bytes_sent' => 0,
                        'bytes_received' => 0,
                        'errors_count' => 0,
                    ],
                ];

                $this->activeSessions[$sessionId] = $sessionData;

                $this->logActivity('WebSocket session created', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId,
                    'tenant_id' => $connectionData['tenant_id'],
                    'user_id' => $connectionData['user_id'],
                ]);

                return $sessionData;
            });
        }, [
            'connection_id' => $connectionId,
            'tenant_id' => $connectionData['tenant_id'],
            'user_id' => $connectionData['user_id'],
        ]);
    }

    /**
     * End a WebSocket session
     */
    public function endSession(string $sessionId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('end_session', function () use ($sessionId) {
            return $this->executeInTransaction(function () use ($sessionId) {
                $session = $this->getSession($sessionId);

                if (!$session) {
                    return ['status' => 'session_not_found'];
                }

                // Calculate session duration
                $duration = now()->diffInSeconds($session['created_at']);

                // Update database record
                WebsocketSession::where('session_id', $sessionId)->update([
                    'status' => 'ended',
                    'ended_at' => now(),
                    'duration_seconds' => $duration,
                    'performance_metrics' => $session['performance_metrics'],
                ]);

                // Remove from active sessions
                unset($this->activeSessions[$sessionId]);

                $this->logActivity('WebSocket session ended', [
                    'session_id' => $sessionId,
                    'tenant_id' => $session['tenant_id'],
                    'user_id' => $session['user_id'],
                    'duration_seconds' => $duration,
                    'messages_sent' => $session['performance_metrics']['messages_sent'],
                    'messages_received' => $session['performance_metrics']['messages_received'],
                ]);

                return [
                    'session_id' => $sessionId,
                    'status' => 'ended',
                    'duration_seconds' => $duration,
                    'performance_metrics' => $session['performance_metrics'],
                ];
            });
        }, [
            'session_id' => $sessionId,
        ]);
    }

    /**
     * Update session heartbeat
     */
    public function updateHeartbeat(string $sessionId): bool
    {
        $this->requireEnabled();

        return $this->executeWithTracking('update_heartbeat', function () use ($sessionId) {
            $session = $this->getSession($sessionId);

            if (!$session) {
                return false;
            }

            // Update in-memory session
            $this->activeSessions[$sessionId]['last_activity'] = now();

            // Update database (throttled to avoid too many writes)
            $lastDbUpdate = $this->sessionMetadata[$sessionId]['last_db_update'] ?? null;
            $updateThreshold = now()->subSeconds(30); // Update DB every 30 seconds

            if (!$lastDbUpdate || $lastDbUpdate < $updateThreshold) {
                WebsocketSession::where('session_id', $sessionId)->update([
                    'last_activity_at' => now(),
                ]);

                $this->sessionMetadata[$sessionId]['last_db_update'] = now();
            }

            return true;
        }, [
            'session_id' => $sessionId,
        ]);
    }

    /**
     * Update session performance metrics
     */
    public function updateSessionMetrics(string $sessionId, array $metrics): bool
    {
        $this->requireEnabled();

        return $this->executeWithTracking('update_session_metrics', function () use ($sessionId, $metrics) {
            $session = $this->getSession($sessionId);

            if (!$session) {
                return false;
            }

            // Update performance metrics
            foreach ($metrics as $metric => $value) {
                if (isset($session['performance_metrics'][$metric])) {
                    $this->activeSessions[$sessionId]['performance_metrics'][$metric] += $value;
                }
            }

            return true;
        }, [
            'session_id' => $sessionId,
            'metrics_count' => count($metrics),
        ]);
    }

    /**
     * Get session information
     */
    public function getSession(string $sessionId): ?array
    {
        return $this->activeSessions[$sessionId] ?? null;
    }

    /**
     * Get sessions for tenant
     */
    public function getTenantSessions(string $tenantId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_tenant_sessions', function () use ($tenantId) {
            $tenantSessions = array_filter($this->activeSessions, function ($session) use ($tenantId) {
                return $session['tenant_id'] === $tenantId;
            });

            return [
                'tenant_id' => $tenantId,
                'active_sessions' => array_values($tenantSessions),
                'session_count' => count($tenantSessions),
                'unique_users' => count(array_unique(array_column($tenantSessions, 'user_id'))),
            ];
        }, [
            'tenant_id' => $tenantId,
        ]);
    }

    /**
     * Get sessions for user
     */
    public function getUserSessions(string $tenantId, string $userId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_user_sessions', function () use ($tenantId, $userId) {
            $userSessions = array_filter($this->activeSessions, function ($session) use ($tenantId, $userId) {
                return $session['tenant_id'] === $tenantId && $session['user_id'] === $userId;
            });

            return [
                'tenant_id' => $tenantId,
                'user_id' => $userId,
                'active_sessions' => array_values($userSessions),
                'session_count' => count($userSessions),
            ];
        }, [
            'tenant_id' => $tenantId,
            'user_id' => $userId,
        ]);
    }

    /**
     * Get session statistics
     */
    public function getSessionStatistics(string $tenantId): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_session_statistics', function () use ($tenantId) {
            $tenantSessions = array_filter($this->activeSessions, function ($session) use ($tenantId) {
                return $session['tenant_id'] === $tenantId;
            });

            $stats = [
                'tenant_id' => $tenantId,
                'active_sessions' => count($tenantSessions),
                'unique_users' => count(array_unique(array_column($tenantSessions, 'user_id'))),
                'total_messages_sent' => 0,
                'total_messages_received' => 0,
                'total_bytes_sent' => 0,
                'total_bytes_received' => 0,
                'total_errors' => 0,
                'average_session_duration' => 0,
                'sessions_by_status' => ['active' => count($tenantSessions)],
            ];

            $totalDuration = 0;

            foreach ($tenantSessions as $session) {
                $metrics = $session['performance_metrics'];
                $stats['total_messages_sent'] += $metrics['messages_sent'];
                $stats['total_messages_received'] += $metrics['messages_received'];
                $stats['total_bytes_sent'] += $metrics['bytes_sent'];
                $stats['total_bytes_received'] += $metrics['bytes_received'];
                $stats['total_errors'] += $metrics['errors_count'];

                $duration = now()->diffInSeconds($session['created_at']);
                $totalDuration += $duration;
            }

            if (count($tenantSessions) > 0) {
                $stats['average_session_duration'] = round($totalDuration / count($tenantSessions), 2);
            }

            return $stats;
        }, [
            'tenant_id' => $tenantId,
        ]);
    }

    /**
     * Clean up inactive sessions
     */
    public function cleanupInactiveSessions(): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('cleanup_inactive_sessions', function () {
            $timeout = config('broadcasting.websocket.connection_timeout', 60);
            $cutoffTime = now()->subSeconds($timeout);

            $cleanedSessions = [];

            foreach ($this->activeSessions as $sessionId => $session) {
                if ($session['last_activity'] < $cutoffTime) {
                    $this->endSession($sessionId);
                    $cleanedSessions[] = $sessionId;
                }
            }

            $this->logActivity('Inactive sessions cleaned up', [
                'cleaned_sessions' => count($cleanedSessions),
                'timeout_seconds' => $timeout,
            ]);

            return [
                'cleaned_sessions' => count($cleanedSessions),
                'session_ids' => $cleanedSessions,
                'timeout_seconds' => $timeout,
            ];
        });
    }

    /**
     * Get session history for tenant
     */
    public function getSessionHistory(string $tenantId, array $options = []): array
    {
        $this->requireEnabled();

        return $this->executeWithTracking('get_session_history', function () use ($tenantId, $options) {
            $dateFrom = $options['date_from'] ?? now()->subDays(7);
            $dateTo = $options['date_to'] ?? now();
            $limit = $options['limit'] ?? 100;

            $query = WebsocketSession::where('tenant_id', $tenantId)
                                   ->whereBetween('created_at', [$dateFrom, $dateTo])
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit);

            if (isset($options['user_id'])) {
                $query->where('user_id', $options['user_id']);
            }

            if (isset($options['status'])) {
                $query->where('status', $options['status']);
            }

            $sessions = $query->get();

            // Calculate statistics
            $totalSessions = $sessions->count();
            $activeSessions = $sessions->where('status', 'active')->count();
            $endedSessions = $sessions->where('status', 'ended')->count();

            $averageDuration = $sessions->where('status', 'ended')
                                      ->avg('duration_seconds') ?? 0;

            return [
                'tenant_id' => $tenantId,
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo,
                ],
                'statistics' => [
                    'total_sessions' => $totalSessions,
                    'active_sessions' => $activeSessions,
                    'ended_sessions' => $endedSessions,
                    'average_duration_seconds' => round($averageDuration, 2),
                ],
                'sessions' => $sessions->toArray(),
            ];
        }, [
            'tenant_id' => $tenantId,
            'date_range_days' => $options['date_from'] ? now()->diffInDays($options['date_from']) : 7,
            'limit' => $options['limit'] ?? 100,
        ]);
    }

    /**
     * Update session status
     */
    public function updateSessionStatus(string $sessionId, string $status): bool
    {
        $this->requireEnabled();

        return $this->executeWithTracking('update_session_status', function () use ($sessionId, $status) {
            $session = $this->getSession($sessionId);

            if (!$session) {
                return false;
            }

            // Update in-memory session
            $this->activeSessions[$sessionId]['status'] = $status;

            // Update database
            WebsocketSession::where('session_id', $sessionId)->update([
                'status' => $status,
                'last_activity_at' => now(),
            ]);

            $this->logActivity('Session status updated', [
                'session_id' => $sessionId,
                'new_status' => $status,
                'tenant_id' => $session['tenant_id'],
                'user_id' => $session['user_id'],
            ]);

            return true;
        }, [
            'session_id' => $sessionId,
            'new_status' => $status,
        ]);
    }

    /**
     * Record session error
     */
    public function recordSessionError(string $sessionId, array $errorData): bool
    {
        $this->requireEnabled();

        return $this->executeWithTracking('record_session_error', function () use ($sessionId, $errorData) {
            $session = $this->getSession($sessionId);

            if (!$session) {
                return false;
            }

            // Increment error count
            $this->activeSessions[$sessionId]['performance_metrics']['errors_count']++;

            // Log the error
            $this->logActivity('WebSocket session error', [
                'session_id' => $sessionId,
                'tenant_id' => $session['tenant_id'],
                'user_id' => $session['user_id'],
                'error_type' => $errorData['type'] ?? 'unknown',
                'error_message' => $errorData['message'] ?? 'No message',
                'error_code' => $errorData['code'] ?? null,
            ], 'error');

            return true;
        }, [
            'session_id' => $sessionId,
            'error_type' => $errorData['type'] ?? 'unknown',
        ]);
    }

    /**
     * Get session by connection ID
     */
    public function getSessionByConnectionId(string $connectionId): ?array
    {
        foreach ($this->activeSessions as $session) {
            if ($session['connection_id'] === $connectionId) {
                return $session;
            }
        }

        return null;
    }

    /**
     * Bulk update session metrics
     */
    public function bulkUpdateMetrics(array $sessionMetrics): int
    {
        $this->requireEnabled();

        return $this->executeWithTracking('bulk_update_metrics', function () use ($sessionMetrics) {
            $updatedSessions = 0;

            foreach ($sessionMetrics as $sessionId => $metrics) {
                if ($this->updateSessionMetrics($sessionId, $metrics)) {
                    $updatedSessions++;
                }
            }

            return $updatedSessions;
        }, [
            'sessions_count' => count($sessionMetrics),
        ]);
    }

    /**
     * Generate session ID
     */
    protected function generateSessionId(): string
    {
        return 'WS_SESSION_' . strtoupper(Str::random(16)) . '_' . time();
    }

    /**
     * Persist session metrics to database
     */
    public function persistSessionMetrics(): int
    {
        $this->requireEnabled();

        return $this->executeWithTracking('persist_session_metrics', function () {
            $persistedSessions = 0;

            foreach ($this->activeSessions as $sessionId => $session) {
                try {
                    WebsocketSession::where('session_id', $sessionId)->update([
                        'performance_metrics' => $session['performance_metrics'],
                        'last_activity_at' => $session['last_activity'],
                    ]);

                    $persistedSessions++;
                } catch (\Exception $e) {
                    $this->logActivity('Failed to persist session metrics', [
                        'session_id' => $sessionId,
                        'error' => $e->getMessage(),
                    ], 'error');
                }
            }

            return $persistedSessions;
        });
    }

    /**
     * Load active sessions from database
     */
    public function loadActiveSessionsFromDatabase(): int
    {
        $this->requireEnabled();

        return $this->executeWithTracking('load_active_sessions_from_database', function () {
            $loadedSessions = 0;

            try {
                $activeSessions = WebsocketSession::where('status', 'active')
                                                ->where('last_activity_at', '>', now()->subHours(1))
                                                ->get();

                foreach ($activeSessions as $dbSession) {
                    $sessionData = [
                        'session_id' => $dbSession->session_id,
                        'connection_id' => $dbSession->connection_id,
                        'tenant_id' => $dbSession->tenant_id,
                        'user_id' => $dbSession->user_id,
                        'status' => $dbSession->status,
                        'created_at' => $dbSession->created_at,
                        'last_activity' => $dbSession->last_activity_at,
                        'metadata' => $dbSession->connection_metadata,
                        'performance_metrics' => $dbSession->performance_metrics ?? [
                            'messages_sent' => 0,
                            'messages_received' => 0,
                            'bytes_sent' => 0,
                            'bytes_received' => 0,
                            'errors_count' => 0,
                        ],
                    ];

                    $this->activeSessions[$dbSession->session_id] = $sessionData;
                    $loadedSessions++;
                }

                $this->logActivity('Active sessions loaded from database', [
                    'loaded_sessions' => $loadedSessions,
                ]);
            } catch (\Exception $e) {
                $this->logActivity('Failed to load active sessions from database', [
                    'error' => $e->getMessage(),
                ], 'error');
            }

            return $loadedSessions;
        });
    }
}
