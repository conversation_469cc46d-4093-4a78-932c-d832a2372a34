import React, { useState, useEffect, useCallback, useRef } from 'react';
import { cn } from '@/utils';

interface ValidationResult {
    isValid: boolean;
    message?: string;
}

interface ValidatedInputProps {
    label: string;
    type: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onValidation?: (result: ValidationResult) => void;
    validator?: (value: string) => ValidationResult;
    placeholder?: string;
    autoComplete?: string;
    required?: boolean;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    helperText?: string;
    className?: string;
    disabled?: boolean;
}

export default function ValidatedInput({
    label,
    type,
    value,
    onChange,
    onValidation,
    validator,
    placeholder,
    autoComplete,
    required = false,
    leftIcon,
    rightIcon,
    helperText,
    className,
    disabled = false,
}: ValidatedInputProps) {
    const [isFocused, setIsFocused] = useState(false);
    const [isTouched, setIsTouched] = useState(false);
    const [validationResult, setValidationResult] = useState<ValidationResult>({ isValid: true });

    // Use ref to store the latest onValidation callback
    const onValidationRef = useRef(onValidation);
    onValidationRef.current = onValidation;

    // Memoize the validation function to prevent unnecessary re-runs
    const runValidation = useCallback((inputValue: string) => {
        if (validator) {
            const result = validator(inputValue);
            setValidationResult(result);
            // Use ref to avoid dependency on onValidation
            onValidationRef.current?.(result);
            return result;
        }
        return { isValid: true };
    }, [validator]);

    // Real-time validation with stable dependencies
    useEffect(() => {
        if (isTouched && value !== undefined) {
            runValidation(value);
        }
    }, [value, isTouched, runValidation]);

    const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e);
        if (!isTouched) {
            setIsTouched(true);
        }
    }, [onChange, isTouched]);

    const handleBlur = useCallback(() => {
        setIsFocused(false);
        setIsTouched(true);
    }, []);

    const handleFocus = useCallback(() => {
        setIsFocused(true);
    }, []);

    const hasError = isTouched && !validationResult.isValid;
    const hasSuccess = isTouched && validationResult.isValid && value.length > 0;
    const showValidation = isTouched && value.length > 0;

    return (
        <div className={cn('space-y-2', className)}>
            {/* Label */}
            <label className="block text-sm font-medium text-gray-700">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
            </label>

            {/* Input Container */}
            <div className="relative">
                {/* Left Icon */}
                {leftIcon && (
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <div className={cn(
                            'h-5 w-5 transition-colors duration-200',
                            hasError ? 'text-red-400' :
                                hasSuccess ? 'text-green-400' :
                                    isFocused ? 'text-blue-400' : 'text-gray-400'
                        )}>
                            {leftIcon}
                        </div>
                    </div>
                )}

                {/* Input Field */}
                <input
                    type={type}
                    value={value}
                    onChange={handleChange}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    placeholder={placeholder}
                    autoComplete={autoComplete}
                    required={required}
                    disabled={disabled}
                    className={cn(
                        'block w-full px-3 py-3 text-gray-900 placeholder-gray-500 border rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0',
                        leftIcon ? 'pl-10' : 'pl-3',
                        rightIcon ? 'pr-10' : 'pr-3',
                        hasError ? [
                            'border-red-300 bg-red-50 focus:border-red-500 focus:ring-red-500/20',
                            'animate-in slide-in-from-left-1 duration-200'
                        ] : hasSuccess ? [
                            'border-green-300 bg-green-50 focus:border-green-500 focus:ring-green-500/20'
                        ] : [
                            'border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500/20',
                            isFocused && 'transform scale-[1.01]'
                        ],
                        disabled && 'opacity-50 cursor-not-allowed',
                        'hover:border-gray-400 disabled:hover:border-gray-300'
                    )}
                />

                {/* Right Icon / Validation Indicator */}
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    {rightIcon ? (
                        <div className={cn(
                            'transition-colors duration-200',
                            hasError ? 'text-red-400' :
                                hasSuccess ? 'text-green-400' :
                                    isFocused ? 'text-blue-400' : 'text-gray-400'
                        )}>
                            {rightIcon}
                        </div>
                    ) : showValidation ? (
                        <div className="flex items-center">
                            {hasError ? (
                                <svg
                                    className="h-5 w-5 text-red-400 animate-in zoom-in-50 duration-200"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            ) : hasSuccess ? (
                                <svg
                                    className="h-5 w-5 text-green-400 animate-in zoom-in-50 duration-200"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            ) : null}
                        </div>
                    ) : null}
                </div>
            </div>

            {/* Helper Text / Error Message */}
            <div className="min-h-[1.25rem]">
                {hasError && validationResult.message ? (
                    <p className="text-sm text-red-600 flex items-center animate-in slide-in-from-top-1 duration-200">
                        <svg className="h-4 w-4 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {validationResult.message}
                    </p>
                ) : hasSuccess ? (
                    <p className="text-sm text-green-600 flex items-center animate-in slide-in-from-top-1 duration-200">
                        <svg className="h-4 w-4 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Looks good!
                    </p>
                ) : helperText ? (
                    <p className="text-sm text-gray-500">{helperText}</p>
                ) : null}
            </div>
        </div>
    );
} 