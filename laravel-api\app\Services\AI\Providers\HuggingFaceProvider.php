<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class HuggingFaceProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://api-inference.huggingface.co';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            // Test with a simple text generation model
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(10)->post($this->baseUrl . '/models/microsoft/DialoGPT-medium', [
                'inputs' => 'Hello',
                'parameters' => ['max_length' => 10]
            ]);

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'Hugging Face API connection successful',
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'Hugging Face API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Hugging Face API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        return $this->getDefaultModels();
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'microsoft/DialoGPT-medium';
        $maxLength = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.7;

        try {
            $payload = [
                'inputs' => $prompt,
                'parameters' => [
                    'max_length' => $maxLength,
                    'temperature' => $temperature,
                    'do_sample' => true,
                ]
            ];

            if (isset($options['stop'])) {
                $payload['parameters']['stop_sequences'] = $options['stop'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . "/models/{$model}", $payload);

            if ($response->successful()) {
                $data = $response->json();

                $content = '';
                if (is_array($data) && isset($data[0]['generated_text'])) {
                    $content = $data[0]['generated_text'];
                    // Remove the original prompt from the response
                    $content = str_replace($prompt, '', $content);
                }

                return [
                    'success' => true,
                    'content' => trim($content),
                    'model' => $model,
                    'usage' => $this->estimateUsage($prompt, $content),
                    'cost' => $this->calculateCost($this->estimateUsage($prompt, $content)),
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Hugging Face API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        // Convert messages to a single prompt for most HF models
        $prompt = $this->convertMessagesToPrompt($messages);
        return $this->generateCompletion($prompt, $options, $stream);
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        $model = $options['model'] ?? 'sentence-transformers/all-MiniLM-L6-v2';

        try {
            $embeddings = [];

            foreach ($texts as $text) {
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ])->timeout(60)->post($this->baseUrl . "/models/{$model}", [
                    'inputs' => $text
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    $embeddings[] = $data;
                } else {
                    throw new \Exception('Hugging Face embedding error: ' . $response->body());
                }
            }

            return [
                'success' => true,
                'embeddings' => $embeddings,
                'model' => $model,
                'usage' => ['total_tokens' => array_sum(array_map('str_word_count', $texts))],
                'cost' => $this->calculateCost(['total_tokens' => array_sum(array_map('str_word_count', $texts))]),
            ];

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function getCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'custom_models',
            'open_source',
            'classification',
            'translation',
            'summarization',
        ];
    }

    public function getName(): string
    {
        return 'huggingface';
    }

    public function getDisplayName(): string
    {
        return 'Hugging Face';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 100,
            'requests_per_hour' => 1000,
            'requests_per_day' => 10000,
        ];
    }

    public function calculateCost(array $usage): float
    {
        // Hugging Face Inference API is free for public models
        // Pro accounts have different pricing
        return 0.0;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('huggingface_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'huggingface',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'microsoft/DialoGPT-medium',
                'name' => 'DialoGPT Medium',
                'description' => 'Conversational AI model',
                'capabilities' => ['text_generation', 'chat'],
                'context_length' => 1024,
                'cost_per_token' => 0.0,
            ],
            [
                'id' => 'gpt2',
                'name' => 'GPT-2',
                'description' => 'OpenAI GPT-2 model',
                'capabilities' => ['text_generation'],
                'context_length' => 1024,
                'cost_per_token' => 0.0,
            ],
            [
                'id' => 'facebook/blenderbot-400M-distill',
                'name' => 'BlenderBot 400M',
                'description' => 'Facebook conversational AI',
                'capabilities' => ['chat'],
                'context_length' => 512,
                'cost_per_token' => 0.0,
            ],
            [
                'id' => 'sentence-transformers/all-MiniLM-L6-v2',
                'name' => 'All MiniLM L6 v2',
                'description' => 'Sentence embedding model',
                'capabilities' => ['embeddings'],
                'context_length' => 256,
                'cost_per_token' => 0.0,
            ],
            [
                'id' => 'facebook/bart-large-cnn',
                'name' => 'BART Large CNN',
                'description' => 'Summarization model',
                'capabilities' => ['summarization'],
                'context_length' => 1024,
                'cost_per_token' => 0.0,
            ],
        ];
    }

    protected function convertMessagesToPrompt(array $messages): string
    {
        $prompt = '';
        foreach ($messages as $message) {
            $role = ucfirst($message['role']);
            $prompt .= "{$role}: {$message['content']}\n";
        }
        $prompt .= "Assistant: ";
        return $prompt;
    }

    protected function estimateUsage(string $prompt, string $completion): array
    {
        $promptTokens = str_word_count($prompt) * 1.3; // Rough estimation
        $completionTokens = str_word_count($completion) * 1.3;

        return [
            'prompt_tokens' => (int) $promptTokens,
            'completion_tokens' => (int) $completionTokens,
            'total_tokens' => (int) ($promptTokens + $completionTokens),
        ];
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'text_generation',
            'chat',
            'embeddings',
            'custom_models',
            'open_source',
            'classification',
            'translation',
            'summarization',
        ];
    }
}
