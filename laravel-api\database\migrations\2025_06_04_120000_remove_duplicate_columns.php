<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove duplicate columns from users table
        Schema::table('users', function (Blueprint $table) {
            // Remove 'locale' column, keep 'language'
            $table->dropColumn('locale');
        });

        // Remove duplicate columns from tenants table
        Schema::table('tenants', function (Blueprint $table) {
            // Remove 'status' column, keep 'subscription_status' but expand it
            $table->dropColumn('status');

            // Update subscription_status to include all needed values
            $table->dropColumn('subscription_status');
        });

        // Re-add subscription_status with expanded enum values
        Schema::table('tenants', function (Blueprint $table) {
            $table->enum('subscription_status', [
                'trial',
                'active',
                'inactive',
                'suspended',
                'cancelled',
                'pending'
            ])->default('trial')->after('subscription_plan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back removed columns
        Schema::table('users', function (Blueprint $table) {
            $table->string('locale')->default('en')->after('language');
        });

        Schema::table('tenants', function (Blueprint $table) {
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])
                  ->default('active')
                  ->after('metadata');

            // Revert subscription_status to original values
            $table->dropColumn('subscription_status');
        });

        Schema::table('tenants', function (Blueprint $table) {
            $table->enum('subscription_status', ['trial', 'active', 'inactive', 'suspended', 'cancelled'])
                  ->default('trial')
                  ->after('subscription_plan');
        });
    }
};
