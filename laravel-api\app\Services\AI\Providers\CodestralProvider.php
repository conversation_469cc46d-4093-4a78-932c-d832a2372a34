<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Http;

class CodestralProvider implements AIProviderInterface
{
    protected LoggingService $loggingService;
    protected array $config = [];
    protected string $apiKey;
    protected string $baseUrl = 'https://codestral.mistral.ai/v1';

    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    public function initialize(array $config): bool
    {
        $this->config = $config;
        $this->apiKey = $config['api_key'] ?? '';

        if (!empty($config['api_endpoint'])) {
            $this->baseUrl = rtrim($config['api_endpoint'], '/');
        }

        return !empty($this->apiKey);
    }

    public function testConnection(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(10)->post($this->baseUrl . '/chat/completions', [
                'model' => 'codestral-latest',
                'messages' => [['role' => 'user', 'content' => 'print("hello")']],
                'max_tokens' => 10,
            ]);

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000 ?? 0,
                    'message' => 'Codestral API connection successful',
                ];
            }

            return [
                'status' => 'unhealthy',
                'message' => 'Codestral API returned error: ' . $response->status(),
                'error_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Codestral API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getAvailableModels(): array
    {
        return $this->getDefaultModels();
    }

    public function generateCompletion(string $prompt, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'codestral-latest';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.1; // Lower temperature for code

        try {
            $payload = [
                'model' => $model,
                'prompt' => $prompt,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            if (isset($options['stop'])) {
                $payload['stop'] = $options['stop'];
            }

            if (isset($options['suffix'])) {
                $payload['suffix'] = $options['suffix'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/fim/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['text'] ?? '',
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Codestral API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateChatCompletion(array $messages, array $options = [], bool $stream = false): array
    {
        $model = $options['model'] ?? 'codestral-latest';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.1; // Lower temperature for code

        try {
            $payload = [
                'model' => $model,
                'messages' => $messages,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
                'stream' => $stream,
            ];

            if (isset($options['stop'])) {
                $payload['stop'] = $options['stop'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['message']['content'] ?? '',
                    'message' => $data['choices'][0]['message'] ?? [],
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Codestral API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    public function generateEmbeddings(array $texts, array $options = []): array
    {
        // Codestral doesn't provide embeddings directly
        return [
            'success' => false,
            'error' => 'Codestral does not provide embeddings',
            'provider' => 'codestral',
        ];
    }

    public function getCapabilities(): array
    {
        return [
            'code_generation',
            'code_completion',
            'chat',
            'fill_in_middle',
            'programming_languages',
            'code_explanation',
            'debugging',
        ];
    }

    public function getName(): string
    {
        return 'codestral';
    }

    public function getDisplayName(): string
    {
        return 'Codestral';
    }

    public function getRateLimits(): array
    {
        return [
            'requests_per_minute' => 100,
            'tokens_per_minute' => 20000,
            'requests_per_day' => 1000,
        ];
    }

    public function calculateCost(array $usage): float
    {
        $promptTokens = $usage['prompt_tokens'] ?? 0;
        $completionTokens = $usage['completion_tokens'] ?? 0;

        // Codestral pricing (specialized for code)
        $promptCost = $promptTokens * 0.000001; // $1 per 1M tokens
        $completionCost = $completionTokens * 0.000003; // $3 per 1M tokens

        return $promptCost + $completionCost;
    }

    public function getHealthStatus(): array
    {
        return $this->testConnection();
    }

    public function handleError(\Exception $exception): array
    {
        $this->loggingService->logAIProvider('codestral_error', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ], 'error');

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'provider' => 'codestral',
        ];
    }

    protected function getDefaultModels(): array
    {
        return [
            [
                'id' => 'codestral-latest',
                'name' => 'Codestral Latest',
                'description' => 'Latest Codestral model for code generation',
                'capabilities' => ['code_generation', 'code_completion', 'fill_in_middle'],
                'context_length' => 32768,
                'cost_per_token' => 0.000001,
            ],
            [
                'id' => 'codestral-2405',
                'name' => 'Codestral 2405',
                'description' => 'Codestral model from May 2024',
                'capabilities' => ['code_generation', 'code_completion', 'fill_in_middle'],
                'context_length' => 32768,
                'cost_per_token' => 0.000001,
            ],
        ];
    }

    protected function getModelDescription(string $modelId): string
    {
        $descriptions = [
            'codestral-latest' => 'Latest Codestral model for code generation',
            'codestral-2405' => 'Codestral model from May 2024',
        ];

        return $descriptions[$modelId] ?? 'Codestral code generation model';
    }

    protected function getModelCapabilities(string $modelId): array
    {
        return ['code_generation', 'code_completion', 'fill_in_middle', 'programming_languages'];
    }

    protected function getModelContextLength(string $modelId): int
    {
        return 32768; // All Codestral models support 32K context
    }

    protected function getModelCostPerToken(string $modelId): float
    {
        return 0.000001; // Consistent pricing across models
    }

    /**
     * Generate code with fill-in-the-middle capability
     */
    public function generateCodeCompletion(string $prefix, string $suffix = '', array $options = []): array
    {
        $model = $options['model'] ?? 'codestral-latest';
        $maxTokens = $options['max_tokens'] ?? 1000;
        $temperature = $options['temperature'] ?? 0.1;

        try {
            $payload = [
                'model' => $model,
                'prompt' => $prefix,
                'suffix' => $suffix,
                'max_tokens' => $maxTokens,
                'temperature' => $temperature,
            ];

            if (isset($options['stop'])) {
                $payload['stop'] = $options['stop'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($this->baseUrl . '/fim/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['text'] ?? '',
                    'model' => $model,
                    'usage' => $data['usage'] ?? [],
                    'cost' => $this->calculateCost($data['usage'] ?? []),
                    'finish_reason' => $data['choices'][0]['finish_reason'] ?? null,
                    'raw_response' => $data,
                ];
            }

            throw new \Exception('Codestral FIM API error: ' . $response->body());

        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Detect programming language from code
     */
    public function detectLanguage(string $code): string
    {
        // Simple language detection based on common patterns
        if (str_contains($code, '<?php')) return 'php';
        if (str_contains($code, 'function') && str_contains($code, '{')) return 'javascript';
        if (str_contains($code, 'def ') && str_contains($code, ':')) return 'python';
        if (str_contains($code, 'public class')) return 'java';
        if (str_contains($code, '#include')) return 'cpp';
        if (str_contains($code, 'fn ') && str_contains($code, '->')) return 'rust';
        if (str_contains($code, 'func ')) return 'go';

        return 'unknown';
    }

    public static function getStaticCapabilities(): array
    {
        return [
            'code_generation',
            'code_completion',
            'chat',
            'fill_in_middle',
            'programming_languages',
            'code_explanation',
            'debugging',
        ];
    }
}
