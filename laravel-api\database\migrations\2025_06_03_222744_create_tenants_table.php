<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('domain')->nullable()->unique();
            $table->string('subdomain')->nullable()->unique();

            // Contact Information
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();

            // Status & Activity
            $table->boolean('is_active')->default(true);

            // Subscription & Billing
            $table->enum('subscription_plan', ['free', 'basic', 'pro', 'enterprise'])->default('free');
            $table->enum('subscription_status', ['trial', 'active', 'inactive', 'suspended', 'cancelled'])->default('trial');
            $table->timestamp('trial_ends_at')->nullable();
            $table->string('timezone')->default('UTC');
            $table->string('language')->default('en');
            $table->json('feature_flags')->nullable();
            $table->json('limits')->nullable();
            $table->json('settings')->nullable();
            $table->string('webhook_secret')->nullable();
            $table->timestamp('subscription_started_at')->nullable();
            $table->timestamp('subscription_expires_at')->nullable();
            $table->decimal('monthly_limit_tokens', 15, 0)->default(10000);
            $table->decimal('used_tokens_current_month', 15, 0)->default(0);

            // Feature Flags
            $table->json('enabled_features')->nullable(); // AI providers, tools, etc.
            $table->integer('max_users')->default(5);
            $table->integer('max_ai_providers')->default(3);
            $table->integer('max_documents')->default(100);
            $table->integer('max_workflows')->default(10);

            // Database Configuration
            $table->string('database_name')->nullable();
            $table->string('database_host')->nullable();
            $table->integer('database_port')->nullable();
            $table->text('database_config')->nullable(); // JSON config

            // Security & Compliance
            $table->json('security_settings')->nullable();
            $table->boolean('data_retention_enabled')->default(true);
            $table->integer('data_retention_days')->default(365);
            $table->boolean('audit_logging_enabled')->default(true);

            // API Configuration
            $table->string('api_key')->unique();
            $table->integer('api_rate_limit_per_minute')->default(60);
            $table->integer('websocket_connection_limit')->default(100);

            // Status & Metadata
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('active');
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['status', 'subscription_status']);
            $table->index(['subscription_plan', 'subscription_status']);
            $table->index('api_key');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};
