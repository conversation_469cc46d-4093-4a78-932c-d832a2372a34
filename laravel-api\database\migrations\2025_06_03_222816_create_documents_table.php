<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_id');
            $table->uuid('uploaded_by_user_id');

            // File Information
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('original_filename');
            $table->string('stored_filename');
            $table->string('file_path');
            $table->string('file_extension');
            $table->string('mime_type');
            $table->bigInteger('file_size'); // in bytes
            $table->string('file_hash'); // SHA-256 for deduplication

            // Processing Status
            $table->enum('processing_status', [
                'uploaded', 'processing', 'processed', 'failed', 'archived'
            ])->default('uploaded');
            $table->timestamp('processing_started_at')->nullable();
            $table->timestamp('processing_completed_at')->nullable();
            $table->decimal('processing_time_seconds', 10, 2)->nullable();
            $table->text('processing_error')->nullable();

            // Content Extraction
            $table->longText('extracted_text')->nullable();
            $table->integer('extracted_text_length')->nullable();
            $table->string('extraction_method')->nullable(); // OCR, parser, etc.
            $table->json('extraction_metadata')->nullable();
            $table->decimal('extraction_confidence', 5, 4)->nullable(); // 0-1

            // Embedding Information
            $table->integer('total_embeddings')->default(0);
            $table->integer('chunk_size')->nullable();
            $table->integer('chunk_overlap')->nullable();
            $table->enum('chunking_strategy', ['fixed', 'semantic', 'paragraph', 'sentence'])->nullable();
            $table->timestamp('embeddings_created_at')->nullable();

            // Document Analysis
            $table->string('language')->nullable();
            $table->json('detected_entities')->nullable(); // NER results
            $table->json('keywords')->nullable();
            $table->json('topics')->nullable();
            $table->enum('content_type', ['text', 'code', 'data', 'mixed'])->nullable();
            $table->integer('readability_score')->nullable(); // 1-100

            // Access Control
            $table->enum('visibility', ['private', 'team', 'public'])->default('private');
            $table->json('access_permissions')->nullable();
            $table->boolean('is_searchable')->default(true);
            $table->boolean('is_active')->default(true);

            // Usage Tracking
            $table->integer('view_count')->default(0);
            $table->integer('download_count')->default(0);
            $table->integer('embedding_usage_count')->default(0);
            $table->timestamp('last_accessed_at')->nullable();
            $table->uuid('last_accessed_by_user_id')->nullable();

            // Versioning
            $table->integer('version')->default(1);
            $table->uuid('parent_document_id')->nullable(); // For versions
            $table->boolean('is_latest_version')->default(true);
            $table->text('version_notes')->nullable();

            // External Sources
            $table->string('source_url')->nullable();
            $table->timestamp('source_last_modified')->nullable();
            $table->boolean('auto_sync_enabled')->default(false);
            $table->timestamp('last_sync_at')->nullable();
            $table->enum('sync_status', ['synced', 'outdated', 'failed'])->nullable();

            // Compliance & Security
            $table->boolean('contains_pii')->default(false);
            $table->boolean('contains_sensitive_data')->default(false);
            $table->json('compliance_tags')->nullable();
            $table->timestamp('retention_expires_at')->nullable();
            $table->boolean('is_encrypted')->default(false);

            // Categorization
            $table->string('category')->nullable();
            $table->json('tags')->nullable();
            $table->integer('importance_score')->nullable(); // 1-10
            $table->json('custom_fields')->nullable();

            // Status & Metadata
            $table->enum('status', ['active', 'archived', 'deleted'])->default('active');
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Foreign Keys
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('uploaded_by_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('last_accessed_by_user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parent_document_id')->references('id')->on('documents')->onDelete('set null');

            // Indexes
            $table->unique(['tenant_id', 'file_hash']); // Prevent duplicates
            $table->index(['tenant_id', 'processing_status']);
            $table->index(['tenant_id', 'is_active', 'is_searchable']);
            $table->index(['tenant_id', 'category']);
            $table->index(['file_extension', 'mime_type']);
            $table->index('last_accessed_at');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
