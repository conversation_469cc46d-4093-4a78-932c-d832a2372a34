<?php

namespace App\Services\MCP;

use App\Services\Core\BaseService;
use App\Services\Core\LoggingService;
use App\Services\Core\ErrorHandlingService;
use App\Services\Core\TenantContextService;
use App\Services\Core\ConfigurationService;

class FormatterService extends BaseService
{
    /**
     * Available formatting strategies
     */
    protected array $formattingStrategies = [
        'conversational' => 'formatConversational',
        'technical' => 'formatTechnical',
        'creative' => 'formatCreative',
        'analytical' => 'formatAnalytical',
        'instructional' => 'formatInstructional',
        'professional' => 'formatProfessional',
        'casual' => 'formatCasual',
        'academic' => 'formatAcademic',
    ];

    /**
     * Template engines supported
     */
    protected array $templateEngines = [
        'mustache' => 'processMustacheTemplate',
        'twig' => 'processTwigTemplate',
        'blade' => 'processBladeTemplate',
        'handlebars' => 'processHandlebarsTemplate',
        'simple' => 'processSimpleTemplate',
    ];

    /**
     * Prompt templates cache
     */
    protected array $templateCache = [];

    /**
     * Formatting context
     */
    protected array $formattingContext = [];

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService,
        TenantContextService $tenantContextService,
        ConfigurationService $configurationService
    ) {
        parent::__construct($loggingService, $errorHandlingService, $tenantContextService, $configurationService);
    }

    /**
     * Get service name
     */
    protected function getServiceName(): string
    {
        return 'formatter_service';
    }

    /**
     * Format prompt with context
     */
    public function formatPrompt(array $promptData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('format_prompt', function () use ($promptData) {
            // Extract formatting parameters
            $template = $promptData['template'] ?? '';
            $context = $promptData['context'] ?? [];
            $strategy = $promptData['strategy'] ?? 'conversational';
            $engine = $promptData['engine'] ?? 'simple';
            $options = $promptData['options'] ?? [];

            // Validate inputs
            $this->validateFormattingInputs($template, $strategy, $engine);

            // Prepare formatting context
            $formattingContext = $this->prepareFormattingContext($context, $strategy, $options);

            // Apply formatting strategy
            $formattedTemplate = $this->applyFormattingStrategy($template, $strategy, $formattingContext);

            // Process template with engine
            $finalPrompt = $this->processTemplate($formattedTemplate, $engine, $formattingContext);

            // Apply post-processing
            $processedPrompt = $this->applyPostProcessing($finalPrompt, $options);

            // Generate metadata
            $metadata = $this->generateFormattingMetadata($template, $strategy, $engine, $formattingContext);

            return [
                'formatted_prompt' => $processedPrompt,
                'original_template' => $template,
                'strategy_used' => $strategy,
                'engine_used' => $engine,
                'context_variables' => array_keys($formattingContext),
                'metadata' => $metadata,
                'formatting_stats' => [
                    'original_length' => strlen($template),
                    'formatted_length' => strlen($processedPrompt),
                    'variables_replaced' => $this->countVariablesReplaced($template, $formattingContext),
                    'processing_time_ms' => $metadata['processing_time_ms'] ?? 0,
                ],
            ];
        }, [
            'strategy' => $promptData['strategy'] ?? 'conversational',
            'engine' => $promptData['engine'] ?? 'simple',
            'template_length' => strlen($promptData['template'] ?? ''),
            'context_variables' => count($promptData['context'] ?? []),
        ]);
    }

    /**
     * Create prompt template
     */
    public function createTemplate(array $templateData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_templates');

        return $this->executeWithTracking('create_template', function () use ($templateData) {
            // Validate template data
            $this->validateTemplateData($templateData);

            $templateId = $this->generateTemplateId();
            $template = [
                'id' => $templateId,
                'tenant_id' => $this->getCurrentTenantId(),
                'user_id' => $this->getCurrentUserId(),
                'name' => $templateData['name'],
                'description' => $templateData['description'] ?? '',
                'template_content' => $templateData['template_content'],
                'default_strategy' => $templateData['default_strategy'] ?? 'conversational',
                'default_engine' => $templateData['default_engine'] ?? 'simple',
                'variables' => $templateData['variables'] ?? [],
                'tags' => $templateData['tags'] ?? [],
                'is_public' => $templateData['is_public'] ?? false,
                'version' => '1.0.0',
                'metadata' => $templateData['metadata'] ?? [],
                'created_at' => now()->toISOString(),
                'updated_at' => now()->toISOString(),
            ];

            // Store template
            $this->storeTemplate($template);

            $this->logActivity('Template created', [
                'template_id' => $templateId,
                'template_name' => $template['name'],
                'strategy' => $template['default_strategy'],
                'engine' => $template['default_engine'],
                'variables_count' => count($template['variables']),
            ]);

            return [
                'template_id' => $templateId,
                'template_name' => $template['name'],
                'status' => 'created',
                'version' => $template['version'],
            ];
        }, [
            'template_name' => $templateData['name'],
            'strategy' => $templateData['default_strategy'] ?? 'conversational',
            'variables_count' => count($templateData['variables'] ?? []),
        ]);
    }

    /**
     * Update template
     */
    public function updateTemplate(string $templateId, array $updateData): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();
        $this->requirePermission('manage_templates');

        return $this->executeWithTracking('update_template', function () use ($templateId, $updateData) {
            $template = $this->getTemplate($templateId);

            // Update template data
            foreach ($updateData as $key => $value) {
                if (in_array($key, ['name', 'description', 'template_content', 'default_strategy', 'default_engine', 'variables', 'tags', 'metadata'])) {
                    $template[$key] = $value;
                }
            }

            // Increment version
            $template['version'] = $this->incrementVersion($template['version']);
            $template['updated_at'] = now()->toISOString();

            // Store updated template
            $this->storeTemplate($template);

            $this->logActivity('Template updated', [
                'template_id' => $templateId,
                'template_name' => $template['name'],
                'updated_fields' => array_keys($updateData),
                'new_version' => $template['version'],
            ]);

            return [
                'template_id' => $templateId,
                'template_name' => $template['name'],
                'status' => 'updated',
                'version' => $template['version'],
                'updated_fields' => array_keys($updateData),
            ];
        }, [
            'template_id' => $templateId,
            'update_fields' => array_keys($updateData),
        ]);
    }

    /**
     * Get available templates
     */
    public function getAvailableTemplates(array $filters = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('get_available_templates', function () use ($filters) {
            $templates = $this->getAllTemplates();

            // Apply filters
            if (isset($filters['name'])) {
                $templates = array_filter($templates, fn($t) => str_contains(strtolower($t['name']), strtolower($filters['name'])));
            }

            if (isset($filters['strategy'])) {
                $templates = array_filter($templates, fn($t) => $t['default_strategy'] === $filters['strategy']);
            }

            if (isset($filters['engine'])) {
                $templates = array_filter($templates, fn($t) => $t['default_engine'] === $filters['engine']);
            }

            if (isset($filters['tags'])) {
                $templates = array_filter($templates, function ($t) use ($filters) {
                    return !empty(array_intersect($t['tags'], $filters['tags']));
                });
            }

            // Sort by name
            usort($templates, fn($a, $b) => strcmp($a['name'], $b['name']));

            return [
                'templates' => array_map(function ($template) {
                    return [
                        'id' => $template['id'],
                        'name' => $template['name'],
                        'description' => $template['description'],
                        'default_strategy' => $template['default_strategy'],
                        'default_engine' => $template['default_engine'],
                        'variables' => $template['variables'],
                        'tags' => $template['tags'],
                        'version' => $template['version'],
                        'created_at' => $template['created_at'],
                        'updated_at' => $template['updated_at'],
                    ];
                }, $templates),
                'total_count' => count($templates),
                'strategies_available' => array_keys($this->formattingStrategies),
                'engines_available' => array_keys($this->templateEngines),
            ];
        }, [
            'filters_applied' => count($filters),
        ]);
    }

    /**
     * Format with template
     */
    public function formatWithTemplate(string $templateId, array $context, array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('format_with_template', function () use ($templateId, $context, $options) {
            $template = $this->getTemplate($templateId);

            // Prepare prompt data
            $promptData = [
                'template' => $template['template_content'],
                'context' => $context,
                'strategy' => $options['strategy'] ?? $template['default_strategy'],
                'engine' => $options['engine'] ?? $template['default_engine'],
                'options' => $options,
            ];

            // Format prompt
            $result = $this->formatPrompt($promptData);

            // Add template information
            $result['template_info'] = [
                'id' => $template['id'],
                'name' => $template['name'],
                'version' => $template['version'],
                'variables_defined' => $template['variables'],
            ];

            return $result;
        }, [
            'template_id' => $templateId,
            'context_variables' => count($context),
            'options_provided' => count($options),
        ]);
    }

    /**
     * Analyze prompt effectiveness
     */
    public function analyzePromptEffectiveness(string $prompt, array $context = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('analyze_prompt_effectiveness', function () use ($prompt, $context) {
            $analysis = [
                'prompt' => $prompt,
                'length_analysis' => $this->analyzePromptLength($prompt),
                'clarity_score' => $this->calculateClarityScore($prompt),
                'specificity_score' => $this->calculateSpecificityScore($prompt),
                'context_utilization' => $this->analyzeContextUtilization($prompt, $context),
                'structure_analysis' => $this->analyzePromptStructure($prompt),
                'improvement_suggestions' => $this->generateImprovementSuggestions($prompt),
                'effectiveness_score' => 0, // Will be calculated
                'analysis_timestamp' => now()->toISOString(),
            ];

            // Calculate overall effectiveness score
            $analysis['effectiveness_score'] = $this->calculateEffectivenessScore($analysis);

            return $analysis;
        }, [
            'prompt_length' => strlen($prompt),
            'context_variables' => count($context),
        ]);
    }

    /**
     * Generate prompt variations
     */
    public function generatePromptVariations(string $basePrompt, array $options = []): array
    {
        $this->requireEnabled();
        $this->validateTenantContext();

        return $this->executeWithTracking('generate_prompt_variations', function () use ($basePrompt, $options) {
            $variationCount = $options['variation_count'] ?? 3;
            $strategies = $options['strategies'] ?? ['conversational', 'technical', 'creative'];
            $context = $options['context'] ?? [];

            $variations = [];

            foreach ($strategies as $strategy) {
                for ($i = 0; $i < $variationCount; $i++) {
                    $variation = $this->generateVariation($basePrompt, $strategy, $context, $i);
                    $variations[] = [
                        'variation_id' => uniqid('var_'),
                        'strategy' => $strategy,
                        'variation_index' => $i,
                        'prompt' => $variation,
                        'effectiveness_prediction' => $this->predictEffectiveness($variation),
                        'generated_at' => now()->toISOString(),
                    ];
                }
            }

            // Sort by predicted effectiveness
            usort($variations, fn($a, $b) => $b['effectiveness_prediction'] <=> $a['effectiveness_prediction']);

            return [
                'base_prompt' => $basePrompt,
                'variations' => $variations,
                'total_variations' => count($variations),
                'strategies_used' => $strategies,
                'generation_metadata' => [
                    'variation_count_per_strategy' => $variationCount,
                    'total_strategies' => count($strategies),
                ],
            ];
        }, [
            'base_prompt_length' => strlen($basePrompt),
            'variation_count' => $options['variation_count'] ?? 3,
            'strategies_count' => count($options['strategies'] ?? []),
        ]);
    }

    /**
     * Validate formatting inputs
     */
    protected function validateFormattingInputs(string $template, string $strategy, string $engine): void
    {
        if (empty($template)) {
            throw new \InvalidArgumentException('Template cannot be empty');
        }

        if (!isset($this->formattingStrategies[$strategy])) {
            throw new \InvalidArgumentException("Unsupported formatting strategy: {$strategy}");
        }

        if (!isset($this->templateEngines[$engine])) {
            throw new \InvalidArgumentException("Unsupported template engine: {$engine}");
        }
    }

    /**
     * Prepare formatting context
     */
    protected function prepareFormattingContext(array $context, string $strategy, array $options): array
    {
        $formattingContext = $context;

        // Add system variables
        $formattingContext['_system'] = [
            'timestamp' => now()->toISOString(),
            'tenant_id' => $this->getCurrentTenantId(),
            'user_id' => $this->getCurrentUserId(),
            'strategy' => $strategy,
        ];

        // Add strategy-specific context
        $formattingContext = $this->addStrategyContext($formattingContext, $strategy);

        // Add option-based context
        if (isset($options['additional_context'])) {
            $formattingContext = array_merge($formattingContext, $options['additional_context']);
        }

        return $formattingContext;
    }

    /**
     * Apply formatting strategy
     */
    protected function applyFormattingStrategy(string $template, string $strategy, array $context): string
    {
        $method = $this->formattingStrategies[$strategy];
        return $this->$method($template, $context);
    }

    /**
     * Process template with engine
     */
    protected function processTemplate(string $template, string $engine, array $context): string
    {
        $method = $this->templateEngines[$engine];
        return $this->$method($template, $context);
    }

    /**
     * Format conversational style
     */
    protected function formatConversational(string $template, array $context): string
    {
        // Add conversational elements
        $conversationalPrefixes = [
            "Let's talk about",
            "I'd like to discuss",
            "Can we explore",
            "Let me help you with",
        ];

        $conversationalSuffixes = [
            "What are your thoughts?",
            "How does this sound to you?",
            "Does this make sense?",
            "What would you like to know more about?",
        ];

        // Add random conversational elements if not present
        if (!str_contains($template, '?') && !str_contains(strtolower($template), 'please')) {
            $prefix = $conversationalPrefixes[array_rand($conversationalPrefixes)];
            $template = $prefix . ' ' . lcfirst($template);
        }

        return $template;
    }

    /**
     * Format technical style
     */
    protected function formatTechnical(string $template, array $context): string
    {
        // Add technical precision
        $technicalPrefixes = [
            "Technical analysis:",
            "System specification:",
            "Implementation details:",
            "Configuration parameters:",
        ];

        // Make language more precise and formal
        $template = str_replace(
            ['might', 'could', 'maybe', 'perhaps'],
            ['will', 'shall', 'specifically', 'precisely'],
            $template
        );

        return $template;
    }

    /**
     * Format creative style
     */
    protected function formatCreative(string $template, array $context): string
    {
        // Add creative elements
        $creativePrefixes = [
            "Imagine if",
            "Picture this:",
            "Let's envision",
            "What if we could",
        ];

        $creativeEnhancements = [
            'think' => 'envision',
            'make' => 'craft',
            'do' => 'create',
            'use' => 'harness',
        ];

        foreach ($creativeEnhancements as $old => $new) {
            $template = str_replace($old, $new, $template);
        }

        return $template;
    }

    /**
     * Format analytical style
     */
    protected function formatAnalytical(string $template, array $context): string
    {
        // Add analytical structure
        $analyticalPrefixes = [
            "Analysis indicates:",
            "Data suggests:",
            "Examination reveals:",
            "Investigation shows:",
        ];

        // Add analytical language
        $analyticalTerms = [
            'shows' => 'demonstrates',
            'says' => 'indicates',
            'means' => 'signifies',
            'because' => 'due to the fact that',
        ];

        foreach ($analyticalTerms as $old => $new) {
            $template = str_replace($old, $new, $template);
        }

        return $template;
    }

    /**
     * Format instructional style
     */
    protected function formatInstructional(string $template, array $context): string
    {
        // Add step-by-step structure
        if (!str_contains($template, 'step') && !str_contains($template, 'first')) {
            $template = "Step 1: " . $template;
        }

        // Add instructional language
        $instructionalTerms = [
            'do' => 'execute',
            'make' => 'create',
            'get' => 'obtain',
            'find' => 'locate',
        ];

        foreach ($instructionalTerms as $old => $new) {
            $template = str_replace($old, $new, $template);
        }

        return $template;
    }

    /**
     * Format professional style
     */
    protected function formatProfessional(string $template, array $context): string
    {
        // Add professional language
        $professionalTerms = [
            'help' => 'assist',
            'fix' => 'resolve',
            'problem' => 'challenge',
            'issue' => 'matter',
        ];

        foreach ($professionalTerms as $old => $new) {
            $template = str_replace($old, $new, $template);
        }

        return $template;
    }

    /**
     * Format casual style
     */
    protected function formatCasual(string $template, array $context): string
    {
        // Add casual language
        $casualTerms = [
            'assist' => 'help',
            'utilize' => 'use',
            'demonstrate' => 'show',
            'facilitate' => 'make easier',
        ];

        foreach ($casualTerms as $old => $new) {
            $template = str_replace($old, $new, $template);
        }

        return $template;
    }

    /**
     * Format academic style
     */
    protected function formatAcademic(string $template, array $context): string
    {
        // Add academic language
        $academicTerms = [
            'show' => 'demonstrate',
            'prove' => 'substantiate',
            'think' => 'hypothesize',
            'find' => 'ascertain',
        ];

        foreach ($academicTerms as $old => $new) {
            $template = str_replace($old, $new, $template);
        }

        return $template;
    }

    /**
     * Process simple template
     */
    protected function processSimpleTemplate(string $template, array $context): string
    {
        // Simple variable replacement with {{variable}} syntax
        return preg_replace_callback('/\{\{(\w+)\}\}/', function ($matches) use ($context) {
            $variable = $matches[1];
            return $context[$variable] ?? $matches[0];
        }, $template);
    }

    /**
     * Process Mustache template
     */
    protected function processMustacheTemplate(string $template, array $context): string
    {
        // Basic Mustache-like processing
        // In production, this would use actual Mustache library
        return $this->processSimpleTemplate($template, $context);
    }

    /**
     * Process Twig template
     */
    protected function processTwigTemplate(string $template, array $context): string
    {
        // Basic Twig-like processing
        // In production, this would use actual Twig library
        return preg_replace_callback('/\{\{\s*(\w+)\s*\}\}/', function ($matches) use ($context) {
            $variable = $matches[1];
            return $context[$variable] ?? $matches[0];
        }, $template);
    }

    /**
     * Process Blade template
     */
    protected function processBladeTemplate(string $template, array $context): string
    {
        // Basic Blade-like processing
        // In production, this would use actual Blade engine
        return preg_replace_callback('/\{\{\s*\$(\w+)\s*\}\}/', function ($matches) use ($context) {
            $variable = $matches[1];
            return $context[$variable] ?? $matches[0];
        }, $template);
    }

    /**
     * Process Handlebars template
     */
    protected function processHandlebarsTemplate(string $template, array $context): string
    {
        // Basic Handlebars-like processing
        // In production, this would use actual Handlebars library
        return $this->processSimpleTemplate($template, $context);
    }

    /**
     * Apply post-processing
     */
    protected function applyPostProcessing(string $prompt, array $options): string
    {
        // Trim whitespace
        $prompt = trim($prompt);

        // Apply length limits
        if (isset($options['max_length'])) {
            $prompt = substr($prompt, 0, $options['max_length']);
        }

        // Apply text transformations
        if (isset($options['case'])) {
            switch ($options['case']) {
                case 'upper':
                    $prompt = strtoupper($prompt);
                    break;
                case 'lower':
                    $prompt = strtolower($prompt);
                    break;
                case 'title':
                    $prompt = ucwords($prompt);
                    break;
            }
        }

        // Remove extra spaces
        $prompt = preg_replace('/\s+/', ' ', $prompt);

        return $prompt;
    }

    /**
     * Generate formatting metadata
     */
    protected function generateFormattingMetadata(string $template, string $strategy, string $engine, array $context): array
    {
        $startTime = microtime(true);

        return [
            'processing_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            'template_complexity' => $this->calculateTemplateComplexity($template),
            'context_size' => count($context),
            'strategy_applied' => $strategy,
            'engine_used' => $engine,
            'variables_detected' => $this->detectVariables($template),
            'formatting_timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Count variables replaced
     */
    protected function countVariablesReplaced(string $template, array $context): int
    {
        $variables = $this->detectVariables($template);
        $replaced = 0;

        foreach ($variables as $variable) {
            if (isset($context[$variable])) {
                $replaced++;
            }
        }

        return $replaced;
    }

    /**
     * Detect variables in template
     */
    protected function detectVariables(string $template): array
    {
        preg_match_all('/\{\{(\w+)\}\}/', $template, $matches);
        return array_unique($matches[1]);
    }

    /**
     * Calculate template complexity
     */
    protected function calculateTemplateComplexity(string $template): string
    {
        $variableCount = count($this->detectVariables($template));
        $length = strlen($template);

        if ($variableCount > 10 || $length > 1000) {
            return 'high';
        } elseif ($variableCount > 5 || $length > 500) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Add strategy-specific context
     */
    protected function addStrategyContext(array $context, string $strategy): array
    {
        switch ($strategy) {
            case 'technical':
                $context['_technical'] = [
                    'precision_level' => 'high',
                    'terminology' => 'formal',
                    'detail_level' => 'comprehensive',
                ];
                break;

            case 'creative':
                $context['_creative'] = [
                    'imagination_level' => 'high',
                    'language_style' => 'expressive',
                    'metaphor_usage' => 'encouraged',
                ];
                break;

            case 'conversational':
                $context['_conversational'] = [
                    'tone' => 'friendly',
                    'formality' => 'casual',
                    'engagement' => 'interactive',
                ];
                break;
        }

        return $context;
    }

    /**
     * Validate template data
     */
    protected function validateTemplateData(array $templateData): void
    {
        $requiredFields = ['name', 'template_content'];
        foreach ($requiredFields as $field) {
            if (!isset($templateData[$field]) || empty($templateData[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        if (isset($templateData['default_strategy']) && !isset($this->formattingStrategies[$templateData['default_strategy']])) {
            throw new \InvalidArgumentException("Invalid default strategy: {$templateData['default_strategy']}");
        }

        if (isset($templateData['default_engine']) && !isset($this->templateEngines[$templateData['default_engine']])) {
            throw new \InvalidArgumentException("Invalid default engine: {$templateData['default_engine']}");
        }
    }

    /**
     * Generate template ID
     */
    protected function generateTemplateId(): string
    {
        return 'tpl_' . strtoupper(substr(md5(uniqid()), 0, 8)) . '_' . time();
    }

    /**
     * Store template
     */
    protected function storeTemplate(array $template): void
    {
        $cacheKey = "template:{$template['tenant_id']}:{$template['id']}";
        $this->putToCache($cacheKey, $template, 3600); // 1 hour TTL

        // Also store in tenant template list
        $listKey = "templates:{$template['tenant_id']}";
        $templates = $this->getFromCache($listKey, []);

        // Update or add template
        $found = false;
        foreach ($templates as &$existingTemplate) {
            if ($existingTemplate['id'] === $template['id']) {
                $existingTemplate = $template;
                $found = true;
                break;
            }
        }

        if (!$found) {
            $templates[] = $template;
        }

        $this->putToCache($listKey, $templates, 3600);
    }

    /**
     * Get template
     */
    protected function getTemplate(string $templateId): array
    {
        $cacheKey = "template:{$this->getCurrentTenantId()}:{$templateId}";
        $template = $this->getFromCache($cacheKey);

        if (!$template) {
            throw new \InvalidArgumentException("Template not found: {$templateId}");
        }

        return $template;
    }

    /**
     * Get all templates
     */
    protected function getAllTemplates(): array
    {
        $listKey = "templates:{$this->getCurrentTenantId()}";
        return $this->getFromCache($listKey, []);
    }

    /**
     * Increment version
     */
    protected function incrementVersion(string $version): string
    {
        $parts = explode('.', $version);
        $parts[2] = (int)$parts[2] + 1;
        return implode('.', $parts);
    }

    /**
     * Analyze prompt length
     */
    protected function analyzePromptLength(string $prompt): array
    {
        $length = strlen($prompt);
        $wordCount = str_word_count($prompt);

        return [
            'character_count' => $length,
            'word_count' => $wordCount,
            'length_category' => $this->categorizeLengthCategory($length),
            'optimal_range' => $this->isInOptimalRange($length),
        ];
    }

    /**
     * Calculate clarity score
     */
    protected function calculateClarityScore(string $prompt): float
    {
        $score = 0.5; // Base score

        // Boost for clear structure
        if (str_contains($prompt, '?')) {
            $score += 0.2;
        }

        // Boost for specific language
        $specificWords = ['specific', 'exactly', 'precisely', 'detailed'];
        foreach ($specificWords as $word) {
            if (str_contains(strtolower($prompt), $word)) {
                $score += 0.1;
                break;
            }
        }

        // Penalty for vague language
        $vagueWords = ['maybe', 'perhaps', 'might', 'could'];
        foreach ($vagueWords as $word) {
            if (str_contains(strtolower($prompt), $word)) {
                $score -= 0.1;
                break;
            }
        }

        return min(1.0, max(0.0, $score));
    }

    /**
     * Calculate specificity score
     */
    protected function calculateSpecificityScore(string $prompt): float
    {
        $score = 0.5; // Base score

        // Count specific elements
        $specificElements = [
            'numbers' => preg_match_all('/\d+/', $prompt),
            'proper_nouns' => preg_match_all('/\b[A-Z][a-z]+\b/', $prompt),
            'technical_terms' => $this->countTechnicalTerms($prompt),
        ];

        foreach ($specificElements as $count) {
            if ($count > 0) {
                $score += 0.1;
            }
        }

        return min(1.0, $score);
    }

    /**
     * Analyze context utilization
     */
    protected function analyzeContextUtilization(string $prompt, array $context): array
    {
        $variables = $this->detectVariables($prompt);
        $contextKeys = array_keys($context);

        $utilized = array_intersect($variables, $contextKeys);
        $unutilized = array_diff($contextKeys, $variables);
        $missing = array_diff($variables, $contextKeys);

        return [
            'variables_in_prompt' => $variables,
            'context_keys_available' => $contextKeys,
            'utilized_variables' => $utilized,
            'unutilized_context' => $unutilized,
            'missing_context' => $missing,
            'utilization_rate' => count($contextKeys) > 0 ? count($utilized) / count($contextKeys) : 0,
        ];
    }

    /**
     * Analyze prompt structure
     */
    protected function analyzePromptStructure(string $prompt): array
    {
        return [
            'has_question' => str_contains($prompt, '?'),
            'has_instruction' => $this->hasInstructionalLanguage($prompt),
            'has_context' => $this->hasContextualInformation($prompt),
            'sentence_count' => substr_count($prompt, '.') + substr_count($prompt, '!') + substr_count($prompt, '?'),
            'paragraph_count' => substr_count($prompt, "\n\n") + 1,
            'structure_score' => $this->calculateStructureScore($prompt),
        ];
    }

    /**
     * Generate improvement suggestions
     */
    protected function generateImprovementSuggestions(string $prompt): array
    {
        $suggestions = [];

        if (strlen($prompt) < 50) {
            $suggestions[] = 'Consider adding more detail to make the prompt more specific';
        }

        if (!str_contains($prompt, '?') && !$this->hasInstructionalLanguage($prompt)) {
            $suggestions[] = 'Add a clear question or instruction to guide the response';
        }

        if ($this->countVariables($prompt) === 0) {
            $suggestions[] = 'Consider using variables to make the prompt more dynamic';
        }

        if ($this->hasVagueLanguage($prompt)) {
            $suggestions[] = 'Replace vague language with more specific terms';
        }

        return $suggestions;
    }

    /**
     * Calculate effectiveness score
     */
    protected function calculateEffectivenessScore(array $analysis): float
    {
        $weights = [
            'clarity_score' => 0.3,
            'specificity_score' => 0.3,
            'structure_score' => 0.2,
            'context_utilization_rate' => 0.2,
        ];

        $score = 0;
        $score += $analysis['clarity_score'] * $weights['clarity_score'];
        $score += $analysis['specificity_score'] * $weights['specificity_score'];
        $score += $analysis['structure_analysis']['structure_score'] * $weights['structure_score'];
        $score += $analysis['context_utilization']['utilization_rate'] * $weights['context_utilization_rate'];

        return round($score, 2);
    }

    /**
     * Generate variation
     */
    protected function generateVariation(string $basePrompt, string $strategy, array $context, int $index): string
    {
        // Apply strategy formatting
        $variation = $this->applyFormattingStrategy($basePrompt, $strategy, $context);

        // Add variation-specific modifications
        switch ($index) {
            case 1:
                $variation = $this->addDetailLevel($variation, 'high');
                break;
            case 2:
                $variation = $this->addDetailLevel($variation, 'low');
                break;
            default:
                // Keep as is
                break;
        }

        return $variation;
    }

    /**
     * Predict effectiveness
     */
    protected function predictEffectiveness(string $prompt): float
    {
        // Simple effectiveness prediction based on prompt characteristics
        $score = 0.5;

        if (strlen($prompt) > 100 && strlen($prompt) < 500) {
            $score += 0.2;
        }

        if (str_contains($prompt, '?')) {
            $score += 0.1;
        }

        if ($this->countVariables($prompt) > 0) {
            $score += 0.1;
        }

        return min(1.0, $score);
    }

    /**
     * Helper methods
     */
    protected function categorizeLengthCategory(int $length): string
    {
        if ($length < 50) return 'short';
        if ($length < 200) return 'medium';
        if ($length < 500) return 'long';
        return 'very_long';
    }

    protected function isInOptimalRange(int $length): bool
    {
        return $length >= 100 && $length <= 300;
    }

    protected function countTechnicalTerms(string $prompt): int
    {
        $technicalTerms = ['API', 'database', 'algorithm', 'function', 'variable', 'parameter'];
        $count = 0;
        foreach ($technicalTerms as $term) {
            if (str_contains(strtolower($prompt), strtolower($term))) {
                $count++;
            }
        }
        return $count;
    }

    protected function hasInstructionalLanguage(string $prompt): bool
    {
        $instructionalWords = ['please', 'create', 'generate', 'explain', 'describe', 'analyze'];
        foreach ($instructionalWords as $word) {
            if (str_contains(strtolower($prompt), $word)) {
                return true;
            }
        }
        return false;
    }

    protected function hasContextualInformation(string $prompt): bool
    {
        return count($this->detectVariables($prompt)) > 0;
    }

    protected function calculateStructureScore(string $prompt): float
    {
        $score = 0.5;

        if ($this->hasInstructionalLanguage($prompt)) $score += 0.2;
        if (str_contains($prompt, '?')) $score += 0.2;
        if ($this->hasContextualInformation($prompt)) $score += 0.1;

        return min(1.0, $score);
    }

    protected function countVariables(string $prompt): int
    {
        return count($this->detectVariables($prompt));
    }

    protected function hasVagueLanguage(string $prompt): bool
    {
        $vagueWords = ['maybe', 'perhaps', 'might', 'could', 'something', 'anything'];
        foreach ($vagueWords as $word) {
            if (str_contains(strtolower($prompt), $word)) {
                return true;
            }
        }
        return false;
    }

    protected function addDetailLevel(string $prompt, string $level): string
    {
        switch ($level) {
            case 'high':
                return "Please provide a detailed and comprehensive response to: " . $prompt;
            case 'low':
                return "Please provide a brief response to: " . $prompt;
            default:
                return $prompt;
        }
    }
}
