<?php

namespace App\Models;

use App\Enums\UserRole;
use App\Enums\UserStatus;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'name',
        'email',
        'password',
        'avatar_url',
        'phone',
        'timezone',
        'language',
        'is_active',
        'password_changed_at',
        'role',
        'permissions',
        'ai_provider_access',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'api_key',
        'api_rate_limit_per_minute',
        'api_access_enabled',
        'tokens_used_current_month',
        'tokens_limit_per_month',
        'chat_sessions_count',
        'documents_uploaded_count',
        'websocket_enabled',
        'last_websocket_connection',
        'websocket_session_id',
        'last_login_at',
        'last_login_ip',
        'last_login_user_agent',
        'last_activity_at',
        'preferences',
        'ai_preferences',
        'notifications_enabled',
        'notification_channels',
        'status',
        'metadata',
        'notes'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
        'password_changed_at' => 'datetime',
        'role' => UserRole::class,
        'permissions' => 'array',
        'ai_provider_access' => 'array',
        'two_factor_enabled' => 'boolean',
        'two_factor_recovery_codes' => 'array',
        'two_factor_confirmed_at' => 'datetime',
        'api_access_enabled' => 'boolean',
        'tokens_used_current_month' => 'decimal:0',
        'tokens_limit_per_month' => 'decimal:0',
        'chat_sessions_count' => 'integer',
        'documents_uploaded_count' => 'integer',
        'websocket_enabled' => 'boolean',
        'last_websocket_connection' => 'datetime',
        'last_login_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'preferences' => 'array',
        'ai_preferences' => 'array',
        'notifications_enabled' => 'boolean',
        'notification_channels' => 'array',
        'status' => UserStatus::class,
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be appended to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'is_owner',
        'is_admin',
        'can_manage_users',
        'effective_permissions'
    ];

    /**
     * Get the tenant that owns the user.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the chat histories for the user.
     */
    public function chatHistories(): HasMany
    {
        return $this->hasMany(ChatHistory::class, 'user_id');
    }

    /**
     * Get the WebSocket sessions for the user.
     */
    public function webSocketSessions(): HasMany
    {
        return $this->hasMany(WebSocketSession::class, 'user_id');
    }

    /**
     * Get the documents created by the user.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class, 'created_by_user_id');
    }

    /**
     * Get the tools created by the user.
     */
    public function tools(): HasMany
    {
        return $this->hasMany(Tool::class, 'created_by_user_id');
    }

    /**
     * Get the workflows created by the user.
     */
    public function workflows(): HasMany
    {
        return $this->hasMany(Workflow::class, 'created_by_user_id');
    }

    /**
     * Check if user is owner
     */
    public function getIsOwnerAttribute(): bool
    {
        return $this->role === UserRole::OWNER;
    }

    /**
     * Check if user is admin or owner
     */
    public function getIsAdminAttribute(): bool
    {
        return in_array($this->role, [UserRole::OWNER, UserRole::ADMIN]);
    }

    /**
     * Check if user can manage other users
     */
    public function getCanManageUsersAttribute(): bool
    {
        return $this->role->canManageUsers();
    }

    /**
     * Get effective permissions for the user
     */
    public function getEffectivePermissionsAttribute(): array
    {
        $permissions = [];

        // Get role-based permissions
        $rolePermissions = $this->getRolePermissions();
        $permissions = array_merge($permissions, $rolePermissions);

        // Add user-specific permissions
        $userPermissions = $this->permissions ?? [];
        $permissions = array_merge($permissions, $userPermissions);

        // Add tenant-specific permissions (if tenant has user_permissions field)
        if ($this->tenant && isset($this->tenant->user_permissions) && is_array($this->tenant->user_permissions)) {
            $tenantPermissions = $this->tenant->user_permissions[$this->id] ?? [];
            $permissions = array_merge($permissions, $tenantPermissions);
        }

        // Note: revoked_permissions functionality removed as these fields don't exist in current migration
        // This can be re-added when the fields are added to the migration

        return array_unique($permissions);
    }

    /**
     * Get role-based permissions
     */
    public function getRolePermissions(): array
    {
        $rolePermissions = [
            'owner' => [
                'tenant:manage', 'users:manage', 'settings:manage', 'billing:manage',
                'ai_providers:manage', 'tools:manage', 'workflows:manage',
                'documents:manage', 'chat:access', 'monitoring:access'
            ],
            'admin' => [
                'users:manage', 'settings:view', 'ai_providers:manage',
                'tools:manage', 'workflows:manage', 'documents:manage',
                'chat:access', 'monitoring:access'
            ],
            'manager' => [
                'users:view', 'ai_providers:view', 'tools:manage',
                'workflows:manage', 'documents:manage', 'chat:access'
            ],
            'user' => [
                'tools:use', 'workflows:use', 'documents:create',
                'documents:view', 'chat:access'
            ],
            'viewer' => [
                'tools:view', 'workflows:view', 'documents:view', 'chat:view'
            ]
        ];

        return $rolePermissions[$this->role->value] ?? [];
    }

    /**
     * Check if user has a specific permission
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->effective_permissions);
    }

    /**
     * Check if user has any of the given permissions
     */
    public function hasAnyPermission(array $permissions): bool
    {
        return !empty(array_intersect($permissions, $this->effective_permissions));
    }

    /**
     * Check if user has all of the given permissions
     */
    public function hasAllPermissions(array $permissions): bool
    {
        return empty(array_diff($permissions, $this->effective_permissions));
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->role->value === $role;
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role->value, $roles);
    }

    /**
     * Check if user is locked (placeholder - locked_until field not in current migration)
     */
    public function isLocked(): bool
    {
        // TODO: Add locked_until field to migration if account locking is needed
        return false;
    }

    /**
     * Lock the user account (placeholder - locked_until field not in current migration)
     */
    public function lock(int $minutes = 30): void
    {
        // TODO: Add locked_until field to migration if account locking is needed
        // For now, just deactivate the user
        $this->update(['is_active' => false]);
    }

    /**
     * Unlock the user account (placeholder - locked_until field not in current migration)
     */
    public function unlock(): void
    {
        // TODO: Add locked_until and failed_login_attempts fields to migration if account locking is needed
        // For now, just activate the user
        $this->update(['is_active' => true]);
    }

    /**
     * Increment failed login attempts (placeholder - failed_login_attempts field not in current migration)
     */
    public function incrementFailedLoginAttempts(): void
    {
        // TODO: Add failed_login_attempts field to migration if this functionality is needed
        // For now, do nothing
    }

    /**
     * Reset failed login attempts (placeholder - failed_login_attempts field not in current migration)
     */
    public function resetFailedLoginAttempts(): void
    {
        // TODO: Add failed_login_attempts and locked_until fields to migration if this functionality is needed
        // For now, do nothing
    }

    /**
     * Update last activity
     */
    public function updateLastActivity(string $ipAddress = null, string $userAgent = null): void
    {
        $this->update([
            'last_activity_at' => now(),
            'last_login_ip' => $ipAddress,
            'last_login_user_agent' => $userAgent
        ]);
    }

    /**
     * Create API token with specific abilities
     */
    public function createApiToken(string $name, array $abilities = ['*']): string
    {
        return $this->createToken($name, $abilities)->plainTextToken;
    }

    /**
     * Create WebSocket token
     */
    public function createWebSocketToken(string $name = 'websocket'): string
    {
        return $this->createToken($name, [
            'websocket:connect',
            'chat:access',
            'chat:sessions:view',
            'chat:messages:send',
            'chat:messages:view'
        ])->plainTextToken;
    }

    /**
     * Scope query to active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope query to users with specific role
     */
    public function scopeWithRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope query to users in specific tenant
     */
    public function scopeInTenant($query, string $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }
}
